import { ref, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ExamStatusEnum } from '@crm/exam-types';
import { useTrackableFlow } from '@/store/track/use-trackable-flow';

export function useEvaluationEvents() {
    const $route = useRoute();
    const $router = useRouter();
    const { seqId, examId } = $route.params;
    const lastQuestionId = ref('');

    const { execute: executeSubmitPaper } = useTrackableFlow('提交测评试卷', async (ctx, { commitType }: { commitType: 1 | 2 | 3 }) => {
        const submitTypeMap = {
            1: 'click_paper_submit',
            2: 'paper_auto_submit',
            3: 'paper_auto_submit', // 假设超时也用这个 remarks
        };
        const remarks = submitTypeMap[commitType];
        ctx.logStep('开始提交测评试卷API调用', { status: 'success', commitType: commitType, remarks: remarks, encryptExamId: examId });
        // @ts-ignore
        const { code, data, message } = await Invoke.common.postEvaluationPaperCommit({
            encryptExamId: examId,
            remarks,
            commitType,
        });
        ctx.logStep('提交测评试卷API调用完成', { status: 'success', code: code, data: data, message: message });
        if (code === 0) {
            ctx.logStep('测评试卷提交成功，跳转至状态页');
            $router.replace(`/status/${seqId}?status=${ExamStatusEnum.已交卷}&text=您已完成该场考试，剩余${data.pendingCount}场待完成`);
        } else {
            ctx.logStep('测评试卷提交失败', { status: 'failure', message: message });
            // 可以考虑抛出错误或进行其他错误处理
        }
    });

    function onQuestionChange(data: { type: string; currentQuestionId: string }) {
        const fromId = lastQuestionId.value || '';
        const toId = data.type === 'end' ? '' : data.currentQuestionId;
        if (fromId || toId) {
            // @ts-ignore
            postLog('eval_question_change', {
                encExamId: examId as string,
                p2: '',
                p3: lastQuestionId.value,
                p4: toId,
            });
        }

        nextTick(() => {
            lastQuestionId.value = toId;
        });
    }

    return {
        onSubmitPaper: executeSubmitPaper,
        onQuestionChange,
        lastQuestionId, // 也导出 lastQuestionId 供外部潜在需要
    };
}
