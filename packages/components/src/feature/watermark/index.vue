<template>
    <div v-bind="$attrs" ref="containerRef" style="position: relative">
        <slot />
    </div>
</template>

<script setup lang="ts">
import { Watermark } from '@boss/design';
import { onMounted, onUnmounted, ref, watch, PropType } from 'vue';

defineOptions({
    name: 'WaterMark',
});
const props = defineProps({
    content: {
        type: Array as PropType<string | string[]>,
        default: '张三-100872',
    },

    // { label: '需要水印', value: 1 },
    // { label: '不需要水印', value: 2 }
    waterMarkConfig: {
        type: Number,
        default: 2,
    },
});
const containerRef = ref();
let destoryWaterMark = () => {};
let unWatch = () => {};
function startWatch() {
    unWatch = watch(
        () => props.waterMarkConfig,
        (value) => {
            if (value === 1) {
                const { destroy } = Watermark.partInit({
                    content: props.content,
                    popupContainer: containerRef.value,

                    opacity: 0.1,
                    font: {
                        color: 'rgba(0,0,0,1)',
                        fontSize: 13,
                        fontFamily: 'FZLT-XianHJ',
                    },
                    rotate: -20,
                    zIndex: 10,
                });
                destoryWaterMark = destroy;
            } else {
                destoryWaterMark();
            }
        },
        { immediate: true },
    );
}
onMounted(startWatch);
onUnmounted(unWatch);
</script>
