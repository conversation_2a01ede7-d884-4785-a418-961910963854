import type { AxiosRequestConfig } from 'axios';
import axios, { AxiosError } from 'axios';

interface IRequest {
    id: number;
    cancel: () => void;
    isCanceled: boolean;
    // params: Record<string, any>;
}
interface IRequestSequenceMap {
    [key: string]: {
        timer: number;
        requests: IRequest[];
    };
}
export default function () {
    const requestSequenceMap: IRequestSequenceMap = {};

    function dealRetryRequest(config: AxiosRequestConfig) {
        if (config && config.url === '/wapi/web/exam/answer/save.json') {
            const requestParams: { encryptId: string } = config.data;
            const controller = new AbortController();
            const requestItem: IRequest = {
                id: Date.now(),
                cancel: controller.abort.bind(controller),
                isCanceled: false,
            };
            config.signal = controller.signal;
            const encryptId = requestParams.encryptId;
            if (requestSequenceMap[encryptId]) {
                window.clearTimeout(requestSequenceMap[encryptId].timer);
                requestSequenceMap[encryptId].timer = -1;
            } else {
                requestSequenceMap[encryptId] = {
                    timer: -1,
                    requests: [],
                };
            }
            for (let i = 0; i < requestSequenceMap[encryptId].requests.length; i++) {
                const requestItem = requestSequenceMap[encryptId].requests[i];
                if (!requestItem.isCanceled) {
                    requestItem.cancel();
                    requestItem.isCanceled = true;
                }
            }
            requestSequenceMap[encryptId].requests = requestSequenceMap[encryptId].requests.filter((x: any) => !x.isCanceled);
            requestSequenceMap[encryptId].requests.push(requestItem);
        }
    }
    function dealRetryError(error: any) {
        if (!axios.isCancel(error) && error.config?.url === '/wapi/web/exam/answer/save.json' && window.location.href.includes('/monitor/')) {
            if (error.config) {
                const requestParams = JSON.parse(error.config.data);
                requestSequenceMap[requestParams.encryptId].timer = window.setTimeout(() => {
                    Invoke.exam.postAnswerSave(requestParams);
                }, 3000);
            }
        }
    }

    return {
        dealRetryRequest,
        dealRetryError,
    };
}
