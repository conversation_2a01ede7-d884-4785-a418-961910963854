export const OPTION_LIST_MAP: any = {
    1: {
        status: 0,
        theme: 'green-deep',
        shortcutKeys: 1,
        color: '#F8FFDC',
        strokeColor: '#C3FE9C',
    },
    2: {
        status: 0,
        theme: 'red-deep',
        shortcutKeys: 2,
        color: '#FFF9DC',
        strokeColor: '#FF94A7',
    },
};

export function getRandomNumbers(numbers: string | any[], count: number) {
    // 首先，确保选择的数字数量不大于数组长度
    if (count > numbers.length) {
        throw new Error('选择的数字数量不能大于数组长度');
    }

    // 创建一个结果数组
    const result: any[] = [];

    // 当结果数组的长度小于要选择的数量时
    while (result.length < count) {
        // 随机选择一个索引
        const index = Math.floor(Math.random() * numbers.length);

        // 如果这个索引对应的数字还没有被选中，则加入结果数组
        if (!result.includes(numbers[index])) {
            result.push(numbers[index]);
        }
    }

    return result;
}

export function getRandomElement(arr: string | any[]) {
    // 确保数组不为空
    if (arr.length === 0) {
        return '';
    }

    // 生成一个0到arr.length-1之间的随机索引
    const index = Math.floor(Math.random() * arr.length);

    // 返回该索引对应的数组元素
    return arr[index];
}

export function delay(s: number) {
    return new Promise((resolve) => setTimeout(resolve, s * 1000));
}

// 生成数组
export const generateArray = (length: number, start: number) => Array.from({ length }, (_, i) => start + i);
export const resultMap = {
    1: 'success',
    0: 'failure',
};
