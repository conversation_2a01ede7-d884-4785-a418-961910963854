{"extends": "./tsconfig.base.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["src/*"], "@fe-kaoshi-web/utils": ["./packages/utils/src"], "@fe-kaoshi-web/components": ["./packages/components/src"], "@fe-kaoshi-web/directives": ["./packages/directives/src"], "@fe-kaoshi-web/hooks": ["./packages/hooks/src"], "@fe-kaoshi-web/services": ["./packages/services/src"], "@fe-kaoshi-web/types": ["./packages/types/src"], "@fe-kaoshi-web/constants": ["./packages/constants/src"]}}, "references": [{"path": "./apps/h5"}, {"path": "./apps/main"}, {"path": "./packages/utils"}, {"path": "./packages/components"}, {"path": "./packages/directives"}, {"path": "./packages/hooks"}, {"path": "./packages/types"}, {"path": "./packages/constants"}], "include": ["vitest.config.ts", "apps/**/*.ts", "apps/**/*.tsx", "apps/**/*.vue", "packages/**/*.ts", "packages/**/*.tsx", "packages/**/*.vue"], "exclude": ["node_modules", "**/dist/**"]}