export const OPTION_LIST = () => [
    {
        content: '参与',
        status: 0,
        id: 1,
        theme: 'green',
        shortcutKeys: 1,
        color: '#F8FFDC',
        strokeColor: '#51A40F',
        shadowFilterConfig: { x: 0, y: 0.92, blur: 0.92, color: '#419C02' },
        img: 'https://img.bosszhipin.com/static/file/2024/qh3ljnjnc21726827009333.svg',
    },
    {
        content: '不参与',
        status: 0,
        id: 0,
        theme: 'orange',
        shortcutKeys: 2,
        color: '#FFF9DC',
        strokeColor: '#FC7313',
        shadowFilterConfig: { x: 0, y: 0.92, blur: 0.92, color: '#E55711' },
        img: 'https://img.bosszhipin.com/static/file/2024/bfoavj7jf31726827009161.svg',
    },
];
export const getRandomNumbers = (numbers: string | any[], count: number) => {
    // 首先，确保选择的数字数量不大于数组长度
    if (count > numbers.length) {
        throw new Error('选择的数字数量不能大于数组长度');
    }

    // 创建一个结果数组
    let result: any[] = [];

    // 当结果数组的长度小于要选择的数量时
    while (result.length < count) {
        // 随机选择一个索引
        let index = Math.floor(Math.random() * numbers.length);

        // 如果这个索引对应的数字还没有被选中，则加入结果数组
        if (!result.includes(numbers[index])) {
            result.push(numbers[index]);
        }
    }

    return result;
};
export const getRandomElement = (arr: string | any[]) => {
    // 确保数组不为空
    if (arr.length === 0) return '';

    // 生成一个0到arr.length-1之间的随机索引
    let index = Math.floor(Math.random() * arr.length);

    // 返回该索引对应的数组元素
    return arr[index];
};
export const delay = (s: number) => {
    return new Promise((resolve) => setTimeout(resolve, s * 1000));
};
// 生成数组
export const generateArray = (length: number, start: number) => Array.from({ length }, (_, i) => start + i);
export const resultMap = {
    1: 'success',
    0: 'failure',
};
