<template>
    <div class="component-wrap">
        <div class="connect-lines-wrap">
            <div class="container" ref="containerRef">
                <template v-for="(blockItem, index) in inputParamList" :key="blockItem.value">
                    <b-popover
                        v-if="showRelationTypePopover"
                        :popupVisible="popupVisibleMap[blockItem.value]"
                        position="right"
                        trigger="click"
                        :showArrow="false"
                        contentClass="connect-lines-popover"
                        @popupVisibleChange="(e) => popupVisibleChange(e, blockItem.value)"
                    >
                        <div
                            ref="startBlockRef"
                            :class="[`block block${index}`, { 'block-actived': currentSelectedId === blockItem.value || popupVisibleMap[blockItem.value] }]"
                            :style="{ width: `${blockWidth}px` }"
                            @click.prevent="handleEndPoint(blockItem)"
                        >
                            {{ blockItem.label }}<SvgIcon v-if="popupVisibleMap[blockItem.value]" name="add" class="icon" :height="12" :width="12"></SvgIcon>
                        </div>
                        <template #content>
                            <p v-for="(item, i) in connectTypeList" :key="item.value" :class="`opt-item item${i}`" @click.prevent="handleConnectType(item.value, blockItem, index)">
                                {{ item.label }}
                            </p>
                        </template>
                    </b-popover>
                    <div
                        v-else
                        ref="startBlockRef"
                        :class="[`block block${index}`, { 'block-actived': currentSelectedId === blockItem.value || popupVisibleMap[blockItem.value] }]"
                        :style="{ width: `${blockWidth}px` }"
                        @click.prevent="handleGetPointBlock(blockItem, index)"
                    >
                        {{ blockItem.label }}<SvgIcon v-if="popupVisibleMap[blockItem.value]" name="add" class="icon" :height="12" :width="12"></SvgIcon>
                    </div>
                </template>
                <svg v-if="draw" ref="svgRef" class="svg-box">
                    <g v-for="(link, index) in links" :key="index" @mouseenter="isHovered = index" @mouseleave="isHovered = null" @click.prevent="handleDelSvgPath(link, index)">
                        <line
                            class="svg-line"
                            v-bind="getSvgInfo(link)"
                            :stroke="arrowFillMap[link.type]"
                            stroke-width="1"
                            :marker-end="`url(#arrow${index})`"
                            :stroke-dasharray="link.type === reverseCode ? '3, 3' : '0, 0'"
                        ></line>
                        <!-- 用于增加热区的透明路径 -->
                        <line v-bind="getSvgInfo(link)" stroke="transparent" stroke-width="10" fill="none"></line>
                    </g>
                </svg>
            </div>
            <div v-if="connectTypeList.length" class="legend">
                <div v-for="item in connectTypeList" class="legend-item">
                    <template v-if="item.value === reverseCode">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="2" viewBox="0 0 14 2">
                            <path
                                transform="matrix(1 0 0 1 1 2)"
                                d="M-1 -2L2.37901 -2L2.37901 0L-1 0L-1 -2ZM3.66827 0L8.33173 0L8.33173 -2L3.66827 -2L3.66827 0ZM13 0L9.62099 0L9.62099 -2L13 -2L13 0Z"
                                fill-rule="evenodd"
                                fill="rgb(240, 106, 57)"
                            />
                        </svg>
                    </template>
                    <span v-else :style="{ background: arrowFillMap[item.value] }"></span>
                    {{ item.label }}
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { useConnectLine } from './useConnectLine';
import { useHotsStore } from '../../store';
defineOptions({
    name: 'RoundLines',
});
const $emit = defineEmits(['update:modelValue', 'onAnswerChange']);
const props = defineProps({
    modelValue: {
        type: Array,
        default: () => [],
    },
    positionList: {
        type: Array,
        default: () => ['bottom', 'right', 'left', 'top'],
    },
});
const hotsStore = useHotsStore();
const getAnswerData = () => {
    return links.value?.map((item) => ({ startParam: item.startParam, endParam: item.endParam, relation: item.type }));
};
/**
 * 确认输出参数
 * @param row
 */
const handleEndPoint = (row) => {
    if (currentSelectedId.value === row.value) {
        handleCancelConnectLine();
        return;
    }
    if (!(currentSelectedId.value || currentSelectedId.value === 0)) return;
    const { value: from, paramName: startParam } = inputParamList.value.find((item) => item.value === currentSelectedId.value) || {};
    const linksData = links.value?.map((item) => {
        if (item.to === row.value && from === item.from) return { ...item, type: connectType.value };
        if (item.from === row.value && from === item.to) return { ...item, to: row.value, from: item.to, type: connectType.value, startParam: item.endParam, endParam: row.label };
        return item;
    });
    const isExist = links.value?.find((item) => (item.to === row.value && from === item.from) || (item.from === row.value && from === item.to));
    let data = new Set(linksData);
    if (!isExist) {
        data = new Set([
            ...linksData,
            {
                from,
                to: row.value,
                type: connectType.value,
                startParam,
                endParam: row.paramName,
            },
        ]);
    }
    links.value = Array.from(data);
    updateData(links.value);
    currentSelectedId.value = '';
    handleCancelConnectLine();
};
const {
    containerRef,
    isHovered,
    startBlockRef,
    svgRef,
    draw,
    popupVisibleMap,
    connectType,
    currentSelectedId,
    reverseCode,
    connectTypeList,
    arrowFillMap,
    inputParamList,
    blockWidth,
    links,
    showRelationTypePopover,
    updateData,
    handleCancelConnectLine,
    popupVisibleChange,
    handleConnectType,
    getPoint,
    handleDelSvgPath,
    handleGetPointBlock,
} = useConnectLine({
    hotsStore,
    showArrow: false,
    getAnswerData,
    $emit,
    positionList: props.positionList,
    handleEndPoint,
});

const getSvgInfo = (link) => {
    const svgBox = svgRef.value;
    const fromIndex = inputParamList.value?.findIndex((item) => item.value === link.from);
    const toIndex = inputParamList.value?.findIndex((item) => item.value === link.to);
    const { startX: x2, startY: y2 } = getPoint({ startDom: startBlockRef.value[toIndex], position: (props?.positionList || [])[toIndex] });
    const { startX: x1, startY: y1 } = getPoint({ startDom: startBlockRef.value[fromIndex], position: (props?.positionList || [])[fromIndex] });
    if (!svgBox || fromIndex === -1 || toIndex === -1) return { x1: 0, x2: 0, y1: 0, y2: 0 };
    return { x1, x2, y1, y2 };
};
defineExpose({ getAnswerData });
</script>
<style scoped lang="less">
@import './styles/round-lines';
</style>
<style lang="less">
.operating-instructions {
    color: #808080;
    font-size: 12px;
    line-height: normal;
    margin-bottom: 12px;
}

.connect-lines-popover {
    border-radius: 8px;
    padding: 8px 0;

    .item0 {
        margin-bottom: 10px;
    }

    .opt-item {
        width: 58px;
        padding: 5px 15px;
        cursor: pointer;

        &:hover {
            background: #f2f2f2;
        }
    }
}
</style>
