import { useMonitorStore } from '@/store/use-monitor-store';
import NebulaMatrix from '@nebula/bodydetect-web';
import { useTrackableFlow } from '@/store/track/use-trackable-flow';

let isNebulaInitializing = false; // 防止并发初始化的标志

export function useBodyDetection() {
    const monitorStore = useMonitorStore();

    const { execute: executeInitBodyDetect } = useTrackableFlow('人体检测功能初始化', async (ctx) => {
        const cameraMonitoringRule = monitorStore.examConfig.cameraMonitoringRule; // 防作弊配置集
        // 是否配置防作弊
        const needAntiCheat =
            monitorStore.examConfig.computerCameraMonitor &&
            cameraMonitoringRule &&
            (cameraMonitoringRule.substituteExam ||
                cameraMonitoringRule.multipleFaces ||
                cameraMonitoringRule.leaveSeat ||
                cameraMonitoringRule.lowerHead ||
                cameraMonitoringRule.lookAround);
        monitorStore.needAntiCheat = !!needAntiCheat; // 更新store中的状态
        ctx.logStep('防作弊需求分析完成', { status: 'success', needAntiCheat: monitorStore.needAntiCheat, cameraMonitoringRule });

        // 是否配置人脸验证
        const needFaceVerify = monitorStore.examConfig.personalVerification;
        ctx.logStep('人脸验证需求分析完成', { status: 'success', needFaceVerify: needFaceVerify });

        // 判断是否初始化活体检测sdk
        if (!monitorStore.nebulaLoaded && (needFaceVerify || needAntiCheat)) {
            if (isNebulaInitializing) {
                ctx.logStep('NebulaMatrix 初始化已在进行中，跳过此次初始化');
                // console.warn('NebulaMatrix initialization is already in progress.');
                return; // 如果已经在初始化，则提前返回
            }
            isNebulaInitializing = true;
            ctx.logStep('开始初始化 NebulaMatrix');

            try {
                NebulaMatrix.init({
                    cdnUrl: 'https://img.bosszhipin.com/static/file/2024/nebula-plugins-matrix.2.0.4.data', // 使用cdn版本
                });
                ctx.logStep('NebulaMatrix.init 调用完成');
                await NebulaMatrix.onRuntimeInitialized(); // 等待 runtime 初始化完成
                ctx.logStep('NebulaMatrix runtime 初始化完成');
                monitorStore.nebulaLoaded = true;
                ctx.logStep('NebulaMatrix 加载状态已更新为 true');
            } catch (error: any) {
                ctx.logStep('NebulaMatrix 初始化失败', { status: 'failure', error: error.message });
                throw error;
            } finally {
                isNebulaInitializing = false; // 无论成功或失败，都重置标志
                ctx.logStep('NebulaMatrix 初始化标志已重置');
            }
        } else {
            ctx.logStep('无需初始化 NebulaMatrix', {
                status: 'success',
                nebulaLoaded: monitorStore.nebulaLoaded,
                needFaceVerify: needFaceVerify,
                needAntiCheat: needAntiCheat,
            });
        }
    });

    return {
        initBodyDetect: executeInitBodyDetect,
    };
}
