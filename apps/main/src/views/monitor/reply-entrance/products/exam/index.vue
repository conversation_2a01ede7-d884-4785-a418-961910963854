<template>
    <div class="exam-wrap">
        <WaterMark
        v-if="dimensionList.length > 0"
        v-loading="isLoadData"
        class="watermark-wrap"
        :content="[`${monitorStore.examBaseInfo.userName}-${monitorStore.examBaseInfo.encryptUserId}`]"
        :waterMarkConfig="1"
    >
        <div class="question-wrap">
            <QuestionList v-model="dimensionList" @answerSave="answerSave" />
        </div>
        <div class="answer-card-wrap">
            <div class="timer-wrap">
                <div class="time-title">答题剩余时间</div>
                <div class="time">
                    {{ totalCountDownStr }}
                </div>
            </div>
            <div class="card-wrap">
                <div class="card-title">
                    <span class="title-main">答题卡</span>
                    <div class="indicators">
                        <div class="answered-indicator">已作答</div>
                        <div class="no-answer-indicator">未作答</div>
                    </div>
                </div>
                <div class="card-dimension-list">
                    <div v-for="dimensionItem of dimensionList" :key="dimensionItem.encryptId" class="card-dimension-item">
                        <div v-if="dimensionItem.questionList.length > 0" class="dimension-title">
                            {{ dimensionItem.dimensionName }}（共{{ dimensionItem.totalQuestionNum }}题，合计{{ dimensionItem.totalScore }}分）
                        </div>
                        <div class="number-list">
                            <div
                                v-for="(questionItem, questionIndex) in dimensionItem.questionList"
                                :key="questionItem.encryptId"
                                class="number-item"
                                :class="[{ 'number-item-answered': questionItem.answerStatus }]"
                                @click="() => clickNumberItem(questionItem)"
                            >
                                {{ questionIndex + 1 }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <b-button v-if="dimensionList.length > 0" type="primary" @click="clickCommitPaper"> 提交试卷 </b-button>
            </div>
        </div>
    </WaterMark>
    <div v-else v-loading="isLoadData" class="empty-question-wrap">
        <img src="https://img.bosszhipin.com/static/zhipin/kanjian/zhice/kaoshi/empty-question.png" alt="" />
        <div>
            未找到试卷，请点击下方按钮刷新页面重新获取试卷<template v-if="paperFailCount >= MaxRequestCount"> 或联系电话{{ HELP_PHONE }} </template>
        </div>
        <b-button type="primary" @click="refetchPaper"> 重新获取试卷 </b-button>
    </div>
    </div>
</template>

<script setup lang="ts" name="Exam">
import commitPaper from '@/hooks/useCommitPaper';
import { _fileDownload, _fileUploadForExam } from '@/services/apis/exam';
import { Timer } from '@/store/time/use-time-center';
import { useMonitorStore } from '@/store/use-monitor-store';
import { Questions as QuestionList, useQuestion } from '@crm/biz-exam-question';
import WaterMark from '@crm/exam-components/src/feature/watermark/index.vue';
import { HELP_PHONE } from '@crm/exam-constants';
import { Storage } from '@crm/exam-utils';
import { debounce } from 'lodash-es';
import { computed, onMounted, provide, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useEndTimeStore } from '@/store/exam-time';

const isLoadData = ref(false);
const route = useRoute();
const router = useRouter();
const monitorStore = useMonitorStore();
const endTimeStore = useEndTimeStore();

// 提供试题包内部调用的方法
useQuestion({
    BossAnalyticsTrack,
    TrackTypeEnum,
    program: {
        _programExecute: Invoke.exam.postProgramExecute,
        _programExecuteStatus: Invoke.exam.postProgramExecuteStatus,
        _programSetLanguage: Invoke.exam.postProgramSetLanguage,
        _programGetLanguage: Invoke.exam.getProgramGetLanguage,
    },
    showToast: Toast as any,
    onFileUpload: _fileUploadForExam,
    onFileDownload: _fileDownload,
});

provide('mode', 'exam');
async function answerSave(params: any) {
    const res = await Invoke.exam.postAnswerSave(params);
    if (res.code === 0) {
        BossAnalyticsTrack('zhice-pc-exam-answer-question', {
            pData: {
                type: TrackTypeEnum.成功,
                message: params,
                nameZh: '考生答题',
            },
        });
    }
}

const buildCountDownStr = (countDownTimer: Timer) => {
    return countDownTimer?.remainingTime.hours + ':' + countDownTimer?.remainingTime.minutes + ':' + countDownTimer?.remainingTime.seconds;
};

// 倒计时
const totalCountDownStr = computed(() => {
    return buildCountDownStr(endTimeStore.examEndTime);
});
// 考题题列表
const dimensionList = ref<any[]>([]);
// 获取答题列表
async function examQuestionList() {
    try {
        isLoadData.value = true;
        const params = {
            encryptExamId: route.query.examId,
        };
        const { code, data } = await Invoke.exam.getExamQuestionList(params);
        if (code === 0) {
            dimensionList.value = data?.questionList || [];
            endTimeStore.initExamEndTime(data.examEndTime);
            failCountReset();
        } else {
            failCountAdd();
        }
    } catch (error) {
        failCountAdd();
    } finally {
        isLoadData.value = false;
    }
}
const debouncedGetQuestionList = debounce(examQuestionList, 300, { leading: true });

const MaxRequestCount = 4;
const paperFailCount = ref(0);
async function failCountAdd() {
    const storageValue = Storage.get('kanjian_paper_fail_count');
    const currentFailCount = Number.isSafeInteger(Number(storageValue)) ? Number(storageValue) : 0;
    paperFailCount.value = currentFailCount + 1;
    Storage.set('kanjian_paper_fail_count', paperFailCount.value);
}
async function failCountReset() {
    Storage.set('kanjian_paper_fail_count', 0);
}
async function refetchPaper() {
    debouncedGetQuestionList();
}
// 点击题目序号定位题目开始
function clickNumberItem(questionItem: any) {
    const dom = document.querySelector(`#question-${questionItem.encryptQuestionId.replace(/~/g, '')}`);
    dom?.scrollIntoView?.();
}

// 交卷
async function clickCommitPaper() {
    // 计算未答题数量
    const questionListAll = dimensionList.value.map((dimensionItem: any) => dimensionItem.questionList).flat();
    const unAnswerQuestion = questionListAll.filter((questionItem: any) => !questionItem.answerStatus);
    if (unAnswerQuestion.length > 0) {
        Dialog.open({
            type: 'warning',
            title: '确认交卷？',
            content: `还有${unAnswerQuestion.length}道题没有作答，请确认是否交卷`,
            cancelText: '继续答题',
            confirmText: '继续交卷',
            confirm: async () => {
                commitPaper('manual', route, router);
            },
        });
    } else {
        commitPaper('manual', route, router);
    }
    // 考生答题页面，点击提交试卷按钮
    BossAnalyticsTrack('zhice-pc-exam-answer-submit', {
        p3: unAnswerQuestion.length > 0 ? 2 : 1,
        p4: unAnswerQuestion.length,
        pData: {
            nameZh: '点击交卷',
        },
    });
}

onMounted(async () => {
    await examQuestionList();
});
</script>

<style lang="less" scoped>
.exam-wrap {
    display: flex;
    margin-top: 8px;
    height: 100%;
}
.watermark-wrap {
    margin: 0 auto;
    display: flex;
}

.question-wrap {
    width: 900px;
    flex-grow: 0;
    flex-shrink: 0;
    margin-right: 8px;
    padding: 24px 0 0 24px;
    border-radius: 8px;
    overflow: auto;
    background-color: #fff;
    user-select: none;

    :deep(.question-list) {
        width: calc(100% - 18px);
    }
}

.answer-card-wrap {
    position: sticky;
    top: 0;
    width: 380px;
    height: calc(100vh - 88px);
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .timer-wrap {
        text-align: center;
        padding: 16px 0;
        border-radius: 8px;
        background-color: #fff;
        margin-bottom: 8px;
        flex-shrink: 0;

        .time-title {
            margin-bottom: 7px;
        }

        .time {
            color: #ed4359;
            font-size: 24px;
            height: 36px;
        }
    }
    .card-wrap {
        flex: 1;
        min-height: 0;
        border-radius: 8px;
        background-color: #fff;
        display: flex;
        flex-direction: column;

        .card-title {
            padding: 24px 19px 0;
            display: flex;
            justify-content: space-between;
            margin-bottom: 24px;
            flex-shrink: 0;

            .title-main {
                font-size: 16px;
                color: #3a3f4d;
            }

            .indicators {
                display: flex;

                .answered-indicator::before,
                .no-answer-indicator::before {
                    content: '';
                    display: inline-block;
                    vertical-align: middle;
                    width: 10px;
                    height: 10px;
                    border-radius: 2px;
                    border: 1px solid transparent;
                    margin-right: 4px;
                }

                .answered-indicator {
                    margin-right: 24px;

                    &::before {
                        border-color: var(--primary-color-6);
                        background-color: var(--primary-color-2);
                    }
                }

                .no-answer-indicator {
                    &::before {
                        background-color: #ebedf2;
                        border-color: #d3d8e6;
                    }
                }
            }
        }

        .card-dimension-list {
            flex: 1;
            min-height: 0;
            padding: 24px 19px;
            overflow-y: auto;

            .card-dimension-item {
                & + .card-dimension-item {
                    margin-top: 8px;
                }

                .dimension-title {
                    margin-bottom: 8px;
                }

                .number-list {
                    display: flex;
                    flex-wrap: wrap;

                    .number-item {
                        cursor: pointer;
                        width: 40px;
                        height: 40px;
                        background: #f7f8fb;
                        border-radius: 8px;
                        display: inline-block;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border: 1px solid transparent;
                        margin-right: 6px;
                        margin-bottom: 8px;

                        &:nth-child(7n) {
                            margin-right: 0;
                        }
                        &.number-item-answered {
                            border-color: var(--primary-color-6);
                            color: var(--primary-color-6);
                            background-color: var(--primary-color-2);
                        }
                    }
                }
            }
        }
    }
    .card-footer {
        margin-top: 8px;
        border-radius: 8px;
        background-color: #fff;
        height: 84px;
        padding: 0 19px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }
}

.empty-question-wrap {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #f4f7f9;
    color: #5e5e5e;
    font-size: 15px;
    line-height: 23px;
    img {
        width: 140px;
        height: 140px;
        margin-bottom: 20px;
    }
    button {
        margin-top: 32px;
    }
    .icon-loading {
        color: #8891b2;
        margin-bottom: 20px;
    }
    .loading-text {
        color: #1f384c;
        font-size: 14px;
    }
}
</style>
