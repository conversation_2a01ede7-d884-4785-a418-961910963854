<template>
    <div class="face-verification-panel">
        <div class="block-title">人脸验证</div>
        <div class="intro">
            <p>请在线录制一个3s的视频进行上传，用于人脸识别使用。</p>
            <p class="strong">要求全脸出镜，正面清晰，<span>面部光线良好</span></p>
        </div>
        <div class="face-container">
            <ItemTab v-model="curIndex" :tabs="tabs" />

            <component
                :is="curComponent"
                class="verification-container"
                :recognizeFaceFailedCount="recognizeResult.recognizeFaceFailedCount"
                :systemRecognizeResult="recognizeResult.systemRecognizeResult"
                :h5SystemRecognizeResult="recognizeResult.h5SystemRecognizeResult"
                :pcSystemRecognizeResult="recognizeResult.pcSystemRecognizeResult"
                :pcRecognizeFileUri="recognizeResult.pcRecognizeFileUri"
                :manualAuditStatus="manualResult.manualAuditStatus"
                :manualAuditResult="manualResult.manualAuditResult"
                :manualAuditResultRemarks="manualResult.manualAuditResultRemarks"
                :manualApplyAttachmentUrl="manualResult.manualApplyAttachmentUrl"
            />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { useMonitorStore } from '@/store/use-monitor-store';
import { emitter } from '@crm/exam-utils';
import { computed, nextTick, onBeforeMount, onBeforeUnmount, provide, reactive, ref, markRaw } from 'vue';
import ItemTab from './components/item-tab.vue';
import VerificationMobile from './components/verification-mobile.vue';
import VerificationPc from './components/verification-pc.vue';
import { MAX_MACHINE_VERIFY_TIMES } from './constant';

// 更新下一步按钮"可点击状态"
const emit = defineEmits(['updateClickable']);

const $monitorStore = useMonitorStore();

const tabs = [
    { name: '电脑摄像头', type: 'pc', component: markRaw(VerificationPc) },
    { name: '手机扫码', type: 'mobile', component: markRaw(VerificationMobile) },
];
const curIndex = ref(0);
const curComponent = computed(() => {
    return tabs[curIndex.value].component;
});

// 机审状态
const recognizeResult = reactive({
    recognizeFaceFailedCount: 0, // 人脸识别失败次数（总次数 pc+h5）
    systemRecognizeResult: 0, // 系统人脸识别结果 （总） 0=未进行 1-成功 2-失败
    pcSystemRecognizeResult: 0, // pc端的系统人脸识别结果 0=未进行 1-成功 2-失败 3-审核中
    h5SystemRecognizeResult: 0, // h5端系统人脸识别结果  0=未进行 1-成功 2-失败
    pcRecognizeFileUri: '', // 机审最后一帧图片
});
// 人工审核
const manualResult = reactive({
    manualAuditStatus: null as any, // 人工审核状态：1-审核中；2-审核完成
    manualAuditResult: null as any, // 人工审核结果：1-通过；2-不通过
    manualAuditResultRemarks: '', // 人工审核驳回原因
    manualApplyAttachmentUrl: '', // 人工审核图片地址
});

// 机审状态
async function getSystemRecognizeResult() {
    try {
        return Invoke.exam.getSystemRecognizeResult({
            encExamId: $monitorStore.examBaseInfo?.examInfo?.examId,
        });
    } catch (error) {
        return {};
    }
}
// 人工审核状态
async function getFaceRecognizeResult() {
    try {
        return Invoke.exam.getFaceRecognizeAuditInfo({
            encExamId: $monitorStore.examBaseInfo?.examInfo?.examId,
        });
    } catch (error) {
        return {};
    }
}

// 更新机审状态
function updateSystemRecognizeStatus(res: any) {
    const { recognizeFaceFailedCount, systemRecognizeResult, pcSystemRecognizeResult, h5SystemRecognizeResult, pcRecognizeFile } = res;
    recognizeResult.recognizeFaceFailedCount = recognizeFaceFailedCount;
    recognizeResult.h5SystemRecognizeResult = h5SystemRecognizeResult;
    recognizeResult.pcSystemRecognizeResult = pcSystemRecognizeResult;
    recognizeResult.systemRecognizeResult = systemRecognizeResult;
    recognizeResult.pcRecognizeFileUri = pcRecognizeFile?.uri || '';
}
// 更新人工审核状态
function updateManualRecognizeStatus(res: any) {
    const { auditStatus, auditResult, auditResultRemarks, applyAttachmentUrl } = res;
    manualResult.manualAuditStatus = auditStatus;
    manualResult.manualAuditResult = auditResult;
    manualResult.manualAuditResultRemarks = auditResult === 2 ? auditResultRemarks : ''; // 人工审核不通过的原因
    manualResult.manualApplyAttachmentUrl = applyAttachmentUrl;
}
// 是否审核通过
const isAuditPassed = computed(() => {
    return recognizeResult.systemRecognizeResult === 1 || (manualResult.manualAuditStatus === 2 && manualResult.manualAuditResult === 1);
});
// 广播更新事件，深度传递
function handleStateChange() {
    // 判断机审或人工审核已经通过
    emit('updateClickable', isAuditPassed.value);
    // 广播更新事件，深度传递
    nextTick(() => {
        emitter.emit('onVerifyStatusChange');
    });
    // 如果审核通过，关闭polling
    handleOffPolling();
}
async function updateStatus() {
    const systemResult = (await getSystemRecognizeResult()) as any;
    if (systemResult.code !== 0 && !systemResult.data) {
        return;
    }
    // 更新机审状态
    updateSystemRecognizeStatus(systemResult.data || {});
    // 更新人工审核状态
    if (recognizeResult.recognizeFaceFailedCount >= MAX_MACHINE_VERIFY_TIMES) {
        const faceResult = (await getFaceRecognizeResult()) as any;

        logger.info('faceResult-------2', faceResult);

        // 如果没有发起过人工审核, data === null
        if (faceResult.code === 0 && faceResult.data) {
            updateManualRecognizeStatus(faceResult.data || {});
        }
    }
    // 回调函数
    handleStateChange();
}

// MQTT消息订阅通知
function messageHandler(payload: any) {
    if (payload.proType === 10 || payload.proType === 8) {
        updateStatus();
    }
}
function addMessageListener() {
    emitter.on('receiveMessage', messageHandler);
}
function removeMessageListener() {
    emitter.off('receiveMessage', messageHandler);
}

// 轮询审核状态，与MQTT混合使用（补偿mqtt消息丢失或断连）
// 规避对用户操作的影响，判断是有当前是审核中的变化变更才去更新
// 还是放在事件触发的时候，抛出事件，进行回调
let pollingTimer: any = null;
async function startPolling() {
    pollingTimer = setInterval(async () => {
        if (document.visibilityState === 'hidden') {
            return;
        }
        // 机审状态
        const systemResult = (await getSystemRecognizeResult()) as any;
        if (systemResult.code !== 0 && !systemResult.data) {
            return;
        }
        // 人工审核状态
        const faceResult = systemResult.recognizeFaceFailedCount >= MAX_MACHINE_VERIFY_TIMES ? await getFaceRecognizeResult() : ({} as any);
        // 只处理审核成功
        if (systemResult.data.systemRecognizeResult === 1 || (faceResult.data?.auditStatus === 2 && faceResult.data?.auditResult === 1)) {
            updateSystemRecognizeStatus(systemResult.data || {});
            if (faceResult.code === 0 && faceResult.data) {
                updateManualRecognizeStatus(faceResult.data || {});
            }
            // 回调函数
            handleStateChange();
        }
    }, 30 * 1000);
}
// C 目前机审或者人工审核通过，没有关闭轮询 修复完成
function clearPolling() {
    if (pollingTimer) {
        clearInterval(pollingTimer);
    }
    pollingTimer = null;
}
// C 目前机审或者人工审核通过，没有关闭轮询 修复完成
function handleOffPolling() {
    if (isAuditPassed.value) {
        clearPolling();
    }
}

onBeforeMount(async () => {
    addMessageListener();
    await updateStatus();
    startPolling();
});

onBeforeUnmount(() => {
    removeMessageListener();
    clearPolling();
});

// 依赖注入
provide('updateOriginStatus', updateStatus);
</script>

<style lang="less" scoped>
.face-verification-panel {
    .block-title {
        position: relative;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 4px;
        &::before {
            content: '';
            position: absolute;
            top: 50%;
            margin-top: -5px;
            left: -12px;
            width: 2px;
            height: 10px;
            background: var(--primary-color);
            border-radius: 0px 2px 2px 0px;
        }
    }
    .intro {
        p {
            line-height: 20px;
            font-size: 13px;
            color: #939cbc;
            & + p {
                margin-top: 2px;
            }
            &.strong {
                width: fit-content;
                background: #ecf9f9;
                border-bottom: 1px dashed var(--primary-color);
                span {
                    color: var(--warning-color);
                }
            }
        }
    }
}
.face-container {
    display: flex;
    flex-direction: column;
    height: 269px;
    margin-top: 8px;
    background-color: rgba(18, 173, 169, 0.08);
    border-radius: 4px;
    .tabs-contaienr {
        margin-top: 16px;
    }
    .verification-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
}
</style>
