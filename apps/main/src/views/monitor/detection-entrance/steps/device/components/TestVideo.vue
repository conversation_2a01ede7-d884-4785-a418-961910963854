<template>
    <div class="detection-item">
        <TestStatus :isTesting="isTesting" :isSuccess="isSuccess" />
        <div class="detection-content">
            <div class="type">摄像头</div>
            <div class="result" :class="{ error: !isSuccess && !isTesting }">
                {{ testResult }}
            </div>
        </div>
        <template v-if="!isSuccess && !isTesting">
            <div class="detection-suggestion">
                {{ testSuggestions }}
                <span class="op-help" @click="emits('showGuid')"><SvgIcon name="svg-full-question" width="16" height="16" />帮助</span>
            </div>
            <div v-if="!isSuccess && !isTesting" class="detection-action">
                <b-button type="outline" shape="round" size="small" @click.prevent="test"> 重新检测 </b-button>
            </div>
        </template>
    </div>
</template>

<script setup lang="ts">
import { usePrepareCheckSave } from '@/hooks/usePrepareCheckSave';

import { urls } from '@/shared/url';
import { onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useTestResult } from '../useTestResult';
import TestStatus from './test-status.vue';
import { useMonitorStore } from '@/store/use-monitor-store';

// 检测建议

const emits = defineEmits(['update:is-success', 'update:is-testing', 'showGuid']);

const $route = useRoute();

const monitorStore = useMonitorStore();
const isTesting = ref(true); // 是否正在检测
const isSuccess = ref(false); // 是否检测成功
const testResult = ref(''); // 检测结果
const testSuggestions = ref('');
function updateIsTesting(value: boolean) {
    emits('update:is-testing', value);
}
function updateIsSuccess(value: boolean) {
    emits('update:is-success', value);
}
useTestResult(isTesting, isSuccess, updateIsTesting, updateIsSuccess);

watch(
    () => monitorStore.STATUS.camera.status,
    (newVal, oldVal) => {
        if (newVal !== oldVal && newVal === 2) {
            test();
        }
    },
);

function reset() {
    isTesting.value = true;
    isSuccess.value = false;
    testResult.value = '正在检测中…';
    testSuggestions.value = '';
}

let rtc: any;

async function test() {
    reset();

    try {
        let localStream = await rtc?.createStream({ video: true, disableDeviceChangeDetect: true });
        // 埋点开始
        BossAnalyticsTrack('zhice-pc-exam-device-check-video-stream-create-success', {
            pData: {
                type: TrackTypeEnum.成功,
                nameZh: '设备检测-成功创建视频流',
            },
        });
        // 埋点结束
        await localStream.initialize();
        // 埋点开始
        BossAnalyticsTrack('zhice-pc-exam-device-check-video-stream-init', {
            pData: {
                type: TrackTypeEnum.成功,
                nameZh: '设备检测-成功初始化视频流',
            },
        });
        // 埋点结束

        isSuccess.value = true;
        testResult.value = '摄像头已开启';

        // 重置监控状态并重新开启摄像头监控
        if (monitorStore.hasComputer) {
            monitorStore.openCameraMonitor(monitorStore.ExamMode === 'PREPARE_MODE').catch((error) => {
                logger.error('Failed to restart camera monitor:', error);
            });
        }

        usePrepareCheckSave({
            encryptExamId: ($route.query.examId || $route.query.seqId) as string,
            checkItem: 4, // 1 浏览器 2 网速 3麦克风 4 电脑主摄像头 5 电脑屏幕 6手机第二视角
            checkStatus: isSuccess.value ? 1 : 2, // //状态 1成功 2失败
            checkRemark: isSuccess.value ? '' : testResult.value,
        });

        // 关闭
        if (localStream) {
            localStream.close();
            localStream = null;
            // 埋点开始
            BossAnalyticsTrack('zhice-pc-exam-device-check-video-stream-close', {
                pData: {
                    type: TrackTypeEnum.成功,
                    nameZh: '设备检测-视频流关闭',
                },
            });
            // 埋点结束
        }
        return true;
    } catch (error: any) {
        isSuccess.value = false;
        testResult.value = '';
        switch (error.name) {
            case 'NotFoundError':
                testResult.value = '缺少可用的摄像头设备';
                testSuggestions.value = '请重新连接摄像头设备';
                break;
            case 'NotAllowedError':
                testResult.value = '摄像头权限未开启';
                testSuggestions.value = '请开启摄像头权限';
                break;
            case 'NotReadableError':
                testResult.value = '无法访问摄像头';
                testSuggestions.value = '请确保当前无其他应用正在使用摄像头';
                break;
            case 'SecurityError':
                testResult.value = '系统禁止访问摄像头';
                testSuggestions.value = '请取消限制后再检测';
                break;
            case 'AbortError':
                testResult.value = '摄像头异常';
                testSuggestions.value = '建议更换浏览器或设备后重新检测';
                break;
            case 'OverConstrainedError':
                testResult.value = 'cameraId 参数的值无效';
                testSuggestions.value = '确保 cameraId 传值正确且有效';
                break;
            default:
                testResult.value = '检测失败';
                testSuggestions.value = '初始化时遇到未知错误, 请重试';
                break;
        }
        usePrepareCheckSave({
            encryptExamId: ($route.query.examId || $route.query.seqId) as string,
            checkItem: 4, // 1 浏览器 2 网速 3麦克风 4 电脑主摄像头 5 电脑屏幕 6手机第二视角
            checkStatus: isSuccess.value ? 1 : 2, // //状态 1成功 2失败
            checkRemark: isSuccess.value ? '' : testResult.value,
        });
        // 埋点开始
        BossAnalyticsTrack('zhice-pc-exam-device-check-video-stream-init', {
            pData: {
                type: TrackTypeEnum.失败,
                message: error.name,
                nameZh: '设备检测-视频流初始化失败',
            },
        });
        // 埋点结束
        return false;
    } finally {
        isTesting.value = false;
    }
}

onMounted(async () => {
    // web RTC SDK实例
    // @ts-ignore
    rtc = NEBULARTC();
    rtc.setEnv(urls.rtcUrl);
    await test();
});

defineExpose({ test });
</script>
