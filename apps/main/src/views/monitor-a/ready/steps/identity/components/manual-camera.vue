<template>
    <div class="manual-camera-container">
        <div v-if="status === 'NOT_INIT'" class="init step">
            <SvgIcon class="svg-icon-face-detect" icon="svg-empty-face-detect" size="50" />
            <b-button type="primary" @click.prevent="startRecord"> 开始拍摄 </b-button>
        </div>
        <div v-else-if="status === 'IN_RECORDING'" class="step">
            <div id="nebular" />
            <div class="btns">
                <b-button type="outline" shape="round" status="primary" @click.prevent="takeShot"> 拍摄 </b-button>
            </div>
        </div>
        <div v-else-if="status === 'HAS_TOKESHOT' || status === 'MANUAL_VERIFYING' || status === 'MANUAL_SUCCESS' || status === 'MANUAL_FAIL'" class="end step">
            <img :src="getDisplayUrl(lastFramePic || manualApplyAttachmentUrl)" alt="" class="frame-img" />
            <div v-if="status === 'MANUAL_FAIL' || status === 'HAS_TOKESHOT'" class="btns">
                <b-button type="outline" shape="round" status="primary" @click.prevent="reShot"> 重拍 </b-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';

import { dataURLtoFile } from '@crm/exam-utils';
import NebulaMatrix from '@nebula/bodydetect-web';
import { inject, nextTick, onMounted, ref } from 'vue';
import { useFaceRecognizeSubmit } from '../hooks/useFaceRecognizeSubmit';
import { useStatus } from '../hooks/useStatus';
import { useDebuggingStore } from '../../../../store';

// 人身核验的前提是需要姓名&身份证信息完整

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false,
    },
    manualAuditStatus: {
        type: Number as PropType<number | null>,
        default: null,
    },
    manualAuditResult: {
        type: Number as PropType<number | null>,
        default: null,
    },
    manualApplyAttachmentUrl: {
        type: String,
        default: '',
    },
});

const emit = defineEmits(['update:modelValue', 'close']);

const debugStore = useDebuggingStore();

const validateNameAndIdentity = inject<() => Promise<boolean>>('validateNameAndIdentity', () => Promise.resolve(false));

function getDisplayUrl(url: string) {
    const regex = /^https?:\/\/[^/]+/;
    // const isDev = import.meta.env?.MODE === 'development'
    const isDev = true;
    const result = isDev ? url.replace(regex, '') : url;
    return result;
}

const status = ref('NOT_INIT');

const nebulaMatrix = new NebulaMatrix();
async function initNebulaMatrix() {
    if (!debugStore.antiCheatStatus.nebulaLoaded) {
        await NebulaMatrix.onRuntimeInitialized(); // 判断模型是否加载完成
        debugStore.antiCheatStatus.nebulaLoaded = true;
    }
    await nebulaMatrix.createBodyDetectStream({
        id: 'nebular',
    });
}

// 监听回调
// 1.活体检测SDK浏览器不支持
nebulaMatrix.on('error', (error: any) => {
    logger.error('浏览器不支持活体检测', error.message, error.code);
});
// 2.初始化时，用户已经禁用摄像头权限
nebulaMatrix.on('camer-mediaError', (error: any) => {
    logger.error('初始化时，用户已经禁用摄像头权限');
});
// 3.用户中途禁用摄像头权限
nebulaMatrix.on('camera-unauthorized', (error: any) => {
    logger.error('用户中途禁用摄像头权限');
});

function destory() {
    try {
        if (debugStore.antiCheatStatus.nebulaLoaded && nebulaMatrix.status !== 'stop') {
            nebulaMatrix.destroy();
        }
    } catch (error) {
        logger.error(error);
    }
}

async function startRecord() {
    status.value = 'IN_RECORDING';
    nextTick(async () => {
        initNebulaMatrix();
    });
}

const lastFramePic = ref('');
const hasShot = ref(false); // 用于规避查看场景，不让直接重复提交
function takeShot() {
    const img = nebulaMatrix.getCurrentFrameImage(); // base64
    lastFramePic.value = img;
    hasShot.value = true;
    destory();
    status.value = 'HAS_TOKESHOT';
    emit('update:modelValue', true); // 可以提交保存
    // 埋点
    BossAnalyticsTrack('zhice-pc-exam-identity-artificial-photo', {
        pData: {
            nameZh: '人工审核拍照',
        },
    });
}

function reShot() {
    startRecord();
}

function changeStatus() {
    // 首次进人工
    if (!props.manualAuditStatus) {
        status.value = 'NOT_INIT';
    } else if (props.manualAuditStatus === 1) {
        status.value = 'MANUAL_VERIFYING';
    } else if (props.manualAuditStatus === 2) {
        if (props.manualAuditResult === 1) {
            status.value = 'MANUAL_SUCCESS';
        } else {
            status.value = 'MANUAL_FAIL';
        }
    }
}

onMounted(() => {
    changeStatus();
});

const { updateOriginStatus } = useStatus(changeStatus);

const isLoading = ref(false);
const { doSubmit } = useFaceRecognizeSubmit({ picSourceType: 1 });
async function submit() {
    if (isLoading.value || !(await validateNameAndIdentity())) {
        return;
    }
    // 校验是否有有效文件
    const file = dataURLtoFile(lastFramePic.value, 'image');
    // 标记提交中
    isLoading.value = true;
    // 发起审核
    doSubmit({ file, biz: 'USER_EXAM_FACE_AUDIT' })
        .then(() => {
            emit('update:modelValue', false);
            emit('close');
            updateOriginStatus();
        })
        .catch(() => {
            Toast.danger({ content: '提交人工审核失败' });
        })
        .finally(() => {
            isLoading.value = false;
        });
}

defineExpose({ submit });
</script>

<style lang="less">
.manual-camera-container {
    .step {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        border-radius: 4px;
        overflow: hidden;

        &.init {
            background: #ecf9f9;
            border: 1px dashed #dfdfdf;
        }

        &.end {
            background: #ecf9f9;
        }

        .svg-icon-face-detect {
            color: #00a6a7;
            margin-bottom: 24px;
        }

        #nebular {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;

            video {
                display: block;
                max-width: 100%;
                max-height: 100%;
            }

            canvas {
                display: block;
            }
        }

        .btns {
            position: absolute;
            right: 24px;
            bottom: 24px;
        }

        .frame-img {
            display: block;
            max-width: 100%;
            max-height: 100%;
            margin: 0 auto;
        }
    }
}
</style>
