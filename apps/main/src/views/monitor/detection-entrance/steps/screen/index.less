.screen-pannel {
    display: flex;
    justify-content: space-between;
    .guid-box {
        position: relative;
        width: 264px;
        height: 305px;
        padding: 20px 24px;
        border-radius: 11px;
        &::before {
            position: absolute;
            content: '';
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            border-radius: 11px;
            border: 1px solid #dfdfdf;
            z-index: 0;
        }
        .title {
            display: flex;
            align-items: center;
            height: 26px;
            h3 {
                margin-left: 4px;
                font-size: 14px;
            }
        }
        .desc {
            margin-top: 8px;
            font-size: 12px;
            line-height: 20px;
            color: #808080;
            i {
                color: #d27400;
                font-style: normal;
            }
        }
        .b-image {
            img {
                position: relative;
                display: block;
                width: 216px;
                height: 135px;
                margin-top: 21px;
                border-radius: 4px;
                z-index: 1;
                cursor: pointer;
            }
        }
    }
    .share-box {
        &-default {
            width: 540px;
            height: 305px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            overflow: hidden;
            gap: 12px;
            background-color: rgba(18, 173, 169, 0.08);
        }
        &-success {
            width: 540px;
            height: 305px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            overflow: hidden;
            gap: 12px;
            background-color: rgba(18, 173, 169, 0.08);
        }
        &-error {
            width: 540px;
            height: 305px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            overflow: hidden;
            gap: 12px;
            background-color: #ffeae8;
        }

        img {
            display: block;
            width: 72px;
            height: 54px;
            margin-bottom: 12px;
        }
        .result {
            line-height: 20px;
            margin-bottom: 4px;
            font-size: 14px;
            color: #1f1f1f;
        }
        .intro {
            line-height: 17px;
            font-size: 12px;
            color: #808080;
        }
        .b-button {
            margin-top: 24px;
            background: none;
        }
    }
}
