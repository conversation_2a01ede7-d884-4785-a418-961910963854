<template>
    <div class="guidance" :class="{ 'preview-guidance': isPreview }">
        <div class="guidance-content">
            <p class="title">测验须知</p>
            <p>
                <span>·本次测验共 {{ examInfo.questionCount }} 题，</span>
                <span v-if="examInfo.questionTypeList">{{ examInfo.questionTypeList.length === 1 ? '均为' : '包含' }}{{ examInfo.questionTypeList.join('、') }}</span>
            </p>
            <!-- 倒计时 -->
            <p v-if="examInfo?.timedRuleType === 2">
                <span>·限时：{{ examInfo?.adviseDurationStr }}</span>
            </p>
            <!-- 正计时 -->
            <template v-if="examInfo?.timedRuleType === 1">
                <p>·预计作答时间为 {{ examInfo.adviseDurationStr }}</p>
                <p v-if="[1, 5, 7, 9, 11].includes(examInfo.productId)" class="warn">
                    <span> 请在 {{ initTime(examInfo.maxAnswerTime) }} 分钟内提交结果，如果超时会影响你的作答成绩 </span>
                </p>
            </template>
            <!-- 根据回看规则 显示对应文案 -->
            <p v-if="examInfo.reviewRuleType === 2">·每题只有一次作答的机会</p>
            <p v-else-if="examInfo.reviewRuleType === 1">·每题只有一次修改的机会</p>
            <template v-if="examInfo.instruction">
                <p class="title" style="margin-top: 16px">注意事项</p>
                <pre>{{ examInfo.instruction }}</pre>
            </template>
        </div>
        <VanButton type="primary" @click="handleStart"> 我知道了，开始答题 </VanButton>
    </div>
</template>

<script lang="ts" setup>
import { useExamStore } from '@/stores/exam/index';
import { usePaperStore } from '@/stores/paper/index';
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();
const examStore = useExamStore();
const paperStore = usePaperStore();
const isPreview = computed(() => paperStore.isPreview);
const examInfo = computed(() => examStore.examBaseInfo.examInfo);
const emit = defineEmits(['previewNext']);
function initTime(time: any) {
    return Math.floor(Number(time) || 0) || 1;
}

async function handleStart() {
    if (isPreview.value) {
        emit('previewNext');
        return;
    }

    const { code, message } = await Invoke.common.postEnterStage({
        seqId: route.query.seqId,
        encryptExamId: route.query.examId,
        stage: 5, // 写死参数，因为H5没有设备调试  5表示当前为指导语页面
    });

    if (code === 0) {
        router.replace({
            path: '/evaluation/question',
            query: route.query,
        });
    }
}
</script>

<style lang="less" scoped>
.guidance {
    font-family: 'PingFang SC';
    font-style: normal;
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;

    &.preview-guidance {
        min-height: 100vh;
        height: 100vh;
    }

    .guidance-content {
        flex: 1;

        p,
        pre {
            color: #5e5e5e;
            font-size: 15px;
            font-weight: 400;
            line-height: 23px;

            &.title {
                margin-bottom: 16px;
                color: #141414;
                font-size: 18px;
                font-weight: 500;
                line-height: 26px;
            }

            &.warn {
                margin: 4px 0;
                margin-left: 7px;
                display: inline-block;
                color: #ff7847;
                font-size: 14px;
                font-weight: 500;
                line-height: 22px;
                border-bottom: 1px solid #ff7847;
                background-color: var(--van-orange-light);
            }
        }
    }
}
</style>
