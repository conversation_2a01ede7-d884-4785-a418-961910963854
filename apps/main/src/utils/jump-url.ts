import { DEPLOY_ENV } from '@/shared';
import { useLoginType } from '@/views/login/composables/use-login-type';
import { useRoute } from 'vue-router';

// 跳转至登录页
export function jumpLogin(encSeqId?: string) {
    const { search } = window.location;
    const { params } = parseURL(search);
    const searchParams = new URLSearchParams(search);

    Invoke.login.postLogout();

    const { loginType, setLoginType } = useLoginType();

    const urlLoginType = Number(searchParams.get('loginType'));

    if (urlLoginType === 1) {
        setLoginType('sms');
    } else if (urlLoginType === 2) {
        setLoginType('password');
    }
    const { seqId } = useRoute().query || useRoute().params;
    window.location.href = `/login?seqId=${seqId || encSeqId || params.seqId}&loginType=${loginType.value === 'password' ? 2 : 1}`;
}

// 跳转打开H5项目 去测评
export function jumpH5Exam(url: string) {
    if (url) {
        let hrefUrl = url;

        // 开发环境需要拼接host
        if (DEPLOY_ENV === 'dev') {
            const { hostname, protocol } = window.location;
            hrefUrl = `${protocol}//${hostname}:8899${hrefUrl}`;
        }

        window.location.href = hrefUrl;
    }
}

// 生成 H5 考试项目 URL
export function generateExamUrl(url: string, isH5: boolean): string {
    let hrefUrl = url;

    const { hostname, protocol } = window.location;

    // 开发环境需要拼接host
    if (DEPLOY_ENV === 'dev') {
        hrefUrl = `:${isH5 ? 8899 : 8080}${hrefUrl}`;
    }

    return `${protocol}//${hostname}${hrefUrl}`;
}
