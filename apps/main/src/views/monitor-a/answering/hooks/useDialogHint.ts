import { useDebuggingStore } from '../../store';
import { useNetwork } from '@crm/vueuse-pro';
// 一、二、三视角异常提醒
import { watch } from 'vue';

const debuggingStore = useDebuggingStore();
const { isSupported, isOnline } = useNetwork();

const TIMER_INTERVAL = 5 * 60 * 1000;

function useOpenDialogCoolDown() {
    const visible = ref(false);

    let lastTimestamp = 0;

    function openDialogWithCoolDown() {
        const now = Date.now();
        if (now - lastTimestamp > TIMER_INTERVAL) {
            visible.value = true;
            lastTimestamp = now;
        }
    }
    return {
        visible,
        openDialogWithCoolDown,
    };
}

export default function () {
    const { visible: dialogVisibleMonitorHintCamera, openDialogWithCoolDown: openDialogWithCoolDownCamera } = useOpenDialogCoolDown();
    const { visible: dialogVisibleMonitorHintPhone, openDialogWithCoolDown: openDialogWithCoolDownPhone } = useOpenDialogCoolDown();
    const { visible: dialogVisibleMonitorHintScreen, openDialogWithCoolDown: openDialogWithCoolDownScreen } = useOpenDialogCoolDown();

    let timerScreen = -1;
    let timerPhone = -1;
    let timerMedia = -1;
    // 创建定时器并检查状态的通用函数
    function createTimerAndCheckStatus(timerRef: number, statusKey: 'screen' | 'phone' | 'camera', dialogVisibleRef: Ref<boolean>, setTimer: (timer: number) => void) {
        if (!isSupported.value || (isSupported.value && isOnline.value)) {
            timerRef = window.setTimeout(() => {
                if (debuggingStore.STATUS[statusKey].status !== 2) {
                    dialogVisibleRef.value = true;
                }
            }, TIMER_INTERVAL);
            setTimer(timerRef);
        }
    }

    function dialogClosed(type: string) {
        const timerConfig = {
            screen: {
                timerRef: timerScreen,
                statusKey: 'screen' as const,
                dialogVisibleRef: dialogVisibleMonitorHintScreen,
                setTimer: (timer: number) => {
                    timerScreen = timer;
                },
            },
            phone: {
                timerRef: timerPhone,
                statusKey: 'phone' as const,
                dialogVisibleRef: dialogVisibleMonitorHintPhone,
                setTimer: (timer: number) => {
                    timerPhone = timer;
                },
            },
            media: {
                timerRef: timerMedia,
                statusKey: 'camera' as const,
                dialogVisibleRef: dialogVisibleMonitorHintCamera,
                setTimer: (timer: number) => {
                    timerMedia = timer;
                },
            },
        };

        const config = timerConfig[type as keyof typeof timerConfig];
        if (config) {
            createTimerAndCheckStatus(config.timerRef, config.statusKey, config.dialogVisibleRef, config.setTimer);
        }
    }

    // 0未开启 1失败 2成功

    // 摄像头
    watch(
        () => debuggingStore.STATUS.camera.status,
        (status) => {
            if (!debuggingStore.hasComputer) {
                return;
            }
            if (status === 1 || status === 0) {
                if (!isSupported.value || (isSupported.value && isOnline.value)) {
                    openDialogWithCoolDownCamera();
                    if (timerMedia) {
                        window.clearTimeout(timerMedia);
                    }
                }
            } else if (status === 2) {
                dialogVisibleMonitorHintCamera.value = false;
                if (timerMedia) {
                    window.clearTimeout(timerMedia);
                }
            }
        },
        {
            immediate: true,
        },
    );

    // 手机
    watch(
        () => debuggingStore.STATUS.phone.status,
        (status) => {
            if (!debuggingStore.hasPhone) {
                return;
            }
            if (status === 1 || status === 0) {
                if (!isSupported.value || (isSupported.value && isOnline.value)) {
                    openDialogWithCoolDownPhone();
                    if (timerPhone) {
                        window.clearTimeout(timerPhone);
                    }
                }
            } else if (status === 2) {
                dialogVisibleMonitorHintPhone.value = false;
                if (timerPhone) {
                    window.clearTimeout(timerPhone);
                }
            }
        },
        {
            immediate: true,
        },
    );

    // 屏幕共享
    watch(
        () => debuggingStore.STATUS.screen.status,
        (status) => {
            if (!debuggingStore.hasScreen) {
                return;
            }
            if (status === 1 || status === 0) {
                if (!isSupported.value || (isSupported.value && isOnline.value)) {
                    openDialogWithCoolDownScreen();
                    if (timerScreen) {
                        window.clearTimeout(timerScreen);
                    }
                }
            } else if (status === 2) {
                dialogVisibleMonitorHintScreen.value = false;
                if (timerScreen) {
                    window.clearTimeout(timerScreen);
                }
            }
        },
        {
            immediate: true,
        },
    );

    return {
        dialogVisibleMonitorHintCamera,
        dialogVisibleMonitorHintPhone,
        dialogVisibleMonitorHintScreen,
        dialogClosed,
    };
}
