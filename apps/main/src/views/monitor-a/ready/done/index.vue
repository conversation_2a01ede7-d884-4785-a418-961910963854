<template>
    <div class="monitor-inner">
        <ExamInfo />
        <div class="exam-setting">
            <div class="prepare-done-panel">
                <img src="https://img.bosszhipin.com/static/zhipin/kanjian/zhice/examinee/497743945157163143.png" alt="" class="done" width="140" />
                <h2>考前准备流程已结束，祝您考试顺利</h2>
                <p class="result">如有检测项未成功通过，可重新调试或更换设备后，再次检测</p>
                <b-button shape="round" type="outline" @click.prevent="doCheck"> 重新检测 </b-button>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { emitter } from '@crm/exam-utils';
import ExamInfo from '../components/exam-info.vue';
import { useDebuggingStore } from '../../store';
import { useRouteParamId } from '../../hooks/useRouteParamId';

const debugStore = useDebuggingStore();
const { encryptExamId, seqId } = useRouteParamId();

function doCheck() {
    debugStore.step = 0;
}

// 考前准备第二视角状态更新
debugStore.updatePhoneCamera({
    encryptExamId: encryptExamId || seqId,
    status: 0,
});

debugStore.closeRtcAll(encryptExamId);
emitter.emit('prepareEnd', true);
</script>

<style lang="less" scoped>
.prepare-done-panel {
    display: flex;
    height: 100%;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .done {
        display: block;
        margin: 0 auto;
        width: 140px;
        height: 140px;
    }
    h2 {
        margin-top: 20px;
        line-height: 24px;
        font-size: 18px;
        font-weight: 500;
        color: #1f1f1f;
    }
    .result {
        margin-top: 8px;
        line-height: 22px;
        color: #808080;
        font-size: 14px;
    }
    .b-button {
        margin-top: 32px;
    }
}
</style>
