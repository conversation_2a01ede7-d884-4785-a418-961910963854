<template>
    <div class="monitor-inner">
        <ExamInfo />
        <div class="exam-setting">
            <div class="prepare-done-panel">
                <img src="./images/done.png" alt="" class="done" width="140" />
                <h2>考前准备流程已结束，祝您考试顺利</h2>
                <p class="result">如有检测项未成功通过，可重新调试或更换设备后，再次检测</p>
                <b-button shape="round" type="outline" @click.prevent="doCheck"> 重新检测 </b-button>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { useMonitorStore } from '@/store/use-monitor-store';
import { emitter } from '@crm/exam-utils';
import { useRoute } from 'vue-router';
import ExamInfo from '../consist/exam-info.vue';
import { useDebuggingStore } from '../../../monitor-a/store';

const monitorStore = useMonitorStore();
const debugStore = useDebuggingStore();
const $route = useRoute();

function doCheck() {
    monitorStore.step = 0;
}

const encryptExamId = ($route.params.examId || $route.params.seqId) as string;

// 考前准备第二视角状态更新
debugStore.updatePhoneCamera({
    encryptExamId,
    status: 0,
});

logger.info('考前准备结束', '关闭所有RTC连接', '开始关闭所有监控连接');

monitorStore.closeRtcAll(encryptExamId);
emitter.emit('prepareEnd', true);
</script>

<style lang="less" scoped>
.prepare-done-panel {
    display: flex;
    height: 100%;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .done {
        display: block;
        margin: 0 auto;
        width: 140px;
        height: 140px;
    }
    h2 {
        margin-top: 20px;
        line-height: 24px;
        font-size: 18px;
        font-weight: 500;
        color: #1f1f1f;
    }
    .result {
        margin-top: 8px;
        line-height: 22px;
        color: #808080;
        font-size: 14px;
    }
    .b-button {
        margin-top: 32px;
    }
}
</style>
