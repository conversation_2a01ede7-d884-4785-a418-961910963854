<template>
    <PaperTime v-if="!loading && !isExam" />
    <div v-if="loading" class="layout-loading-wrap">
        <b-loading type="dot"> 加载中 </b-loading>
    </div>
    <div v-else class="paper-content">
        <RouterView />
        <!-- <component :is="componentsMap[type]" v-if="componentsMap[type]" @onSubmitPaper="onSubmitPaper" @onQuestionChange="questionShowTimeEvent" :isPreview="true" /> -->
    </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useMonitorStore } from '@/store/use-monitor-store';
import PaperTime from '@/views/monitor/components/paper-time.vue';
import { useReplyType } from '@/views/monitor/reply-entrance/composables/useReplyType';

const route = useRoute();
const monitorStore = useMonitorStore();
const { type, isExam, componentsMap } = useReplyType();

const { fetchPreviewBaseInfo } = monitorStore;

const loading = ref(true);

// 获取数据
async function getExamInfo() {
    loading.value = true;
    await fetchPreviewBaseInfo({
        key: route.query.key as string,
    });
    loading.value = false;
}

function onSubmitPaper() {
    Dialog.open({
        title: '预览结束',
        content: '预览结束，请关闭页面',
        type: 'warning',
        showCancel: false,
        showClose: false,
        confirmText: '知道了',
        layerClosable: false,
        enableEscClose: false,
    });
}

function questionShowTimeEvent() {}

onMounted(() => {
    getExamInfo();
});
</script>

<style lang="less" scoped>
.paper-content {
    position: relative;
    top: 0;
    left: 0;
    flex: 1;
    display: flex;
    height: 100%;
    padding: 12px 13px;
    justify-content: center;
}

.layout-loading-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
</style>
