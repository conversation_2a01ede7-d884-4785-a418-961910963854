<template>
    <div class="btn-control-wrap">
        <b-button v-if="showJumpBtn" @click="onClickAdvanceSubmit" status="danger" type="outline">提前交卷</b-button>
        <b-button v-if="showJumpBtn" @click="onClickJump" type="outline">跳过</b-button>
        <b-button v-if="showPrevBtn" @click="onClickPrev" type="outline">上一题</b-button>
        <b-button v-if="showNextBtn" @click="onClickNext" :type="isCertificationAbility ? 'primary' : 'outline'">下一题</b-button>
        <b-button :disabled="nextDisabled" v-if="showSubmitBtn" @click="onClickSubmit" type="primary">提交</b-button>
    </div>
</template>
<script setup lang="ts">
import { computed } from 'vue';
import { usePaperStore } from '../store';
import { debounce } from 'lodash-es';
import { PAPER_REVIEW_TYPE, paperQuestionNextMethod } from '../constant';

defineOptions({
    name: 'BtnControl',
});

const props = defineProps({
    nextDisabled: {
        type: Boolean,
        default: false,
    },
    isCertificationAbility: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(['next', 'jump', 'advanceSubmit', 'submit', 'prev']);

const paperStore = usePaperStore();

const isCurrentLast = computed(() => {
    const { currentQuestionIndex, allQuestionList } = paperStore;

    const isNotAnsweredNumber = notAnsweredNumber.value <= 1;

    if (props.isCertificationAbility) {
        return isNotAnsweredNumber;
    } else {
        return isNotAnsweredNumber && currentQuestionIndex === allQuestionList.length - 1;
    }
});

// 不是第一题（除第一题外的每道题都显示）
const notFirst = computed(() => {
    const { currentQuestionIndex } = paperStore;
    return currentQuestionIndex > 0;
});

const notAnsweredNumber = computed(() => {
    const { allQuestionList } = paperStore;
    return allQuestionList.filter((item) => item.status === 0).length;
});

const showPrevBtn = computed(() => {
    let flag = false;
    const { config, currentQuestionIndex, allQuestionList } = paperStore;
    const { reviewRuleType } = config;

    if (reviewRuleType === PAPER_REVIEW_TYPE.不可回看) {
        flag = false;
    } else if (reviewRuleType === PAPER_REVIEW_TYPE.不限制回看) {
        flag = notFirst.value;
    } else if (reviewRuleType === PAPER_REVIEW_TYPE.可回看一题) {
        const notAanswerIndex = allQuestionList.findIndex((item) => item.status === 0);
        flag = (notFirst.value && notAanswerIndex === currentQuestionIndex) || isCurrentLast.value;
    }

    return flag;
});

const showSubmitBtn = computed(() => {
    const { config } = paperStore;
    const { next_method } = config;

    // 如果配置了手动提交
    if (next_method === paperQuestionNextMethod.manual && isCurrentLast.value) {
        return true;
    }

    return isCurrentLast.value;
});

const showNextBtn = computed(() => {
    const { currentQuestion, config } = paperStore;
    const { next_method, reviewRuleType } = config || {};

    // 如果配置了 不限制回看 或者 可回看一题 并且处于回看状态中
    if (currentQuestion?.status === 1 && [PAPER_REVIEW_TYPE.不限制回看, PAPER_REVIEW_TYPE.可回看一题].includes(reviewRuleType)) {
        return !isCurrentLast.value;
    } else if (showSubmitBtn.value || !currentQuestion || next_method === paperQuestionNextMethod.auto) {
        // 如果 提交按钮展示了 或者 当前题没有 或者 自动提交模式下 不展示下一题按钮
        return false;
    }

    return !isCurrentLast.value;
});

const showJumpBtn = computed(() => {
    if (props.isCertificationAbility) {
        return notAnsweredNumber.value > 1;
    }

    return false;
});

const onClickJump = debounce(
    () => {
        emit('jump');
    },
    300,
    { leading: true },
);

const onClickAdvanceSubmit = debounce(
    () => {
        emit('advanceSubmit');
    },
    300,
    { leading: true },
);

const onClickNext = debounce(
    () => {
        const { currentQuestion } = paperStore;
        if (showSubmitBtn.value || !currentQuestion) {
            return;
        }
        emit('next');
    },
    300,
    { leading: true },
);

const onClickSubmit = debounce(
    () => {
        emit('submit');
    },
    300,
    { leading: true },
);

const onClickPrev = debounce(
    () => {
        emit('prev');
    },
    300,
    { leading: true },
);
</script>
<style lang="less" scoped>
.btn-control-wrap {
    display: flex;
    justify-content: center;
    align-items: center;

    .b-button {
        margin-right: 12px;

        &:last-child {
            margin-right: 0;
        }
    }
}
</style>
