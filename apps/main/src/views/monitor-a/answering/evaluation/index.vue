<template>
    <div v-if="loading" class="some-class loading-wrap">
        <b-loading type="dot"> 加载中 </b-loading>
    </div>
    <div v-else class="some-class">
        <div class="evaluation-question-wrap">
            <!-- 背景信息模块 -->
            <div v-if="isBackgroundQuestion" class="evaluation-question-box background-info">
                <div class="evaluation-question-wrap-header">
                    <p class="background-info-title">
                        <span v-if="paperStore.currentQuestion.backgroundInfoType === QUESTION_TYPE.SCENE">背景信息</span>
                        <span v-if="paperStore.currentQuestion.backgroundInfoType === QUESTION_TYPE.DATA_ANALYSIS">资料分析</span>
                    </p>
                </div>
                <div class="evaluation-question-wrap-content">
                    <SceneQuestion />
                </div>
            </div>
            <!-- 试题模块 -->
            <div
                class="evaluation-question-box"
                :class="{
                    'background-question': isBackgroundQuestion,
                    question: !isBackgroundQuestion,
                }"
            >
                <div class="evaluation-question-wrap-header">
                    <QuestionNumberInfo />
                </div>
                <div class="evaluation-question-wrap-content">
                    <Question ref="questionRef" @answerSelect="onAnswerSelect" @questionChange="onQuestionChange" />
                </div>
                <div class="evaluation-question-wrap-footer">
                    <BtnControl
                        :nextDisabled="hasAnswer"
                        @next="onClickNext({ isAutoSelect: false, commitType: 0 })"
                        @submit="onClickNext({ isAutoSelect: false, commitType: 1 })"
                        @prev="onClickPrev"
                        @advanceSubmit="onClickAdvanceSubmit"
                        @jump="onClickJump"
                        :isCertificationAbility="isCertificationAbility"
                    />
                </div>
            </div>
        </div>
        <!-- 操作面板模块 -->
        <div class="evaluation-info-wrap">
            <ProgressInfo />
            <div v-if="isCertificationAbility" class="question-panel-box">
                <QuestionPanel @switchQuestion="onSwitchQuestion" />
            </div>
        </div>
    </div>
    <PreviewImage ref="previewImageRef" />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, provide } from 'vue';
import ProgressInfo from './component/progress-info.vue';
import QuestionNumberInfo from './component/question-number-info.vue';
import { usePaperStore } from './store';
import Question from './component/question/index.vue';
import SceneQuestion from './component/question/scene-question.vue';
import BtnControl from './component/btn-control.vue';
import QuestionPanel from './component/question-panel.vue';
import { paperQuestionNextMethod } from './constant';
import { PreviewImage } from '@crm/exam-components';
import { EVALUATION_PREVIEW_IMAGE_KEY, QUESTION_TYPE } from './constant';
import { useMonitorStore } from '@/store/use-monitor-store';
import { useEvaluationEvents } from '../hooks/useEvaluationEvents';
import { useRouteParamId } from '../../hooks/useRouteParamId';
import { TCommitType } from '../type';

const { seqId, examId } = useRouteParamId();
const { onSubmitPaper, onQuestionChange } = useEvaluationEvents({ examId, seqId });

const paperStore = usePaperStore();
const monitorStore = useMonitorStore();
const loading = ref(false);
// 题型组件ref
const questionRef = ref<any>(null);
// 预览图片弹窗实例
const previewImageRef = ref();
// 预览图片
provide(EVALUATION_PREVIEW_IMAGE_KEY, {
    open: (src: string) => {
        previewImageRef.value.open(src);
    },
});

const isBackgroundQuestion = computed(() => {
    const backgroundInfoEncryptId = paperStore.currentQuestion?.backgroundInfoEncryptId || '';
    const backgroundInfoType = paperStore.currentQuestion?.backgroundInfoType || 0;

    return backgroundInfoEncryptId && [QUESTION_TYPE.SCENE, QUESTION_TYPE.DATA_ANALYSIS].includes(backgroundInfoType);
});

const hasAnswer = computed(() => {
    let flag = true;
    if (questionRef.value && questionRef.value.checkQuestionStatus) {
        const [result] = questionRef.value?.checkQuestionStatus();
        if (result) {
            flag = false;
        }
    }
    return flag;
});

// 作答方式 (目前用于区分是否是认证能力)
const isCertificationAbility = computed(() => monitorStore.examBaseInfo.examInfo?.answerListType === 4);

// 未答题列表
const notAnsweredList = computed(() => {
    return paperStore.allQuestionList.filter((item: { status: number }) => item.status === 0);
});

const getEvaluationList = async () => {
    loading.value = true;

    await paperStore.getData({
        encryptExamId: examId as string,
    });

    loading.value = false;
};

// 试题选择答案以后回调
function onAnswerSelect() {
    onClickNext({ isAutoSelect: true, commitType: 0 });
}

const onClickAdvanceSubmit = () => {
    Dialog.open({
        title: '提前交卷',
        content: '尚未完成题目作答，是否提交试卷?',
        type: 'warning',
        showCancel: true,
        showClose: false,
        layerClosable: false,
        enableEscClose: false,
        confirmText: '确定交卷',
        confirm() {
            onClickNext({ isAutoSelect: false, commitType: 1 });
        },
    });
};

// 跳过当前试题逻辑
const onClickJump = () => {
    const { currentQuestionIndex, allQuestionList } = paperStore;
    // 当前试题未作答，则跳转至下一题
    let index = currentQuestionIndex + 1;

    // 如果当前试题是最后一题，则跳转至第一题
    if (index >= allQuestionList.length) {
        index = 0;
    }

    // 切换试题
    switchQuestion(allQuestionList[index].encryptId);
};

// 切换试题逻辑
const switchQuestion = (currentEncryptId: string = '') => {
    const { allQuestionList } = paperStore;
    let encryptId = currentEncryptId;

    // 默认跳转的题目索引
    let notAnsweredCurrentIndex = 0;

    // 获取当前题在所有题目当中的索引
    const sliceIndex = allQuestionList.findIndex((item: { encryptId: string }) => item.encryptId === encryptId);
    // 获取当前题在所有题目当中的索引之后的所有题目
    const sliceList = allQuestionList.slice(sliceIndex, allQuestionList.length);

    // 如果当前题后面还有试题
    if (sliceList.length > 0) {
        // 获取当前题在所有题目当中的索引之后的所有题目中未作答的题目
        const nextNotAnsweredQuestion = sliceList.find((item: { status: number }) => item.status === 0);

        // 如果当前题后面还有未作答的题目
        if (nextNotAnsweredQuestion) {
            // 获取未作答题目在所有题目当中的索引
            const notAnsweredIndex = notAnsweredList.value.findIndex((item: { encryptId: string }) => item.encryptId === nextNotAnsweredQuestion.encryptId);
            // 如果未作答题目在所有题目当中的索引存在
            if (notAnsweredIndex !== -1) {
                // 设置未作答题目在所有题目当中的索引
                notAnsweredCurrentIndex = notAnsweredIndex;
            }
        }
    }

    // 获取未作答题目
    const nextQuestion = notAnsweredList.value[notAnsweredCurrentIndex];

    // 获取未作答题目在所有题目当中的索引
    let nextQuestionIndex = allQuestionList.findIndex((item: { encryptId: string }) => item.encryptId === nextQuestion.encryptId);

    // 跳转至未作答题目
    paperStore.jumpToQuestion(nextQuestionIndex);
};

// 上一题
const onClickPrev = () => {
    questionRef.value
        .submit({
            isShowToast: false,
        })
        .then(() => {
            const prevIndex = paperStore.currentQuestionIndex - 1;
            paperStore.jumpToQuestion(prevIndex < 0 ? 0 : prevIndex);
        });
};

// 下一题
const onClickNext = ({ isAutoSelect = true, commitType = 0 }) => {
    const { config, currentQuestionIndex, allQuestionList } = paperStore;
    const isCurrentLast = currentQuestionIndex === allQuestionList.length - 1 && notAnsweredList.value.length === 0;
    let nextQuestionIndex = currentQuestionIndex === allQuestionList.length - 1 ? currentQuestionIndex : currentQuestionIndex + 1;
    const { next_method } = config;

    // 如果配置项是 自动提交 并且是 手动点击下一题
    if (next_method === paperQuestionNextMethod.auto && !isAutoSelect && !commitType) {
        if (checkQuestionStatus()) {
            paperStore.jumpToQuestion(nextQuestionIndex);
        }
        return;
    }

    questionRef.value
        ?.submit({
            isShowToast: commitType !== 2,
        })
        .then((res: any) => {
            if (isAutoSelect && isCurrentLast) {
                return;
            }

            if (commitType === 0 && res.params) {
                if (isCertificationAbility.value) {
                    switchQuestion(res.params.evaPaperSnapshotId);
                } else {
                    paperStore.jumpToQuestion(nextQuestionIndex);
                }
            }
        })
        .finally(() => {
            if (commitType > 0) {
                // 提交试卷
                onSubmitPaper({ commitType: commitType as TCommitType });
            }
        });
};

// 跳题
const onSwitchQuestion = (data: any) => {
    switchQuestion(data.encryptId);
};

function checkQuestionStatus() {
    let flag = false;
    const [result, message] = questionRef.value.checkQuestionStatus();
    if (!result && message) {
        Toast.warning(message as string);
        flag = false;
    } else {
        flag = true;
    }
    return flag;
}

onMounted(() => {
    getEvaluationList();
});
</script>

<style lang="less" scoped>
.some-class {
    // position: relative;
    display: flex;
    height: 100%;
    width: 1117px;
    margin: 0 auto;

    &.loading-wrap {
        background-color: #ffffff;
        justify-content: center;
        align-items: center;
    }

    .evaluation-question-wrap {
        flex: 1;
        background-color: #ffffff;
        border-radius: 12px 12px 0px 0px;
        background-size: 100% auto;
        background-image: url('//img.bosszhipin.com/static/zhipin/kanjian/evaluate/662206703547197105.png');
        background-repeat: no-repeat;
        background-position: top center;
        display: flex;

        .evaluation-question-box {
            position: relative;
            display: flex;
            flex-direction: column;
            border-radius: 12px 12px 0 0;
            overflow: hidden;

            &.background-question {
                width: 431px;
            }

            &.question {
                flex: 1;
            }

            &.background-info {
                flex: 1;

                .evaluation-question-wrap-content {
                    border-right: 1px solid #e5e5e5;
                }
            }

            .evaluation-question-wrap-header {
                padding: 24px 32px 14px;

                .background-info-title {
                    padding-top: 6px;
                    color: #141414;
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 26px;
                }
            }

            .evaluation-question-wrap-content {
                flex: 1;
                overflow-y: auto;
                padding: 0 32px;
            }

            .evaluation-question-wrap-footer {
                padding: 24px 32px;
                background-color: #ffffff;
            }
        }
    }

    .evaluation-info-wrap {
        margin-left: 10px;
        width: 237px;
        height: 100%;
        // display: flex;
        // flex-direction: column;
        overflow: hidden;

        .question-panel-box {
            margin-top: 10px;
            height: calc(100vh - 240px);
        }
    }
}
</style>
