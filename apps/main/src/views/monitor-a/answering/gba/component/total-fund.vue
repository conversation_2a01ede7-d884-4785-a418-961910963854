<template>
    <div class="total-fund-container">
        <img src="@/assets/images/gba/coin.png" alt="" />
        <Text :stroke-width="strokeWidth" :text="text" style="flex-grow: 1" v-bind="styleAttr" />
        <Text :needAnimation="true" :stroke-width="strokeWidth" :text="goldNum" style="white-space: nowrap" v-bind="styleAttr" :reservedDecimalPlaces="reservedDecimalPlaces" />
    </div>
</template>

<script lang="ts" setup name="TotalFund">
import { computed } from 'vue';
import Text from './text.vue';

const props = defineProps({
    goldNum: {
        type: Number,
        default: 0,
    },
    text: {
        type: String,
        default: '金币',
    },
    strokeWidth: {
        type: [Number, String],
        default: 1,
    },
    reservedDecimalPlaces: {
        type: Number,
        default: 0,
    },
});
const styleAttr = computed(() => ({
    'font-family': 'PingFang SC',
    'font-size': '16px',
    'font-weight': 600,
    'stroke-color': '#fff',
    'stroke-width': props.strokeWidth,
    'text-color': '#2d2d2d',
    'shadow-filter-config': { x: 0, y: 1, blur: 0.5, color: 'rgba(37, 41, 46, 0.29)' },
}));
</script>

<style lang="less" scoped>
.total-fund-container {
    min-width: 173px;
    height: 38px;
    padding-right: 12px;
    background: linear-gradient(179.99998deg, rgba(255, 255, 255, 0.5) 0%, rgba(203, 236, 255, 0.5) 100%);
    border: 1px solid rgba(255, 255, 255, 0.8);
    border-radius: 29.53px;
    display: flex;
    align-items: center;

    & > * {
        flex-shrink: 0;
    }

    img {
        width: 52px;
        height: 40px;
        margin-right: -10px;
    }
}
</style>
