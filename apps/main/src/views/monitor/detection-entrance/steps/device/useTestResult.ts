import type { Ref } from 'vue';
import { watch } from 'vue';

export function useTestResult(isTesting: Ref<boolean>, isSuccess: Ref<boolean>, updateIsTesting: (value: boolean) => void, updateIsSuccess: (value: boolean) => void) {
    watch(
        () => isTesting.value,
        (newValue) => {
            updateIsTesting(newValue);
        },
    );
    watch(
        () => isSuccess.value,
        (newValue) => {
            updateIsSuccess(newValue);
        },
    );
}
