<template>
    <QuestionTitle :questionInfo="questionInfo" />
    <div class="single-choice-wrap">
        <div class="single-choice-item" v-for="item of questionInfo.questionOptionList" :key="item.encryptId" @click="clickAnswer(item.encryptId)">
            <OptionItem :imgList="item.files" :content="item.optionContent" :isChecked="checked === item.encryptId" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { paperQuestionNextMethod } from '../../constant';
import OptionItem from './component/option-item.vue';
import QuestionTitle from './component/question-title.vue';

const props = defineProps({
    questionInfo: {
        type: Object,
        default: () => ({}),
    },
    answer: {
        type: [String, Array],
        default: () => '',
    },
    nextMethodType: {
        type: Number,
        default: 0,
    },
});

const emit = defineEmits(['select', 'onComplete']);

const lastChecked = ref('');
const checked = ref('');
watch(
    () => [props.questionInfo, props.answer],
    async () => {
        const answer = props.answer as string;

        lastChecked.value = answer;
        checked.value = answer;
    },
    { immediate: true },
);

function clickAnswer(value: any) {
    const [flag, , params] = checkAnswer(value);

    if (!flag) {
        return;
    }

    checked.value = value;

    // 如果配置项是 自动提交
    if (props.nextMethodType === paperQuestionNextMethod.auto) {
        setTimeout(() => {
            emit('select', params);
        }, 200);
    }
}

function checkAnswer(value: string) {
    let flag = true;
    let message = '';
    const val = value || checked.value;
    const { encryptId, questionType } = props.questionInfo;

    if (!val) {
        flag = false;
        message = '答案不能为空';
    }

    const params = {
        evaPaperSnapshotId: encryptId,
        questionType,
        answerContent: val,
    };

    return [flag, message, params];
}

defineExpose({
    check: checkAnswer,
});
</script>

<style lang="less" scoped>
.single-choice-wrap {
    .single-choice-item {
        margin-bottom: 12px;

        &:last-child {
            margin-bottom: 20px;
        }
    }
}
</style>
