import { commitPaper } from '../../utils/commitPaper';
import { useDebuggingStore } from '../../store';

// 常量定义
const DIALOG_CONFIG = {
    RE_LOGIN: {
        title: '重复登录',
        content: '检测到您刚刚有重复登录操作，重登超过一定次数会自动交卷，作答期间请勿离开答题页面！',
        type: 'warning' as const,
        confirmText: '知道了',
    },
    FORCE_SUBMIT: {
        title: '强制交卷',
        content: '检测到您重复登录次数超过限制，系统已强制交卷！',
        type: 'warning' as const,
        confirmText: '知道了',
    },
} as const;

// 类型定义
interface ReLoginRule {
    reLoginWarning: boolean;
    currentReLoginCount: number;
    warningConfirm: boolean;
    maxLoginCount: number;
    forceSubmitWhenOverLimit: boolean;
}

/**
 * 检查是否需要执行重复登录流程
 */
function shouldExecuteReLoginFlow(reLoginWarning: boolean, currentReLoginCount: number, warningConfirm: boolean): boolean {
    if (!reLoginWarning) {
        return false; // 未设置重复登录警告
    }

    if (currentReLoginCount === 0) {
        return false; // 第一次登录
    }

    if (warningConfirm) {
        return false; // 确认过弹窗提醒
    }

    return true;
}

/**
 * 执行重复登录流程
 */
function executeReLoginFlow(currentReLoginCount: number, maxLoginCount: number, forceSubmitWhenOverLimit: boolean, route: any): void {
    if (currentReLoginCount < maxLoginCount) {
        showReLoginWarningDialog(route);
    } else if (forceSubmitWhenOverLimit) {
        showForceSubmitDialog(route);
    }
}

/**
 * 显示重复登录警告弹窗
 */
function showReLoginWarningDialog(route: any): void {
    Dialog.open({
        ...DIALOG_CONFIG.RE_LOGIN,
        showCancel: false,
        showClose: false,
        layerClosable: false,
        enableEscClose: false,
        close() {
            Invoke.exam.postWaningConfirmReLogin({
                encryptExamId: route.query.examId,
            });
        },
    });

    // 埋点记录
    BossAnalyticsTrack('zhice-pc-exam-reLogin-dialog-warn', {
        pData: {
            type: TrackTypeEnum.失败,
            nameZh: '弹出重复登录弹窗',
        },
    });
}

/**
 * 显示强制交卷弹窗
 */
function showForceSubmitDialog(route: any): void {
    Dialog.open({
        ...DIALOG_CONFIG.FORCE_SUBMIT,
        showCancel: false,
    });
    const { examId, seqId } = route.query;

    // 使用 force 作为交卷原因
    commitPaper({ type: 'reLogin', examId, seqId });
}

/**
 * @description 重复登录弹窗流程 hooks
 * 该流程用于处理用户在考试过程中重复登录的场景。
 * 1. 首先会检查考试配置中是否开启了重复登录警告 (reLoginWarning)。如果未开启，则流程直接结束。
 * 2. 如果当前是首次登录 (currentReLoginCount === 0)，则流程直接结束。
 * 3. 如果用户已经确认过重复登录的警告弹窗 (warningConfirm)，则流程直接结束。
 * 4. 如果当前的重复登录次数 (currentReLoginCount) 小于最大允许登录次数 (maxLoginCount)：
 *    - 弹出一个警告框，提示用户检测到重复登录操作，并告知重登超过一定次数会自动交卷，作答期间请勿离开答题页面。
 *    - 用户点击"知道了"后，会调用 `Invoke.exam.postWaningConfirmReLogin` 接口，通知后端用户已确认警告。
 *    - 同时，进行埋点 `BossAnalyticsTrack('zhice-pc-exam-reLogin-dialog-warn')`，记录弹窗事件。
 * 5. 如果当前的重复登录次数达到了最大允许登录次数，并且考试配置中设置了超限强制交卷 (forceSubmitWhenOverLimit)：
 *    - 弹出一个提示框，告知用户重复登录次数超过限制，系统已强制交卷。
 *    - 调用 `commitPaper` 函数，以 `force` 为原因强制交卷。
 */
export const useReLoginFlow = () => {
    const route = useRoute();
    const debuggingStore = useDebuggingStore();

    // 获取重复登录规则配置
    const reLoginRule = debuggingStore.examConfig?.reLoginRule as unknown as ReLoginRule;

    const { reLoginWarning, currentReLoginCount, warningConfirm, maxLoginCount, forceSubmitWhenOverLimit } = reLoginRule || {};

    // 检查是否需要执行重复登录流程
    if (!shouldExecuteReLoginFlow(reLoginWarning, currentReLoginCount, warningConfirm)) {
        return;
    }

    // 执行重复登录流程
    executeReLoginFlow(currentReLoginCount, maxLoginCount, forceSubmitWhenOverLimit, route);
};
