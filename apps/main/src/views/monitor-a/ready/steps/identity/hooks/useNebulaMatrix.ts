import NebulaMatrix from '@nebula/bodydetect-web';
import { debounce } from 'lodash-es';
import { onUnmounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useDebuggingStore } from '../../../../store';
import { useMonitorStore } from '@/store/use-monitor-store';

const faceFrontalArr = [10000]; // 正脸code集

export function useNebulaMatrix(onRecordSuccess: (obj: { file: any; screenshot: string }) => void, onCollectIn30SError: () => void) {
    const debugStore = useDebuggingStore();
    const monitorStore = useMonitorStore();
    // 初始化模型
    const nebulaMatrix = new NebulaMatrix();

    const hintMessage = ref(''); // 活体检测异常的提示信息
    const isCollecting = ref(false); // 是否在进行有效录制(正脸采集中)
    let detectCode = -1; // 活体检测状态code码

    const countDown = ref(3);
    const resetCoundown = () => {
        countDown.value = 3;
    };

    const effectivePeriod = 30 * 1000; // 30s
    let detect30sTimer: any = null;
    const start30sTimer = () => {
        detect30sTimer = setTimeout(() => {
            if (onCollectIn30SError && typeof onCollectIn30SError === 'function') {
                onCollectIn30SError();
            }
            // 抛出异常，终止正常流程
            try {
                clearTimer();
                if (isCollecting.value) {
                    stopRecorder();
                }
                destory();
            } catch (error) {
                logger.error(error);
            }
        }, effectivePeriod);
    };
    const clear30sTimer = () => {
        if (detect30sTimer) {
            clearTimeout(detect30sTimer);
        }
        detect30sTimer = null;
    };

    const initNebulaMatrix = async () => {
        start30sTimer();
        detectCode = -1;
        isCollecting.value = false;

        if (!debugStore.antiCheatStatus.nebulaLoaded) {
            await NebulaMatrix.onRuntimeInitialized(); // 判断模型是否加载完成
            debugStore.antiCheatStatus.nebulaLoaded = true;
        }
        await nebulaMatrix.createBodyDetectStream({
            id: 'nebular',
        });
    };

    const $route = useRoute();
    const savefaceRecognizeException = (data: { exceptionType: number; exceptionDetailType: number }) => {
        try {
            const params = {
                examId: $route.params.examId as string,
                examineeId: monitorStore.examBaseInfo.encryptUserId,
                ...data,
            };
            // exceptionType 异常类型: 2 超时  3录制失败
            // exceptionDetailType 异常详细类型: 5 人脸验证超时  7视频录制失败 8 摄像头错误 9 摄像头权限被禁用
            Invoke.exam.postFaceRecognizeExceptionSave(params);
        } catch (error) {
            logger.error(error);
        }
    };
    // 监听回调
    // 1.活体检测SDK浏览器不支持
    nebulaMatrix.on('error', (error: any) => {
        logger.error('浏览器不支持活体检测', error.message, error.code);
    });
    // 2.初始化时，用户已经禁用摄像头权限
    nebulaMatrix.on('camer-mediaError', (error: any) => {
        savefaceRecognizeException({
            exceptionType: 3,
            exceptionDetailType: 8,
        });
        logger.error('初始化时，用户已经禁用摄像头权限');
    });
    // 3.用户中途禁用摄像头权限
    nebulaMatrix.on('camera-unauthorized', (error: any) => {
        savefaceRecognizeException({
            exceptionType: 3,
            exceptionDetailType: 9,
        });
        logger.error('用户中途禁用摄像头权限');
    });
    // 4.视频录制失败
    nebulaMatrix.on('record-createError ', (error: any) => {
        savefaceRecognizeException({
            exceptionType: 3,
            exceptionDetailType: 7,
        });
        logger.error('视频录制失败');
    });

    // 录制结束的回调
    nebulaMatrix.on('recorder_result', () => {
        if (countDown.value === 0) {
            const file = new File([nebulaMatrix.recorderChunks[nebulaMatrix.recorderChunks.length - 1]], 'demo.mp4', { type: 'video/mp4' });
            const screenshot = nebulaMatrix.getCurrentFrameImage();
            clear30sTimer();
            nebulaMatrix.destroy();
            if (onRecordSuccess && typeof onRecordSuccess === 'function') {
                onRecordSuccess({ file, screenshot });
            }
        }
    });

    // 开始录制
    const startRecorder = () => {
        isCollecting.value = true;
        nebulaMatrix.startRecorder();
    };
    // 停止录制
    const stopRecorder = () => {
        isCollecting.value = false;
        nebulaMatrix.stopRecorder();
    };

    let timer: null | number = null;
    const clearTimer = () => {
        if (timer) {
            window.clearTimeout(timer);
            timer = null;
        }
    };
    const countDownMinus1 = () => {
        return new Promise<void>((resolve, reject) => {
            timer = window.setTimeout(() => {
                countDown.value -= 1;
                hintMessage.value = `识别中…${countDown.value}s`;
                resolve();
            }, 1000);
        });
    };
    const doCountDown = async () => {
        if (countDown.value > 0) {
            await countDownMinus1();
            doCountDown();
        } else {
            stopRecorder();
        }
    };

    const debouncedReportFace = debounce(() => {
        BossAnalyticsTrack('zhice-pc-exam-identity-video-Tips', {
            p3: 1,
            pData: {
                nameZh: '人脸录制-被提示露出正脸',
            },
        });
    }, 1000);

    // 活体检测code
    nebulaMatrix.on('detect_result', (code: number) => {
        // 当前是否为正脸
        const isFrontalNow = faceFrontalArr.includes(code);
        // 上一次检测是否为正脸
        const isFrontalBefore = faceFrontalArr.includes(detectCode);

        if (!isFrontalBefore && isFrontalNow) {
            resetCoundown();
            clearTimer();
            startRecorder();
            hintMessage.value = `识别中…${3}s`;
            doCountDown();
        } else if (isFrontalBefore && isFrontalNow) {
            // 录制过程中，无变化
        } else {
            clearTimer();
            hintMessage.value = '请露出正脸';
            stopRecorder();
            debouncedReportFace();
        }
        detectCode = code;
    });

    const destory = () => {
        try {
            if (monitorStore.nebulaLoaded && nebulaMatrix.status !== 'stop') {
                nebulaMatrix.destroy();
            }
        } catch (error) {
            logger.error(error);
        }
    };

    onUnmounted(() => {
        clearTimer();
        clear30sTimer();
        destory();
    });

    return {
        isCollecting,
        hintMessage,

        initNebulaMatrix,
    };
}
