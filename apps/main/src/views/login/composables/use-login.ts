import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useMonitorStore } from '@/store/use-monitor-store';
import { <PERSON>ie } from '@crm/exam-utils';
import { useBroadcastChannel } from '@crm/exam-hooks';
import { STUDENT_BROADCAST_CHANNEL } from '@crm/exam-constants';
import { useTrackableFlow } from '@/store/track/use-trackable-flow';
import type { LoginParams, LoginResponse } from '../types';
import Cookies from 'js-cookie';

export function useLogin() {
    const isLoading = ref(false);
    const loadingSuccess = ref(false);
    const btnText = computed(() => (loadingSuccess.value ? '正在加载资源...' : '立即进入'));

    const route = useRoute();
    const router = useRouter();
    const monitorStore = useMonitorStore();
    const { post } = useBroadcastChannel(STUDENT_BROADCAST_CHANNEL);
    const { seqId: encryptExamId = '' } = route.query;

    const handleLoginSuccess = (data: LoginResponse['data']) => {
        // 本地开发 自己种Cookie
        if (import.meta.env?.VITE_DEPLOY_ENV === 'dev') {
            Cookie.set('t_kanjian_exam_web', data?.token);
        }

        router.replace(`/exam-list/${encryptExamId}`);

        // 广播告诉其他页面，有新登录了。其他页签可以退出登录了
        post(Date.now());

        Toast.success({
            content: '登录成功！',
        });

        // 登录成功标识
        loadingSuccess.value = true;
    };

    const handleLoginError = (message: string) => {
        isLoading.value = false;
        Toast.danger({
            content: message,
        });
    };

    // 包装登录流程
    const { execute: executeLoginFlow } = useTrackableFlow<[LoginParams, any], { success: boolean; data?: any; message?: string }>(
        '用户登录尝试流程',
        async ({ logStep }, params, options) => {
            isLoading.value = true;
            try {
                logStep('表单验证', { mobile: params.mobile });

                logStep('调用登录API', { mobile: params.mobile });
                const { code, data, message } = await Invoke.login.postLogin(params, options);

                if (code === 0) {
                    logStep('登录成功', { mobile: params.mobile });

                    setAPMcustomUid();

                    if (import.meta.env.DEV) {
                        Cookies.set('t_kanjian_exam_web', data.token);
                    }
                    handleLoginSuccess(data);
                    return { success: true, data };
                } else {
                    logStep('登录失败', { status: 'failure', mobile: params.mobile, errorMsg: message });
                    handleLoginError(message);
                    return { success: false, message };
                }
            } catch (error: any) {
                logStep('登录请求异常', { status: 'failure', mobile: params.mobile, error: error.message });
                handleLoginError(error.message);
                return { success: false, message: error.message };
            } finally {
                isLoading.value = false;
            }
        },
        {
            transformArgsForLogging: ([params]) => {
                // 脱敏处理，不记录完整手机号和验证码
                const { mobile, smsCaptcha, ...rest } = params;
                return [
                    {
                        mobile: mobile ? `${mobile.substring(0, 3)}****${mobile.substring(7)}` : '',
                        hasSmsCode: !!smsCaptcha,
                        ...rest,
                    },
                ];
            },
        },
    );

    // 登录方法现在使用可追踪流程
    const login = async (params: LoginParams, options: any) => {
        return executeLoginFlow(params, options);
    };

    // 发送短信验证码的可追踪流程
    const { execute: executeGetSmsCodeFlow } = useTrackableFlow(
        '请求短信验证码流程',
        async ({ logStep }, params, options) => {
            logStep('调用短信API', { mobile: params.mobile });

            try {
                const res = await Invoke.login.postLoginCaptchaSms({ encryptExamId, ...params }, options);
                if (res.code === 0) {
                    logStep('发送验证码成功', { mobile: params.mobile });
                } else {
                    logStep('发送验证码失败', { status: 'failure', mobile: params.mobile, errorMsg: res?.message });
                    Toast.danger({
                        content: res.message,
                    });
                }
                return res;
            } catch (err: any) {
                logStep('发送验证码请求异常', { status: 'failure', mobile: params.mobile, error: err?.message });
                Toast.danger({
                    content: '发送验证码请求异常，请稍后重试',
                });
                throw err;
            }
        },
        {
            transformArgsForLogging: ([params]) => {
                // 脱敏处理
                const { mobile, ...rest } = params;
                return [
                    {
                        mobile: mobile ? `${mobile.substring(0, 3)}****${mobile.substring(7)}` : '',
                        ...rest,
                    },
                ];
            },
        },
    );

    return {
        isLoading,
        loadingSuccess,
        btnText,
        encryptExamId: encryptExamId as string,
        login,
        executeGetSmsCodeFlow,
    };
}
