.phone-panel {
    display: flex;
    justify-content: space-between;
    .guid-box {
        width: 540px;
        height: 352px;
        padding: 20px 35px 0 24px;
        background: #ffffff;
        border-radius: 12px;
        border: 1px solid #f0f1f2;
        .title {
            display: flex;
            align-items: center;
            height: 20px;
            h3 {
                margin-left: 4px;
                font-weight: 500;
                color: #1f384c;
            }
        }
        .guid-content {
            display: flex;
            margin-top: 14px;
            .desc {
                display: flex;
                flex-wrap: wrap;
                flex: 1;
                padding-top: 12px;
                dl {
                    display: block;
                    &:nth-child(odd) {
                        width: 190px;
                        white-space: nowrap;
                    }
                    margin-bottom: 24px;
                    dt {
                        margin-bottom: 7px;
                        line-height: 20px;
                        font-size: 14px;
                        font-weight: 500;
                        color: #1f1f1f;
                    }
                    dd {
                        line-height: 22px;
                        font-size: 12px;
                        color: #4d4d4d;
                    }
                }
            }
            .example {
                width: 130px;
                text-align: center;
                .gallery-name {
                    line-height: 17px;
                    margin-top: 9px;
                    font-size: 12px;
                    color: #808080;
                }
            }
        }
    }
    .phone-box {
        flex-shrink: 0;
        .phone-box-inner {
            width: 264px;
            height: 352px;

            border-radius: 12px;
            overflow: hidden;
            &.default {
                background: #ecf9f9;
                text-align: center;
                .qr-code-box {
                    width: 161px;
                    height: 161px;
                    padding-top: 21px;
                    background: url(./images/qrcode-bg.png) no-repeat;
                    background-size: 100% 100%;
                    margin: 73px auto 0;
                    canvas {
                        display: block;
                        width: 120px !important;
                        height: 120px !important;
                        margin: 0 auto;
                    }
                }
                .tip {
                    margin-top: 30px;
                    font-size: 14px;
                    font-weight: 500;
                    color: #1f1f1f;
                    line-height: 20px;
                }
            }
            &.success {
                background: #ecf9f9;
            }
            &.error {
                background: #ffeae8;
                padding-top: 40px;
                .error-title {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 20px;
                    h3 {
                        line-height: 20px;
                        margin-left: 4px;
                        font-size: 14px;
                        font-weight: 500;
                        color: #1f1f1f;
                    }
                }
                .error-reason {
                    margin-top: 12px;
                    line-height: 18px;
                    font-size: 12px;
                    color: #4d4d4d;
                    text-align: center;
                }
                canvas {
                    display: block;
                    width: 120px !important;
                    height: 120px !important;
                    margin: 24px auto;
                }
            }
        }
    }
}
