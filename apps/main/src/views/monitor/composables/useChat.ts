import { ref, computed } from 'vue';
import { useMonitorStore } from '@/store/use-monitor-store';
import { chatService } from '@/utils/socket';
import { useTrackableFlow } from '@/store/track/use-trackable-flow';

import { emitter, formatDate } from '@crm/exam-utils';
import { useExamActions } from './useExamActions';
import { useRouteParamId } from './useRouteParamId';

export function useChat() {
    const { encryptExamId, seqId } = useRouteParamId();
    const monitorStore = useMonitorStore();
    const showChat = ref(false);

    const hasChat = computed(() => {
        const { hasInvigilator } = monitorStore.examBaseInfo.examInfo || {};
        const isExamMode = monitorStore.ExamMode === 'EXAM_MODE';
        const chatEnabled = hasInvigilator && monitorStore.examConfig.openCommunicateInlet && isExamMode;
        return chatEnabled;
    });

    function toggleChat(value: boolean, from?: string) {
        showChat.value = value;
        if (from === 'entrance') {
            BossAnalyticsTrack('zhice-pc-exam-chat-trigger', {
                p1: Number(value),
            });
        }
    }

    const { execute: executeInitMqtt } = useTrackableFlow('MQTT 初始化', async (ctx) => {
        // Ensure necessary info is available before connecting
        const examId = encryptExamId || seqId;
        if (examId && monitorStore.examBaseInfo.mobile && monitorStore.examBaseInfo.seqInfo?.wsConnectSecret) {
            ctx.logStep('MQTT 连接参数校验通过', { examId });
            chatService.connect({
                examId,
                encryptMobile: monitorStore.examBaseInfo.mobile,
                wsConnectSecrect: monitorStore.examBaseInfo.seqInfo?.wsConnectSecret,
            });
            ctx.logStep('chatService.connect 已调用');
        } else {
            ctx.logStep('MQTT 连接参数校验失败', {
                examId,
                mobile: !!monitorStore.examBaseInfo.mobile,
                wsConnectSecret: !!monitorStore.examBaseInfo.seqInfo?.wsConnectSecret,
            });
            logger.error('MQTT connection prerequisites not met.');
            // Potentially handle this error more gracefully, e.g., by notifying the user or logging.
        }
    });

    const { execute: executeRemoveMqtt } = useTrackableFlow('MQTT 清理', async (ctx) => {
        try {
            chatService?.disconnect();
            ctx.logStep('chatService.disconnect 已调用');
        } catch (error: any) {
            ctx.logStep('MQTT断开连接时发生错误', { status: 'failure', error: error.message });
            logger.error('Error disconnecting MQTT:', error);
        }
    });

    // mitt相关
    function onReceiveMessage(res: any) {
        const { commitPaper } = useExamActions();
        // 统一封窗弹窗提示
        function showTipDialog({ title, content }: { title: string; content: string }) {
            Dialog.open({
                title,
                content,
                type: 'info',
                showCancel: false,
                confirmText: '知道了',
            });
        }

        const monitorStore = useMonitorStore();
        const { proType, messages = [] } = res;
        const messgae = messages[0] || {};
        // 用来判断是监考官发送给我的消息
        const isMine = messgae.to?.userId === monitorStore.examBaseInfo.encryptUserId;
        if (!isMine) {
            return;
        }

        if (proType === 17) {
            // 17: 考前准备结束
            // 关闭RTC
            monitorStore.closeRtcAll(encryptExamId || seqId);
            const startTime = formatDate(monitorStore.examBaseInfo.seqInfo?.seqStartTime, 'HH:mm:ss');
            Dialog.open({
                title: '考前准备结束',
                content: `考前准备截止24点结束，感谢您的时间！请于明天${startTime}准时前来考试，预祝您一切顺利～`,
                layerClosable: false,
                type: 'warning',
                showConfirm: false,
                cancelText: '关闭',
                enableEscClose: false,
                showClose: false,
                beforeCancel: () => {
                    window.location.reload();
                    return false;
                },
            });
        } else if (proType === 4) {
            showTipDialog({
                title: '系统公告',
                content: messgae.text,
            });
        } else if (proType === 7) {
            showTipDialog({
                title: '监考官提醒',
                content: messgae.text,
            });
        } else if (proType === 2 && monitorStore.step !== 5) {
            showTipDialog({
                title: '强制交卷',
                content: '检测到您存在作弊行为，系统已强制交卷',
            });
            commitPaper(encryptExamId, '检测到作弊行为，系统已强制交卷');
        } else if (proType === 16) {
            // V组流事件回调通知
            try {
                // 解析JSON消息
                const eventData = JSON.parse(messgae.text || '{}');
                // 如果是手机第二视角相关消息
                if (eventData.reasonDesc === '正常进房') {
                    // 尝试重新连接第二视角
                    monitorStore.openPhoneMonitor().catch((err) => {});
                }
            } catch (error) {}
        }
    }
    const receiveMessageEmitter = () => emitter.on('receiveMessage', onReceiveMessage);
    const removeReceiveMessageEmitter = () => emitter.off('receiveMessage', onReceiveMessage);

    return {
        hasChat,
        showChat,
        toggleChat,
        initMqtt: executeInitMqtt,
        removeMqtt: executeRemoveMqtt,
        receiveMessageEmitter,
        removeReceiveMessageEmitter,
    };
}
