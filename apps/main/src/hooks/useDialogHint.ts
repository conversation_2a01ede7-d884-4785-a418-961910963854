import { useMonitorStore } from '@/store/use-monitor-store';
import { useNetwork } from '@crm/vueuse-pro';
// 一、二、三视角异常提醒
import { watch } from 'vue';
import { useOpenDialogCoolDown } from './useOpenDialogCoolDown';

const monitorStore = useMonitorStore();
const { isSupported, isOnline } = useNetwork();

export default function () {
    const { visible: dialogVisibleMonitorHintCamera, openDialogWithCoolDown: openDialogWithCoolDownCamera } = useOpenDialogCoolDown();
    const { visible: dialogVisibleMonitorHintPhone, openDialogWithCoolDown: openDialogWithCoolDownPhone } = useOpenDialogCoolDown();
    const { visible: dialogVisibleMonitorHintScreen, openDialogWithCoolDown: openDialogWithCoolDownScreen } = useOpenDialogCoolDown();
    let timerScreen = -1;
    let timerPhone = -1;
    let timerMedia = -1;
    function dialogClosed(type: string) {
        switch (type) {
            case 'screen':
                if (!isSupported.value || (isSupported.value && isOnline.value)) {
                    timerScreen = window.setTimeout(
                        () => {
                            if (monitorStore.STATUS.screen.status !== 2) {
                                dialogVisibleMonitorHintScreen.value = true;
                            }
                        },
                        5 * 60 * 1000,
                    );
                }
                break;
            case 'phone':
                if (!isSupported.value || (isSupported.value && isOnline.value)) {
                    timerPhone = window.setTimeout(
                        () => {
                            if (monitorStore.STATUS.phone.status !== 2) {
                                dialogVisibleMonitorHintPhone.value = true;
                            }
                        },
                        5 * 60 * 1000,
                    );
                }
                break;
            case 'media':
                if (!isSupported.value || (isSupported.value && isOnline.value)) {
                    timerMedia = window.setTimeout(
                        () => {
                            if (monitorStore.STATUS.camera.status !== 2) {
                                dialogVisibleMonitorHintCamera.value = true;
                            }
                        },
                        5 * 60 * 1000,
                    );
                }
                break;

            default:
                break;
        }
    }

    // 0未开启 1失败 2成功

    // 摄像头
    watch(
        () => monitorStore.STATUS.camera.status,
        (status) => {
            if (!monitorStore.hasComputer) {
                return;
            }
            if (status === 1 || status === 0) {
                if (!isSupported.value || (isSupported.value && isOnline.value)) {
                    openDialogWithCoolDownCamera();
                    if (timerMedia) {
                        window.clearTimeout(timerMedia);
                    }
                }
            } else if (status === 2) {
                dialogVisibleMonitorHintCamera.value = false;
                if (timerMedia) {
                    window.clearTimeout(timerMedia);
                }
            }
        },
        {
            immediate: true,
        },
    );

    // 手机
    watch(
        () => monitorStore.STATUS.phone.status,
        (status) => {
            if (!monitorStore.hasPhone) {
                return;
            }
            if (status === 1 || status === 0) {
                if (!isSupported.value || (isSupported.value && isOnline.value)) {
                    openDialogWithCoolDownPhone();
                    if (timerPhone) {
                        window.clearTimeout(timerPhone);
                    }
                }
            } else if (status === 2) {
                dialogVisibleMonitorHintPhone.value = false;
                if (timerPhone) {
                    window.clearTimeout(timerPhone);
                }
            }
        },
        {
            immediate: true,
        },
    );

    // 屏幕共享
    watch(
        () => monitorStore.STATUS.screen.status,
        (status) => {
            if (!monitorStore.hasScreen) {
                return;
            }
            if (status === 1 || status === 0) {
                if (!isSupported.value || (isSupported.value && isOnline.value)) {
                    openDialogWithCoolDownScreen();
                    if (timerScreen) {
                        window.clearTimeout(timerScreen);
                    }
                }
            } else if (status === 2) {
                dialogVisibleMonitorHintScreen.value = false;
                if (timerScreen) {
                    window.clearTimeout(timerScreen);
                }
            }
            // if ([1, 2].includes(status)) {
            //     setTimeout(() => {
            //         screenShareBehaviorDone = true;
            //     }, 500);
            // }
        },
        {
            immediate: true,
        },
    );

    return {
        dialogVisibleMonitorHintCamera,
        dialogVisibleMonitorHintPhone,
        dialogVisibleMonitorHintScreen,
        dialogClosed,
    };
}
