<template>
    <div class="result-content">
        <div v-for="(item, index) in resultList" :key="item.showName" class="result-item">
            <div class="text-wrap">
                <p class="tit">
                    {{ item.showName }}
                </p>
                <p class="number">
                    {{ item.historyList.length < 1 ? '-' : item.value }}
                </p>
            </div>
            <div class="chart" :style="{ width: `${chartW}px`, height: `${chartH}px` }">
                <ResultChart :domId="index" :chartList="item.historyList" :max="item.maxValue" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import ResultChart from './result-chart.vue';

const props = defineProps({
    resultList: {
        type: Array,
        default: () => [],
    },
    chartW: {
        type: Number,
        default: 295,
    },
    chartH: {
        type: Number,
        default: 90,
    },
});
</script>

<style lang="less" scoped>
.result-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .result-item {
        display: flex;

        &:last-child {
            margin-bottom: 0;
        }

        .text-wrap {
            margin-right: 12px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            color: #1f1f1f;
            width: 80px;
            font-size: 14px;
            font-style: normal;
            font-weight: 500;
            line-height: 22px;
            text-align: center;

            .tit {
                width: 60px;
                margin: 0 auto 7px;
            }

            .number {
                font-family: 'Kanzhun';
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px;
                text-align: center;
                margin: 0 auto;
                width: 70px;
                padding: 5px 0;
                background: #f7f7f7;
                border-radius: 4px;
            }
        }

        .chart {
            // width: 295px;
            // height: 126px;
        }
    }
}
</style>
