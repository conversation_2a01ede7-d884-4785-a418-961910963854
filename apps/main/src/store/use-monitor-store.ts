import type { ICameraMonitorRule, IExamDebugInfo } from '@crm/exam-types';
import type { BaseInfoFormData, ExamBaseInfo, ExamConfig, MonitorStatus, UpdatePhoneCameraParams } from './type';
import { STORAGE_KEY_KANJIAN_EXAM_SEQ_ID } from '@/shared';
import { createTimer, timeCenter } from '@/store/time';
import { Storage } from '@crm/exam-utils';
import dayjs from 'dayjs';
import { defineStore } from 'pinia';
import { computed, ref } from 'vue';
import { Media } from './strategy/media';
import { cleanupLocalMonitor, cleanupPhone } from './utils';
import { NEBULA } from '@/types/nebulartc';
import { useTrackableFlow } from '@/store/track/use-trackable-flow';

const examConfigDefault = {
    canDebug: true,
    recognizeFaceConfirm: true,
    personalVerification: true,
    recognizeFaceFailedCount: 0, // 人脸识别失败次数
    systemRecognizeResult: 1, // 系统人脸识别结果：0-未进行；1-成功;2-失败；3-审核中
    recognizeFile: {
        name: '',
        type: '',
        size: 0, // 文件大小 单位字节
        url: '', // 文件url
        uri: '', // 文件相对路径
    },
    computerCameraMonitor: false,
    mobilePerspectiveMonitor: false,
    computerScreenMonitor: false,
    examineeInfoCanModify: false,
    examineeInfoList: [],
    openCommunicateInlet: true,
    monitorRoomId: '',
    cameraMonitoringRule: {} as ICameraMonitorRule,
    switchScreenRule: {
        forceSubmitWhenLevePage: 1,
        maxSwitchCount: 2,
        switchScreenWarning: 1,
        fullScreen: 1,
        warningConfirm: false,
    },
    reLoginRule: {
        maxLoginCount: 1,
        forceSubmitWhenOverLimit: 1,
        reLoginWarning: 1,
        currentReLoginCount: 0,
        warningConfirm: true,
    },
};

const examBaseInfoDefault = {
    encryptUserId: '', // 考生加密id
    userName: '', // 考生姓名
    mobile: '', // 考生加密手机号
    seqInfo: {
        // 大场次信息
        seqId: '', // 大场次加密Id
        seqName: '', // 大场次名称
        seqStartTime: '', // 大场次开始时间
        seqEndTime: '', // 大场次结束时间
        seqRemarks: '', // 大场次作答说明
        canDebug: true, // 是否考前准备
        wsConnectSecret: '', // ws连接密码
    },
    examInfo: {
        // 小考试信息
        answerInstruction: '', // 作答说明
        examType: 1, // 1-考试 2-量表 3-GBA 4-hots
        examId: '', // 加密小考试id
        examName: '', // 小考试名称
        examRemarks: '', // 小考试答题说明
        operateGuide: '', // 小考试（测评独有）操作指引：富文本
        interruptDuration: 0, // 小考试（测评独有）试卷答题过程中 用户中断时长
        questionListType: 1, // 小考试（测评独有）答题列表类型
        sort: 1, // 排序 从1开始
        answerMode: 1, // 作答模式 1定时模式 2即时模式
        examStartTime: '', // 开始作答时间 2023-01-02 14:00:00
        examEndTime: '2023-01-02 14:00:00', // 结束作答时间 2023-01-02 14:00:00,
        questionCount: 0, // 题目数量
        status: 0, // 考试状态：0-待启用；1-待开始；2-考试中；3-已结束
        statusDesc: '考试中', // 考试状态描述
        hasCommitPaper: false, // 是否已交卷:true-已交卷
        remainSeconds: 0, // 考试结束剩余秒数
        adviseDurationStr: '', // 建议作答时长 90分钟
        hasInvigilator: false, // 是否有监考官
        openCountDown: false, // （考试独有）开启倒计时
        paperReadyClickTimeTs: 0, // 考生点击开始作答时间
        adviseAnswerTime: 0, // 建议答题时间
        answerListType: 1, // 答题列表类型 1-答题列表 2-认证能力
        examEndTimeTs: 0, // 考试结束时间戳
        examStartTimeTs: 0, // 考试开始时间戳
    },
};

export const useMonitorStore = defineStore('monitor', () => {
    const hasInitStep = ref(false);
    const step = ref(0);
    const encryptExamId = ref('');
    const isPreview = ref(false);
    const debugInfo = ref<IExamDebugInfo>({
        canDebug: false,
        startTime: '',
        endTime: '',
        prepareCheck: 1,
    });

    const examConfig = ref<ExamConfig>({ ...examConfigDefault });

    const examBaseInfo = ref<ExamBaseInfo>({ ...examBaseInfoDefault });

    const needAntiCheat = ref(false); // 是否配置防作弊功能（1多人脸 2离开 3低头 4左右张望）
    const nebulaLoaded = ref(false); // 是否已经初始化活体检测sdk
    const baseInfoFormData = ref<BaseInfoFormData>({}); // 使用导入的类型

    // 管理三个视角监控状态的集合
    const STATUS = ref<MonitorStatus>({
        // 使用导入的类型
        camera: {
            client: null,
            status: 0,
            videoStatus: 0,
            audioStatus: 0,
            errorText: '',
            videoErrorText: '',
            audioErrorText: '',
            localStream: null,
        },
        phone: {
            client: null,
            status: 0,
            errorText: '',
            remoteStream: null,
            localStream: null,
        },
        screen: {
            client: null,
            status: 0,
            errorText: '',
            localStream: null,
            videoTrack: null,
        },
    });

    const { currentTime } = timeCenter;
    const examEndCountdownTimer = createTimer();

    const seqExamCountdownTimer = createTimer();

    const hasComputer = computed(() => examConfig.value.computerCameraMonitor); // 第一视角
    const hasPhone = computed(() => examConfig.value.mobilePerspectiveMonitor); // 第二视角
    const hasScreen = computed(() => examConfig.value.computerScreenMonitor); // 第三视角
    const ExamMode = computed(() => {
        return examBaseInfo.value.seqInfo?.canDebug ? 'PREPARE_MODE' : 'EXAM_MODE';
    });

    // 添加摄像头细化状态的计算属性，方便UI使用
    const cameraStatus = computed(() => ({
        overall: STATUS.value.camera.status,
        video: STATUS.value.camera.videoStatus,
        audio: STATUS.value.camera.audioStatus,
        videoError: STATUS.value.camera.videoErrorText,
        audioError: STATUS.value.camera.audioErrorText,
        overallError: STATUS.value.camera.errorText,
        // 判断是否只是音频问题
        isOnlyAudioIssue: STATUS.value.camera.videoStatus === 2 && STATUS.value.camera.audioStatus === 1,
        // 判断是否只是视频问题
        isOnlyVideoIssue: STATUS.value.camera.videoStatus === 1 && STATUS.value.camera.audioStatus === 2,
        // 判断音视频是否都正常
        isFullyWorking: STATUS.value.camera.videoStatus === 2 && STATUS.value.camera.audioStatus === 2,
    }));

    // 根据当前时间计算考试状态，依赖于currentTime的计算属性
    const examStatus = computed(() => {
        const examInfo = examBaseInfo.value.examInfo;
        if (!examInfo || !examInfo.examStartTimeTs || !examInfo.examEndTimeTs) {
            return examInfo?.status;
        }

        // 使用时间中心当前时间，这个依赖将使计算属性随时间更新
        const now = dayjs(currentTime.value);
        const startTime = dayjs(examInfo.examStartTimeTs);
        const endTime = dayjs(examInfo.examEndTimeTs);

        let newStatus = examInfo.status;

        // 根据时间计算状态
        if (now.isBefore(startTime)) {
            // 当前时间小于开始时间：状态为1（待开始）
            newStatus = 1;
        } else if (now.isAfter(startTime) && now.isBefore(endTime)) {
            // 当前时间在开始和结束时间之间：状态为2（考试中）
            newStatus = 2;
        } else if (now.isAfter(endTime)) {
            // 当前时间大于结束时间：状态为3（已结束）
            newStatus = 3;
        }

        return newStatus;
    });

    // 更新考试状态
    const updateExamStatus = () => {
        const examInfo = examBaseInfo.value.examInfo;
        const status = examStatus.value;

        // 状态描述映射
        const statusDescMap = {
            0: '待启用',
            1: '待开始',
            2: '考试中',
            3: '已结束',
        };

        // 如果状态有变化，更新状态和描述
        if (examInfo && status !== examInfo.status) {
            examInfo.status = status || 0;
            examInfo.statusDesc = statusDescMap[status as keyof typeof statusDescMap];
        }

        return status;
    };

    // 计算考试状态并更新
    const calculateExamStatus = () => {
        return updateExamStatus();
    };

    const fetchBaseInfo = async (params: { seqId: string; examId: string }) => {
        isPreview.value = false;
        setSeqId(params.seqId);

        const res = await Invoke.common.getBaseInfo(params);

        if (res.code === 0) {
            initBaseInfo(res);
        }

        return res;
    };

    const initBaseInfo = (res: any) => {
        examBaseInfo.value = {
            ...examBaseInfoDefault,
            ...res.data,
        };

        // 如果返回的包含小场次信息
        if (res.data.examInfo) {
            const { examEndTimeTs } = res.data.examInfo;
            seqExamCountdownTimer.start({
                finishTime: () => examEndTimeTs,
            });
        }
    };

    const initExamConfig = (configData: ExamConfig) => {
        examConfig.value = {
            ...examConfigDefault,
            ...configData,
        };
    };

    const fetchPreviewBaseInfo = async (params: { key: string }) => {
        isPreview.value = true;
        const res = await Invoke.preview.paperBase(params);

        if (res.code === 0) {
            initBaseInfo(res);
        }
    };

    const updatePhoneCamera = (params: UpdatePhoneCameraParams) => {
        // 使用导入的类型
        Invoke.exam.postPhoneCameraUpdate(params);
    };

    const updateStatus = (
        type: keyof MonitorStatus, // Use keyof for type safety
        params: {
            status?: number;
            videoStatus?: number;
            audioStatus?: number;
            errorText?: string;
            videoErrorText?: string;
            audioErrorText?: string;
            client?: NEBULA.RtcClient | null; // 使用导入的类型
            localStream?: NEBULA.RtcStream | null; // 使用导入的类型
            remoteStream?: NEBULA.RtcStream | null; // 使用导入的类型
        },
    ) => {
        const target = STATUS.value[type];

        if (params.status !== undefined) {
            target.status = params.status;
        }

        // 处理摄像头的细化状态
        if (type === 'camera') {
            const cameraTarget = target as typeof STATUS.value.camera;

            if (params.videoStatus !== undefined) {
                cameraTarget.videoStatus = params.videoStatus;
            }
            if (params.audioStatus !== undefined) {
                cameraTarget.audioStatus = params.audioStatus;
            }
            if (params.videoErrorText !== undefined) {
                cameraTarget.videoErrorText = params.videoErrorText;
            }
            if (params.audioErrorText !== undefined) {
                cameraTarget.audioErrorText = params.audioErrorText;
            }

            // 根据音视频状态计算整体状态（如果没有显式设置status）
            if (params.status === undefined && (params.videoStatus !== undefined || params.audioStatus !== undefined)) {
                const videoStatus = params.videoStatus !== undefined ? params.videoStatus : cameraTarget.videoStatus;
                const audioStatus = params.audioStatus !== undefined ? params.audioStatus : cameraTarget.audioStatus;

                // 只要视频正常，整体就认为是正常的（因为视频是主要功能）
                if (videoStatus === 2) {
                    cameraTarget.status = 2;
                } else if (videoStatus === 1 || audioStatus === 1) {
                    // 如果视频失败或音频失败，但不影响视频的情况下，状态可以是部分成功
                    cameraTarget.status = videoStatus === 1 ? 1 : 2;
                } else {
                    cameraTarget.status = 0;
                }
            }

            // 组合错误信息
            if (params.videoErrorText !== undefined || params.audioErrorText !== undefined) {
                const videoError = params.videoErrorText !== undefined ? params.videoErrorText : cameraTarget.videoErrorText;
                const audioError = params.audioErrorText !== undefined ? params.audioErrorText : cameraTarget.audioErrorText;

                const errors = [];
                if (videoError) errors.push(`视频: ${videoError}`);
                if (audioError) errors.push(`音频: ${audioError}`);

                cameraTarget.errorText = errors.length > 0 ? errors.join('; ') : '';
            }
        }

        if (params.errorText !== undefined && 'errorText' in target) {
            target.errorText = params.errorText;
        }
        if (params.client !== undefined) {
            target.client = params.client;
        }
        if (params.localStream !== undefined && 'localStream' in target) {
            target.localStream = params.localStream;
        }
        if (params.remoteStream !== undefined && 'remoteStream' in target) {
            // Type assertion needed because target could be CameraStatus or ScreenStatus
            (target as typeof STATUS.value.phone).remoteStream = params.remoteStream;
        }
    };

    const openCameraMonitor = async (isVirtualTest: boolean = false) => {
        if (STATUS.value.camera.status === 2) return;

        const media = new Media();
        await media.openCameraMonitor(isVirtualTest);
    };

    const openPhoneMonitor = async (isVirtualTest: boolean = false) => {
        if (STATUS.value.phone.status === 2) return;

        const media = new Media();
        await media.openPhoneMonitor(isVirtualTest);
    };

    const openScreenMonitor = async (isVirtualTest: boolean = false) => {
        if (STATUS.value.screen.status === 2) return;

        const media = new Media();
        await media.openScreenMonitor(isVirtualTest);
    };

    /**
     * 关闭所有 RTC 连接和流
     * Safely attempts to close all active RTC connections (phone, screen, camera),
     * handling errors for each type independently using imported utils.
     * 使用 useTrackableFlow 包装，以便追踪流程执行过程
     */
    const closeRtcAll = async (encryptExamId: string) => {
        const { execute } = useTrackableFlow<[string], void>('关闭所有监控流程', async ({ logStep }, examId) => {
            logStep('开始关闭监控');
            try {
                logStep('开始关闭手机监控');
                await cleanupPhone(STATUS.value.phone, hasPhone.value);
                logStep('手机监控关闭完成', { status: 'success' });
            } catch (error) {
                logStep('手机监控关闭失败', { status: 'failure', error });
            }

            try {
                logStep('开始关闭屏幕共享');
                await cleanupLocalMonitor('Screen', STATUS.value.screen, hasScreen.value);
                logStep('屏幕共享关闭完成', { status: 'success' });
            } catch (error) {
                logStep('屏幕共享关闭失败', { status: 'failure', error });
            }

            try {
                logStep('开始关闭摄像头');
                await cleanupLocalMonitor(
                    'Camera',
                    STATUS.value.camera,
                    hasComputer.value, // Assuming hasComputer corresponds to camera enabled
                );
                logStep('摄像头关闭完成', { status: 'success' });
            } catch (error) {
                logStep('摄像头关闭失败', { status: 'failure', error });
            }

            // 通知第二视角，进行关闭
            logStep('通知第二视角关闭');
            Invoke.exam.postPhoneCameraUpdate({
                encryptExamId: examId || examBaseInfo.value.seqInfo?.seqId || '',
                status: 0, // 0 means close
            });
            logStep('关闭所有监控完成', { status: 'success' });
        });

        return execute(encryptExamId);
    };

    const setSeqId = (seqId: string) => {
        Storage.set(STORAGE_KEY_KANJIAN_EXAM_SEQ_ID, seqId);
    };

    return {
        examEndCountdown: examEndCountdownTimer,
        hasInitStep,
        step,
        encryptExamId,
        debugInfo,
        examConfig,
        examBaseInfo,
        needAntiCheat,
        nebulaLoaded,
        baseInfoFormData,
        hasComputer,
        hasPhone,
        hasScreen,
        ExamMode,
        STATUS,
        closeRtcAll,
        fetchBaseInfo,
        fetchPreviewBaseInfo,
        updatePhoneCamera,
        updateStatus,
        openCameraMonitor,
        openPhoneMonitor,
        openScreenMonitor,
        seqExamCountdownTimer,
        setSeqId,
        calculateExamStatus,
        examStatus,
        isPreview,
        // cameraStatus,
        initExamConfig,
        initBaseInfo,
    };
});
