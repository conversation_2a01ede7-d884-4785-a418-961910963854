import { useMonitorStore } from '@/store/use-monitor-store';
import { ref } from 'vue';
import { useRouteParamId } from 'apps/main/src/views/monitor/hooks/useRouteParamId';

/**
 * 请求状态枚举
 * 用于跟踪考试信息请求的状态
 */
export enum RequestStatus {
    INITIAL = '初始化, 请求中', // 初始化状态
    SUCCESS = '请求成功', // 请求成功
    FAILURE = '请求失败', // 请求失败
}

/**
 * 全局初始化钩子函数
 *
 * @param $route - 当前路由对象
 * @param $router - 路由器对象
 * @returns 返回请求状态和获取信息的函数
 */
export function useGlobalInit() {
    // 注意: 在实际生产环境中，useMonitorStore应该在函数内部调用，而不是外部
    const monitorStore = useMonitorStore();

    const { encryptExamId: examId, seqId } = useRouteParamId();

    // 解构路由参数
    const { name } = useRoute();

    const $router = useRouter();

    // 考试信息请求状态
    const examInfoRequestStatus = ref<RequestStatus>(RequestStatus.INITIAL);

    /**
     * 获取考试数据(基础信息和配置信息)
     *
     * @returns Promise<[baseInfoRes, configRes]> 包含基础信息和配置的响应
     */
    async function fetchExamData() {
        return Promise.all([Invoke.common.getBaseInfo({ seqId, examId }), Invoke.common.getConfig({ seqId, examId })]);
    }

    /**
     * 初始化监控和水印
     *
     * @param baseInfo - 考试基础信息
     */
    function initMonitoring(baseInfo: any) {
        try {
            // 渲染水印
            // renderWatermark(baseInfo?.userName, baseInfo?.encryptUserId)
        } catch (error) {
            logger.error('监控初始化失败', error);
        }
    }

    /**
     * 检查是否需要重定向路由
     *
     * @param baseInfo - 考试基础信息
     * @returns 是否已重定向
     */
    function handleRouteRedirection(baseInfo: any): boolean {
        // 如果在monitor页面，并且考试已交卷或时间用尽，返回考试列表页
        if (name === 'monitor' && examId) {
            const hasCommitPaper = (baseInfo?.examInfo?.hasCommitPaper as boolean) || baseInfo?.examInfo?.remainSeconds <= 0;

            if (hasCommitPaper) {
                $router.replace(`/exam-list/${seqId}`);
                return true;
            }
        }
        return false;
    }

    /**
     * 处理成功响应
     *
     * @param baseInfoRes - 基础信息响应
     * @param configRes - 配置信息响应
     * @returns 是否处理成功
     */
    function handleSuccessResponse(baseInfoRes: any, configRes: any): { isSuccess: boolean; isRedirected: boolean } {
        // 两个请求都成功
        if (baseInfoRes?.code === 0 && configRes?.code === 0) {
            const baseInfo = baseInfoRes.data;
            const config = configRes.data;

            // 更新存储
            monitorStore.initBaseInfo(baseInfoRes);
            monitorStore.initExamConfig(config);

            // 初始化监控和水印
            initMonitoring(baseInfo);

            // 处理可能的路由重定向
            const redirected = handleRouteRedirection(baseInfo);

            // 设置请求状态为成功
            if (!redirected) {
                examInfoRequestStatus.value = RequestStatus.SUCCESS;
            }

            return { isSuccess: true, isRedirected: redirected };
        } else if (configRes?.code === 600) {
            // 特殊状态码处理
            $router.replace(`/exam-list/${seqId}`);
            return { isSuccess: true, isRedirected: true };
        }

        return { isSuccess: false, isRedirected: false };
    }

    /**
     * 获取考试或场次信息
     * 发起API请求获取考试基础信息和配置信息
     */
    async function fetchSeqOrExamInfo() {
        try {
            // 获取考试数据
            const [baseInfoRes, configRes] = await fetchExamData();

            // 处理成功响应
            const { isSuccess, isRedirected } = handleSuccessResponse(baseInfoRes, configRes);

            // 如果未成功处理，设置为失败状态
            if (!isSuccess) {
                examInfoRequestStatus.value = RequestStatus.FAILURE;
            }
            return isRedirected;
        } catch (error) {
            // 捕获并处理请求错误
            examInfoRequestStatus.value = RequestStatus.FAILURE;
            logger.error('获取考试信息失败:', error);
            return false;
        }
    }

    function examExit() {
        const monitorStore = useMonitorStore();
        monitorStore.closeRtcAll(examId as string);
    }

    function retry() {
        window.location.reload();
    }

    return {
        examInfoRequestStatus,
        fetchSeqOrExamInfo,
        examExit,
        retry,
    };
}
