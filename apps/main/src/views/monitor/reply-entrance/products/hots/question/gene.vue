<template>
    <div v-if="props.data.inputParamList.length" class="question-option">
        <div class="option-wrap">
            <div v-for="item in optinonList" :key="item.showName" class="option-item">
                <div class="info">
                    <p class="tit">
                        {{ item.showName }}
                    </p>
                    <div
                        class="icon"
                        :class="{
                            checked: !!item.value,
                        }"
                    />
                </div>
                <SwitchBtn v-model="item.value" />
            </div>
        </div>
        <div class="btn-line-wrap">
            <b-button class="start-btn" type="primary" shape="round" @click="onClickMockCommit">
                {{ props.data.mockButtonName }}
            </b-button>
        </div>
        <div v-if="resultList && resultList.length" class="result-wrap">
            <ResultHistoryt :resultList="resultList" :chartH="126" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { throttle } from 'lodash-es';
import ResultHistoryt from '../component/result-history.vue';
import SwitchBtn from '../component/switch-btn.vue';
// import { useBasePaperStore } from '@/store/user/base-paper'
import { useHotsStore } from '../store';

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    resultList: {
        type: Array,
        default: () => [],
    },
    lastInput: {
        type: Array,
        default: () => [],
    },
    isPreview: {
        type: Boolean,
        default: false,
    },
});
const emits = defineEmits(['onMockCommit']);
// const basePaperStore = useBasePaperStore()
const hotsStore = useHotsStore();

const optinonList = ref([]);

const parentData = computed(() => {
    const { currentQuestion, sceneQuestionData } = hotsStore;
    const { parentEncryptId } = currentQuestion;
    return sceneQuestionData[parentEncryptId];
});

// 模拟训练
const onClickMockCommit = throttle(async () => {
    const { currentQuestion } = hotsStore;

    const input = optinonList.value.map((item) => ({
        paramName: item.paramName,
        paramValue: item.value,
    }));

    emits('onMockCommit', {
        // encExamId: basePaperStore.encExamId,
        // encSeqTmpId: basePaperStore.encSeqTmpId,
        questionId: parentData.value.data.id,
        childQuestionId: currentQuestion.id,
        input,
    });
}, 500);

watch(
    () => props.lastInput,
    (list) => {
        // 设置最后一次入参
        if (list.length) {
            list.forEach((item) => {
                const optinonListIndex = optinonList.value.findIndex((element) => {
                    return element.paramName === item.paramName;
                });

                // 赋值入参
                if (optinonListIndex > -1) {
                    optinonList.value[optinonListIndex].value = item.paramValue;
                }
            });
        }
    },
    {
        deep: true,
        immediate: true,
    },
);

watch(
    () => props.data,
    (val) => {
        optinonList.value = props.data.inputParamList.map((item) => {
            return {
                ...item,
                value: 0,
            };
        });
    },
    {
        deep: true,
        immediate: true,
    },
);
</script>

<style lang="less" scoped>
.question-option {
    display: flex;
    justify-content: space-between;

    .option-wrap {
        width: 280px;
        display: flex;
        flex-wrap: wrap;

        .option-item {
            display: flex;
            flex-direction: column;
            margin-bottom: 24px;

            &:nth-child(odd) {
                margin-right: 20px;
            }

            &:nth-last-child(-n + 2) {
                margin-bottom: 0;
            }

            .info {
                margin-bottom: 10px;
                padding-top: 9px;
                width: 123px;
                height: 90px;
                border: 1px solid #dfdfdf;
                border-radius: 8px;
                text-align: center;
                background: #ffffff;

                .tit {
                    color: #1f1f1f;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 22px;
                    text-align: center;
                }

                .icon {
                    margin: 0 auto;
                    width: 84px;
                    height: 42px;
                    background-image: url(https://img.bosszhipin.com/static/file/2024/wxfvr1qab31728702565978.png.webp);
                    background-size: 100% auto;
                    background-position: center 6px;

                    &.checked {
                        background-position: center 0px;
                        background-image: url(https://img.bosszhipin.com/static/file/2024/gbbmslttpb1728702424309.png.webp);
                    }
                }
            }
        }
    }

    .btn-line-wrap {
        position: relative;
        padding: 28px;
        width: 65px;
        display: flex;
        justify-content: center;

        &::before {
            content: '';
            height: 100%;
            width: 1px;
            border-right: 1px dashed #dfdfdf;
        }

        .start-btn {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translateX(-50%) translateY(-50%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            width: 64px;
            height: 64px;
            background: linear-gradient(108.057274deg, #00a5a6 6%, #00bfc0 91%);
            border: none;
            box-shadow:
                inset 1px 1px 3px 0px rgba(255, 255, 255, 0.32),
                2px 2px 4px 0px rgba(0, 168, 169, 0.2),
                -3px -2px 4px 0px #ffffff;
            border-radius: 66px;
            color: #ffffff;
            font-family: 'PingFang SC';
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            text-align: center;
        }
    }

    .result-wrap {
        min-width: 375px;
        display: flex;
        flex-direction: column;
        justify-content: space-around;

        .result-item {
            display: flex;
            margin-bottom: 18px;

            &:last-child {
                margin-bottom: 0;
            }

            .text-wrap {
                display: flex;
                flex-direction: column;
                justify-content: center;
                color: #1f1f1f;
                width: 80px;
                font-size: 14px;
                font-style: normal;
                font-weight: 500;
                line-height: 22px;
                text-align: center;

                .tit {
                    width: 60px;
                    margin: 0 auto 12px;
                    font-family: 'PingFang SC';
                }

                .number {
                    font-family: 'Kanzhun';
                }
            }

            .chart {
                width: 295px;
                height: 126px;
            }
        }
    }
}
</style>
