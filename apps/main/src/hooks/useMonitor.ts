import { useMonitorStore } from '@/store/use-monitor-store';

export default function useMonitor() {
    const monitorStore = useMonitorStore();
    const { hasComputer, hasPhone, hasScreen, STATUS } = monitorStore;
    if (STATUS.camera.status !== 2 && hasComputer) {
        monitorStore.openCameraMonitor();
    }
    if (STATUS.phone.status !== 2 && hasPhone) {
        monitorStore.openPhoneMonitor();
    }
    if (STATUS.screen.status !== 2 && hasScreen) {
        monitorStore.openScreenMonitor();
    }
}
