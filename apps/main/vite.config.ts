/* eslint-disable no-console */
import path from 'node:path';
import process from 'node:process';
import { BossDesignResolver } from '@boss/design-resolver';
import inject from '@rollup/plugin-inject';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import autoImport from 'unplugin-auto-import/vite'; // 自动导入组件
import components from 'unplugin-vue-components/vite'; // 自动导入组件
import { defineConfig } from 'vite';
import { nodePolyfills } from 'vite-plugin-node-polyfills';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import legacy from '@vitejs/plugin-legacy';
import autoprefixer from 'autoprefixer';
import { visualizer } from 'rollup-plugin-visualizer';

const deployVersion = process.env.VITE_DEPLOY_VERSION;

// @ts-ignore
import SetSourceMapFiletToPathPlugin from 'sourcemap-set-path-plugin';
const sourceDir = path.resolve(__dirname, './dist/');
const targetDir = path.resolve(__dirname, './dist/map/');

export default defineConfig(({ command, mode }) => {
    console.log('DEPLOY_ENV:', mode);
    console.log('DEPLOY_VERSION:', deployVersion);

    return {
        plugins: [
            {
                ...SetSourceMapFiletToPathPlugin({ sourceDir, targetDir }),
                enforce: 'post',
                apply: 'build',
            },
            vue(),
            inject({
                Invoke: path.resolve(process.cwd(), 'src/services/invoke.ts'),
                postLog: path.resolve(process.cwd(), 'src/utils/post-log.ts'),
            }),
            vueJsx(),
            legacy({
                targets: ['Chrome >= 49', 'Android >= 4.4', 'iOS >= 10.3', 'Samsung >= 4'],
                additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
                modernPolyfills: true,
            }),
            createSvgIconsPlugin({
                iconDirs: [path.resolve(process.cwd(), 'src/assets/svg')],
                symbolId: 'icon-[dir]-[name]',
            }),
            components({
                resolvers: [BossDesignResolver()],
            }),
            autoImport({
                imports: ['vue', 'vue-router', 'pinia'],
                dirs: ['src/components', 'src/utils', 'src/hooks'],
                eslintrc: { enabled: true, filepath: './.eslintrc-auto-import.json', globalsPropValue: 'readonly' },
                resolvers: [BossDesignResolver({ autoImport: true })],
            }),
            nodePolyfills({
                protocolImports: true,
            }),
            visualizer({
                open: true, // 构建完成后自动打开报告
                filename: 'bundle-report.html', // 报告文件名
                gzipSize: true, // 显示 gzip 压缩后的大小
                brotliSize: true, // 显示 brotli 压缩后的大小
            }),
        ],
        base: '/', // 打包路径
        define: {
            DEPLOY_VERSION: deployVersion,
        },
        resolve: {
            alias: {
                '@': path.resolve(__dirname, 'src'),
                '~@': path.resolve(__dirname, './src'),
            },
            extensions: ['.js', '.ts'], // 不建议忽略自定义导入类型的扩展名（例如：.vue）
        },
        build: {
            sourcemap: true,
            assetsDir: 'static', // 静态资源目录
            assetsInlineLimit: 1024, // 小于1kb的资源内联
            rollupOptions: {
                onwarn(warning, warn) {
                    if (warning.code === 'EVAL' && warning.id?.includes('node_modules')) {
                        return;
                    }
                    warn(warning);
                },
            },
        },
        css: {
            postcss: {
                plugins: [
                    // 自动添加浏览器前缀

                    autoprefixer(),
                ],
            },
        },
        server: {
            host: true,
            cors: true,
            open: true, // 服务启动时自动在浏览器中打开应用
            port: 8080,
            proxy: {
                '/wapi': {
                    target: 'https://kaoshi-qa.weizhipin.com',
                    // target: 'https://kaoshi-rd.weizhipin.com',
                    // target: 'https://pre-kaoshi.zhipin.com',
                    changeOrigin: true,
                    secure: true,
                },
                '/mapi': {
                    target: 'https://kaoshi-qa.weizhipin.com',
                    // target: 'https://kaoshi-rd.weizhipin.com',
                    // target: 'https://pre-kaoshi.zhipin.com',
                    changeOrigin: true,
                    secure: true,
                    // rewrite: (path: string) => path.replace(/^\/wapi/, ""),
                },
            },
        },
    };
});
