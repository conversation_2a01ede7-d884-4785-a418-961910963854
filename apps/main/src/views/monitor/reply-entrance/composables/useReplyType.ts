import { computed } from 'vue';
import { useMonitorStore } from '@/store/use-monitor-store';

export enum ReplayTypeEnum {
    Exam = 1,
    Evaluation = 2,
    Gba = 3,
    Hots = 4,
}

export function useReplyType() {
    const monitorStore = useMonitorStore();
    const type = computed(() => monitorStore.examBaseInfo.examInfo?.examType || ReplayTypeEnum.Exam);
    const isExam = computed(() => type.value === ReplayTypeEnum.Exam);
    const $router = useRouter();
    const $route = useRoute();
    const componentsMap: Record<ReplayTypeEnum, string> = {
        [ReplayTypeEnum.Exam]: `/monitor/exam/${$route?.params?.seqId}?examId=${$route?.params?.examId}`,
        [ReplayTypeEnum.Evaluation]: `/monitor/evaluation/${$route?.params?.seqId}`,
        [ReplayTypeEnum.Gba]: `/monitor/gba/${$route?.params?.seqId}`,
        [ReplayTypeEnum.Hots]: `/monitor/hots/${$route?.params?.seqId}`,
    };

    $router.push(componentsMap[type.value as ReplayTypeEnum]);

    return {
        isExam,
        ReplayTypeEnum, // Exporting enum for potential use elsewhere if needed directly
    };
}
