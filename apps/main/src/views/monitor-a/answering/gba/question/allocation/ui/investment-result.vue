<template>
    <div v-show="localModelValue" class="investment-container">
        <div class="mask-container" />
        <div ref="resultContentContainerRef" :style="{ 'background-image': resultAttrSet.bg }" class="result-content-container">
            <div
                v-show="[TypeEnum.成功, TypeEnum.失败].includes(props.type)"
                ref="resultContainerInnerRef"
                :style="{ 'row-gap': resultAttrSet.rowGap }"
                class="result-container-inner"
            >
                <div class="result-info">
                    <img :src="resultAttrSet.img" alt="" />
                    <span>{{ resultAttrSet.title }}</span>
                </div>
                <div v-if="props.type === TypeEnum.成功" class="result-count">
                    <img alt="" src="https://img.bosszhipin.com/static/file/2024/k0fq809j501726725553854.png.webp" />
                    <Text
                        :key="reward"
                        :font-weight="600"
                        :shadowFilterConfig="{ x: 0, y: 0.87, blur: 0.87, color: 'rgba(218, 79, 12, 0.70)' }"
                        :stroke-width="1"
                        :text="reward"
                        font-size="36px"
                        strokeColor="#FDF1BD"
                        textColor="#894D00"
                    />
                </div>
                <div class="result-ppl">
                    <span style="margin-right: 5px">要求人数</span><span style="margin-right: 12px">{{ successMinParticipant }}</span
                    ><span style="margin-right: 5px">参与人数</span><span>{{ realityParticipant }}</span>
                </div>
            </div>
            <AssignProfit
                v-show="![TypeEnum.成功, TypeEnum.失败].includes(props.type)"
                :key="props.type"
                ref="assignProfitRef"
                :assignProfitAnimationEnd="assignProfitAnimationEnd"
                :distributeTime="distributeTime"
                :gamePlayers="gamePlayers"
                :reward="reward"
                :rewardMap="rewardMap"
                :type="type"
                @allocationSubmit="allocationSubmit"
                @roundCountdownOnFinished="roundCountdownOnFinished"
            />
        </div>
    </div>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue';
import AssignProfit from './assign-profit.vue';
import { gsap } from 'gsap';
import { computed, ref, watch } from 'vue';
import Text from '../../../component/text.vue';

defineOptions({
    name: 'InvestmentResult',
});
const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false,
    },
    type: {
        type: String as PropType<TypeEnum>,
        default: TypeEnum.成功,
    },
    successMinParticipant: {
        type: Number,
        default: 0,
    },
    realityParticipant: {
        type: Number,
        default: 0,
    },
    reward: {
        type: Number,
        default: 0,
    },
    distributeTime: {
        type: Number,
        default: 0,
    },
    rewardMap: {
        type: Object,
        default: () => ({}),
    },
    gamePlayers: {
        type: Array,
        default: () => [],
    },
    /**
     * 回合数据快照
     */
    currentRoundSnapshot: {
        type: Object as PropType<any>,
        default: () => ({}),
    },
});
const emit = defineEmits<{
    roundCountdownOnFinished: [];
    allocationSubmit: [rewardValue: { [key: number]: number }];
    assignStartAppear: [];
    assignProfitAnimationEnd: [];
    dialogOpenAnimationEnd: [];
    dialogCloseAnimationEnd: [type: TypeEnum];
}>();

function allocationSubmit(...rest: any) {
    const t2 = registryDialogCloseAnimation(...rest);
    t2.play(0);
}

enum TypeEnum {
    成功 = 'success',
    失败 = 'failure',
    分配 = 'assign',
}

const resultAttrSet = computed(() => {
    const result: { img?: string; title?: string; rowGap?: string; bg?: string } = {};
    if (props.type === TypeEnum.成功) {
        result.img = 'https://img.bosszhipin.com/static/file/2024/zemfaw5sil1726725553432.png.webp';
        result.title = '恭喜投资成功';
        result.rowGap = '12px';
        result.bg = 'linear-gradient(179.99998deg, #FFC41F 0%, #FDFFEE 96%)';
    } else if (props.type === TypeEnum.失败) {
        result.img = 'https://img.bosszhipin.com/static/file/2024/y0cb3k0gib1726725554833.png.webp';
        result.title = '很遗憾投资失败';
        result.rowGap = '36px';
        result.bg = 'linear-gradient(179.99998deg, #FFA387 0%, #FDFFEE 96%)';
    } else if (props.type === TypeEnum.分配) {
        result.bg = 'linear-gradient(179.99998deg, #FFC41F 0%, #FDFFEE 96%)';
    }
    return result;
});

function roundCountdownOnFinished() {
    if (props.type === 'assign') {
        emit('roundCountdownOnFinished');
    }
}

const assignProfitAnimationEnd = ref(false);
const resultContentContainerRef = ref(); // 大容器
const resultContainerInnerRef = ref(); // 展示结果的小容器
const assignProfitRef = ref(); // 分配收益的内容
function registryDialogOpenAnimation(): gsap.core.Timeline {
    const t1 = gsap.timeline({ paused: true });
    t1.set(resultContentContainerRef.value, { scale: 0, width: 398, height: 235 });
    t1.set(resultContainerInnerRef.value, { opacity: 1 });
    t1.set(assignProfitRef.value.$el, { opacity: 0 });

    t1.to(resultContentContainerRef.value, {
        scale: 1,
        duration: 0.3,
        ease: 'back.out',
    }).call(() => emit('dialogOpenAnimationEnd'), [], '>'); // 大容器弹出

    if ([TypeEnum.失败].includes(props.type)) {
        return t1;
    }
    if ([TypeEnum.成功].includes(props.type) && props.currentRoundSnapshot.playerDistribute === 0) {
        return t1;
    }
    t1.to(resultContainerInnerRef.value, { opacity: 0, duration: 0.3, delay: 2 }, '>'); // 成功文字消失
    t1.to(
        resultContentContainerRef.value,
        {
            width: 780,
            height: 343,
            duration: 0.3,
            ease: 'back.out',
        },
        '>',
    ).call(() => emit('assignStartAppear'), [], '>'); // 弹窗放大，之后通知外部改为分配状态
    t1.to(assignProfitRef.value.$el, { opacity: 1, duration: 0.3 }, '>').call(
        () => {
            emit('assignProfitAnimationEnd');
            assignProfitAnimationEnd.value = true;
        },
        [],
        '>',
    ); // 分配收益的内容出现，之后通知外部
    return t1;
}

function registryDialogCloseAnimation(...rest: any): gsap.core.Timeline {
    const t2 = gsap.timeline({ paused: true });
    t2.to(resultContentContainerRef.value, { scale: 0, duration: 0.3, ease: 'back.in' }).call(
        () => {
            emit('dialogCloseAnimationEnd', props.type);
            if (props.type === TypeEnum.分配) {
                emit('allocationSubmit', ...rest);
            }
            localModelValue.value = false;
        },
        [],
        '>',
    );
    return t2;
}

const localModelValue = ref(false);
watch(
    () => props.modelValue,
    (val) => {
        if (val) {
            localModelValue.value = val;
            const t1 = registryDialogOpenAnimation();
            t1.play(0);
        } else {
            const t2 = registryDialogCloseAnimation();
            t2.play(0);
        }
    },
);

// 透传提示事件
function onTips(...rest: any[]) {
    emit('tips', ...rest);
}
</script>

<style lang="less" scoped>
.investment-container {
    display: flex;
    width: 812px;
    height: 375px;
    justify-content: center;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 11;

    .mask-container {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        background-color: rgba(0, 0, 0, 0.5);
    }

    .result-content-container {
        width: 398px;
        height: 235px;
        border-radius: 16px;
        border: 5px solid #fff;
        position: relative;
        z-index: 1;

        .result-container-inner {
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .base-layout() {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .result-info {
            .base-layout();

            img {
                width: 38px;
                height: 34px;
                margin-right: 6px;
            }

            span {
                color: #894d00;
                font-size: 24px;
                font-weight: 600;
                line-height: 34px;
            }
        }

        .result-count {
            .base-layout();

            img {
                width: 108px;
                height: 86px;
                margin-right: -4px;
            }
        }

        .result-ppl {
            .base-layout();
            color: #894d00;
            font-size: 16px;
            line-height: 19.11px;
        }
    }
}
</style>
