import { useTrackableFlow } from '@/store/track/use-trackable-flow';
import { useMonitorStore } from '@/store/use-monitor-store';
import commitPaper from '@/hooks/useCommitPaper';

/**
 * @description 重复登录弹窗流程 hooks
 * 该流程用于处理用户在考试过程中重复登录的场景。
 * 1. 首先会检查考试配置中是否开启了重复登录警告 (reLoginWarning)。如果未开启，则流程直接结束。
 * 2. 如果当前是首次登录 (currentReLoginCount === 0)，则流程直接结束。
 * 3. 如果用户已经确认过重复登录的警告弹窗 (warningConfirm)，则流程直接结束。
 * 4. 如果当前的重复登录次数 (currentReLoginCount) 小于最大允许登录次数 (maxLoginCount)：
 *    - 弹出一个警告框，提示用户检测到重复登录操作，并告知重登超过一定次数会自动交卷，作答期间请勿离开答题页面。
 *    - 用户点击"知道了"后，会调用 `Invoke.exam.postWaningConfirmReLogin` 接口，通知后端用户已确认警告。
 *    - 同时，进行埋点 `BossAnalyticsTrack('zhice-pc-exam-reLogin-dialog-warn')`，记录弹窗事件。
 * 5. 如果当前的重复登录次数达到了最大允许登录次数，并且考试配置中设置了超限强制交卷 (forceSubmitWhenOverLimit)：
 *    - 弹出一个提示框，告知用户重复登录次数超过限制，系统已强制交卷。
 *    - 调用 `commitPaper` 函数，以 `reLogin` 为原因强制交卷。
 */
export const useReLoginFlow = () =>
    useTrackableFlow('重复登录弹窗流程', (context) => {
        const route = useRoute();
        const router = useRouter();
        const monitorStore = useMonitorStore();
        if (!monitorStore.examConfig.reLoginRule.reLoginWarning) {
            context.logStep('未设置重复登录警告');
            return; // 未设置重复登录警告
        }
        if (monitorStore.examConfig.reLoginRule.currentReLoginCount === 0) {
            context.logStep('第一次登录');
            return; // 第一次登录
        }
        if (monitorStore.examConfig.reLoginRule.warningConfirm) {
            context.logStep('确认过弹窗提醒');
            return; // 确认过弹窗提醒
        }
        if (monitorStore.examConfig.reLoginRule.currentReLoginCount < monitorStore.examConfig.reLoginRule.maxLoginCount) {
            context.logStep('弹出重复登录弹窗');
            Dialog.open({
                title: '重复登录',
                content: '检测到您刚刚有重复登录操作，重登超过一定次数会自动交卷，作答期间请勿离开答题页面！',
                type: 'warning',
                showCancel: false,
                showClose: false,
                confirmText: '知道了',
                layerClosable: false,
                enableEscClose: false,
                close() {
                    Invoke.exam.postWaningConfirmReLogin({
                        encryptExamId: route.params.examId,
                    });
                },
            });
            // 埋点开始
            BossAnalyticsTrack('zhice-pc-exam-reLogin-dialog-warn', {
                pData: {
                    type: TrackTypeEnum.失败,
                    nameZh: '弹出重复登录弹窗',
                },
            });
            // 埋点结束
        } else if (monitorStore.examConfig.reLoginRule.forceSubmitWhenOverLimit) {
            context.logStep('弹出强制交卷弹窗');
            Dialog.open({
                title: '强制交卷',
                content: '检测到您重复登录次数超过限制，系统已强制交卷！',
                type: 'warning',
                showCancel: false,
                confirmText: '知道了',
            });
            commitPaper('reLogin', route, router);
        }
    });
