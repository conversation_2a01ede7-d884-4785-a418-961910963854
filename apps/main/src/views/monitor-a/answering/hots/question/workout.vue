<template>
    <div class="question-option" v-if="props.data.inputParamList.length">
        <div class="option-wrap">
            <div v-for="item in optinonList" class="option-item" :key="item.showName">
                <p class="tit">
                    <span>{{ item.showName }}：</span>
                    <span v-if="item.value" class="val-box">{{ item.value }}</span>
                </p>
                <div class="slider-wrap">
                    <p class="number">{{ item.min }}</p>
                    <div class="slider-content">
                        <b-slider v-if="[...item.inputValue].includes(0.5)" v-model="item.value" showStops :min="item.min" :max="item.max" :step="[...item.inputValue]"></b-slider>
                        <b-slider v-else v-model="item.value" :step="item.max >= 100 ? item.max / 10 : 1" showStops :min="item.min" :max="item.max"></b-slider>
                    </div>
                    <p class="number">{{ item.max }}</p>
                </div>
            </div>
        </div>
        <div class="btn-line-wrap">
            <b-button @click="onClickMockCommit" class="start-btn" type="primary" shape="round">{{ props.data.mockButtonName }}</b-button>
        </div>
        <div class="result-wrap">
            <ResultHistoryt :resultList="resultList"></ResultHistoryt>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { throttle } from 'lodash-es';
// import { useBasePaperStore } from '@/store/user/base-paper';
import { useHotsStore } from '../store';
import ResultHistoryt from '../component/result-history.vue';
import SwitchBtn from '../component/switch-btn.vue';

// const basePaperStore = useBasePaperStore();
const hotsStore = useHotsStore();

const emits = defineEmits(['onMockCommit']);
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    resultList: {
        type: Array,
        default: () => [],
    },
    lastInput: {
        type: Array,
        default: () => [],
    },
    isPreview: {
        type: Boolean,
        default: false,
    },
});

const optinonList = ref([]);

const parentData = computed(() => {
    const { currentQuestion, sceneQuestionData } = hotsStore;
    const { parentEncryptId } = currentQuestion;
    return sceneQuestionData[parentEncryptId];
});

// 模拟训练
const onClickMockCommit = throttle(async () => {
    const { currentQuestion } = hotsStore;

    const input = optinonList.value.map((item) => ({
        paramName: item.paramName,
        paramValue: item.value,
    }));

    emits('onMockCommit', {
        // encExamId: basePaperStore.encExamId,
        // encSeqTmpId: basePaperStore.encSeqTmpId,
        questionId: parentData.value.data.id,
        childQuestionId: currentQuestion.id,
        input,
    });
}, 500);

watch(
    () => props.lastInput,
    (list) => {
        // 设置最后一次入参
        if (list.length) {
            list.forEach((item) => {
                let optinonListIndex = optinonList.value.findIndex((element) => {
                    return element.paramName === item.paramName;
                });

                // 赋值入参
                if (optinonListIndex > -1) {
                    optinonList.value[optinonListIndex].value = item.paramValue;
                }
            });
        }
    },
    {
        deep: true,
        immediate: true,
    },
);

watch(
    () => props.data,
    (val) => {
        optinonList.value = props.data.inputParamList.map((item) => {
            return {
                ...item,
                value: 0,
                min: Number(item.inputValue[0]),
                max: Number(item.inputValue[item.inputValue.length - 1]),
            };
        });
    },
    {
        deep: true,
        immediate: true,
    },
);
</script>

<style lang="less" scoped>
.question-option {
    display: flex;
    justify-content: space-between;

    .option-wrap {
        position: relative;

        .option-item {
            margin-bottom: 8px;
            width: 280px;
            padding: 20px 20px 11px;
            padding-left: 0;

            &:last-child {
                margin-bottom: 0;
            }

            .tit {
                margin-bottom: 11px;
                height: 26px;
                display: flex;
                align-items: center;

                .val-box {
                    margin-left: 5px;
                    min-width: 50px;
                    height: 26px;
                    background: #caebeb;
                    border-radius: 4px;
                    text-align: center;
                    color: #1f1f1f;
                    font-family: 'Kanzhun';
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 24px;
                }
            }

            // 重置滑块选择器样式
            .slider-wrap {
                display: flex;
                align-items: center;

                .slider-content {
                    margin: 0 12px;
                    flex: 1;

                    :deep(.b-slider) {
                        .b-slider-runway {
                            height: 6px;
                            box-shadow: inset 1px 1px 2px 0 rgba(0, 0, 0, 0.2);
                            border-radius: 26px;

                            .b-slider-bar {
                                border-radius: 4px;
                                height: 100%;
                                z-index: 2;
                            }
                        }

                        // 控制球
                        .b-slider-button-wrap {
                            transform: translateX(-50%) translateY(2px);
                            width: 14px;
                            height: 14px;
                            border-radius: 30px;
                            filter: drop-shadow(1px 1px 2px rgba(2, 112, 112, 0.4));
                            background: linear-gradient(108.057274deg, #00a5a6 6%, #00bfc0 91%);

                            .b-slider-button {
                                border: none;
                                width: 8px;
                                height: 8px;
                            }
                        }

                        // 背景分割块
                        .b-slider-stop {
                            height: 100%;
                            width: 9px;
                            border-radius: 0;
                            // position: relative;
                            display: flex;
                            justify-content: space-between;

                            &::after,
                            &::before {
                                width: 4px;
                                content: '';
                                flex: 1;
                                background: rgba(235, 237, 238, 1);
                                opacity: 0.9;
                                // background: red;
                                box-shadow: inset 0 1px 0 0 rgba(0, 0, 0, 0.1);
                                // opacity: 0.9;
                                // padding-left: -1px;
                            }

                            &::after {
                                margin-left: 1px;
                                border-radius: 10px 0 0 10px;
                            }
                            &::before {
                                border-radius: 0 10px 10px 0;
                            }
                        }
                    }
                }
            }
        }
    }

    .btn-line-wrap {
        position: relative;
        padding: 28px;
        width: 65px;
        display: flex;
        justify-content: center;

        &::before {
            content: '';
            height: 100%;
            width: 1px;
            border-right: 1px dashed #dfdfdf;
        }

        .start-btn {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translateX(-50%) translateY(-50%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            width: 64px;
            height: 64px;
            background: linear-gradient(108.057274deg, #00a5a6 6%, #00bfc0 91%);
            border: none;
            box-shadow:
                inset 1px 1px 3px 0px rgba(255, 255, 255, 0.32),
                2px 2px 4px 0px rgba(0, 168, 169, 0.2),
                -3px -2px 4px 0px #ffffff;
            border-radius: 66px;
            color: #ffffff;
            font-family: 'PingFang SC';
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            text-align: center;
        }
    }

    .result-wrap {
        min-width: 375px;
    }
}
</style>
