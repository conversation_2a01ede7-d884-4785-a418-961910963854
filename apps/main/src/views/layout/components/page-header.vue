<template>
    <div class="head-container">
        <div class="head-content-wrap">
            <div class="logo-wrap">
                <img src="https://img.bosszhipin.com/static/zhipin/kanjian/zhice/examinee/19067594215719392.png" alt="" />
            </div>
            <MonitorStatus v-if="debuggingStore.showMonitorStatus" />
            <DebugPanel v-if="isDev" />
            <!-- 用户信息 -->
            <span>{{ monitorStore.examBaseInfo.userName }}</span>
        </div>
    </div>
</template>

<script setup lang="ts">
import MonitorStatus from './monitor-status/index.vue';
import DebugPanel from '@/components/DebugPanel.vue';

import { DEPLOY_ENV } from '@/shared';
import { useDebuggingStore } from '../../monitor-a/store';
import { useMonitorStore } from '@/store/use-monitor-store';

const isDev = DEPLOY_ENV === 'dev' || DEPLOY_ENV === 'rd' || DEPLOY_ENV === 'qa';

const debuggingStore = useDebuggingStore();
const monitorStore = useMonitorStore();

// 1）其他路由不展示 2）考试路由-准备结束 不展示
// const showMonitorStatus = computed(() => {
//     return ($route.name === 'ready' || $route.name === 'monitor') && debuggingStore.hasInitStep && !(monitorStore.examBaseInfo.seqInfo?.canDebug && debuggingStore.step === -4); // -4在多个组件内维护，存在改动异常风险
// });
</script>

<style lang="less" scoped>
.head-container {
    height: 64px;
    background-color: #ffffff;
    .head-content-wrap {
        height: 100%;
        max-width: 1366px;
        margin: 0 auto;
        display: flex;
        align-items: center;
        padding: 0 10px;

        .logo-wrap {
            flex-grow: 1;
            display: flex;
            flex-direction: column;

            .boss-logo {
                margin-bottom: 7px;
            }

            .system-title {
                font-size: 13px;
                letter-spacing: 2px;
            }
            img {
                width: 82px;
                height: 39px;
            }
        }

        .user-wrap {
            display: flex;
            align-items: center;
            cursor: pointer;

            .user-img-wrap {
                border-radius: 50%;
                overflow: hidden;
                width: 30px;
                height: 30px;
                border: 1px solid #ebedf2;
                margin-right: 8px;

                img {
                    display: block;
                    width: 100%;
                    height: 100%;
                }
            }
        }
    }
}
</style>
