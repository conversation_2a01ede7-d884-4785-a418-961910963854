import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { nextTick } from 'vue';
import { createTimeManager } from '../create-time-manager';
import { TimerStatus } from '../types';

const raf = vi.fn((callback: FrameRequestCallback) => {
    const id = setTimeout(() => callback(performance.now()), 16);
    return id as unknown as number;
});

const caf = vi.fn((id: number) => {
    clearTimeout(id);
});

describe('createTimeManager reconfigure 功能', () => {
    beforeEach(() => {
        vi.useFakeTimers();
        vi.clearAllMocks();
        raf.mockClear();
        caf.mockClear();

        global.performance = global.performance || {};
        global.performance.now = vi.fn(() => Date.now());
    });

    afterEach(() => {
        vi.useRealTimers();
        vi.restoreAllMocks();
    });

    describe('基本重新配置场景', () => {
        it('应该在重新配置时返回新的时间中心实例', () => {
            const manager = createTimeManager();
            const originalTimeCenter = manager.timeCenter;

            manager.reconfigure({
                syncTimeFrequency: 30000,
            });

            expect(manager.timeCenter).not.toBe(originalTimeCenter);
            expect(manager.timeCenter).toBeDefined();
            expect(typeof manager.timeCenter.currentTime.value).toBe('number');
        });

        it('应该在重新配置后保持 createTimer 函数的可用性', () => {
            const manager = createTimeManager();

            manager.reconfigure({
                syncTimeFrequency: 30000,
            });

            const timer = manager.createTimer();
            expect(timer).toBeDefined();
            expect(timer.status).toBe(TimerStatus.idled);

            timer.start({});
            expect(timer.status).toBe(TimerStatus.running);
        });

        it('应该正确合并默认配置和新配置', async () => {
            const mockSyncTime = vi.fn().mockResolvedValue(Date.now());

            const manager = createTimeManager({
                syncTimeFrequency: 10000, // 初始配置
            });

            // 重新配置，只指定 syncTime，应该保持默认的 syncTimeFrequency
            manager.reconfigure({
                syncTime: mockSyncTime,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            expect(mockSyncTime).toHaveBeenCalled();
            manager.timeCenter.stopUpdateTime();
        });
    });

    describe('多次重新配置场景', () => {
        it('应该支持连续多次重新配置', () => {
            const manager = createTimeManager();
            const instances = [manager.timeCenter];

            // 连续重新配置 5 次
            for (let i = 0; i < 5; i++) {
                manager.reconfigure({
                    syncTimeFrequency: (i + 1) * 10000,
                });
                instances.push(manager.timeCenter);
            }

            // 验证每次都创建了新的实例
            const uniqueInstances = new Set(instances);
            expect(uniqueInstances.size).toBe(6); // 原始 + 5 次重新配置

            manager.timeCenter.stopUpdateTime();
        });

        it('应该允许旧的时间中心继续运行直到垃圾回收', () => {
            const manager = createTimeManager({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            const originalTimeCenter = manager.timeCenter;
            // 重新配置
            manager.reconfigure({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            const newTimeCenter = manager.timeCenter;

            // 验证创建了新的时间中心实例
            expect(newTimeCenter).not.toBe(originalTimeCenter);

            // 验证新的时间中心是有效的
            expect(typeof newTimeCenter.currentTime.value).toBe('number');
            expect(newTimeCenter.stopUpdateTime).toBeInstanceOf(Function);

            // 手动清理
            originalTimeCenter.stopUpdateTime();
            newTimeCenter.stopUpdateTime();
        });
    });

    describe('计时器独立性测试', () => {
        it('已存在的计时器应该继续使用原来的时间中心', async () => {
            const manager = createTimeManager({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 创建第一个计时器
            const timer1 = manager.createTimer();
            timer1.start({});
            // 保存旧的时间中心引用
            const oldTimeCenter = manager.timeCenter;

            // 推进时间
            await vi.advanceTimersByTimeAsync(1000);
            await nextTick();

            const timer1ElapsedBefore = timer1.elapsedTime.total;
            expect(timer1ElapsedBefore).toBeCloseTo(1000, -2);

            // 重新配置
            manager.reconfigure({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 继续推进时间
            await vi.advanceTimersByTimeAsync(1000);
            await nextTick();

            // timer1 应该继续正常工作
            const timer1ElapsedAfter = timer1.elapsedTime.total;
            expect(timer1ElapsedAfter).toBeCloseTo(2000, -2);
            expect(timer1.status).toBe(TimerStatus.running);

            // 清理新旧时间中心
            oldTimeCenter.stopUpdateTime();
            manager.timeCenter.stopUpdateTime();
        });

        it('新创建的计时器应该使用新的时间中心', async () => {
            const manager = createTimeManager({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 重新配置
            manager.reconfigure({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 创建新计时器
            const timer = manager.createTimer();
            timer.start({});

            await vi.advanceTimersByTimeAsync(500);
            await nextTick();

            expect(timer.elapsedTime.total).toBeCloseTo(500, -2);
            expect(timer.status).toBe(TimerStatus.running);

            manager.timeCenter.stopUpdateTime();
        });

        it('应该支持重新配置后创建多个计时器', async () => {
            const manager = createTimeManager();

            manager.reconfigure({
                syncTimeFrequency: 30000,
            });

            // 创建多个计时器
            const timers = Array.from({ length: 5 }, () => manager.createTimer());

            timers.forEach((timer, index) => {
                timer.start({
                    finishTime: (now) => now + (index + 1) * 1000,
                });
                expect(timer.status).toBe(TimerStatus.running);
            });

            // 验证所有计时器都使用相同的时间基准
            const startTimes = timers.map((timer) => timer.beginTime);
            const maxDiff = Math.max(...startTimes) - Math.min(...startTimes);
            expect(maxDiff).toBeLessThan(100); // 开始时间应该很接近
        });
    });

    describe('同步功能重新配置', () => {
        it('应该在重新配置后使用新的同步函数', async () => {
            const oldSyncTime = vi.fn().mockResolvedValue(1000000);
            const newSyncTime = vi.fn().mockResolvedValue(2000000);

            const manager = createTimeManager({
                syncTime: oldSyncTime,
                syncTimeFrequency: 1000,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 等待初始同步
            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            expect(oldSyncTime).toHaveBeenCalled();
            oldSyncTime.mockClear();

            // 重新配置
            manager.reconfigure({
                syncTime: newSyncTime,
                syncTimeFrequency: 1000,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 等待新的同步
            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            expect(newSyncTime).toHaveBeenCalled();
            expect(oldSyncTime).not.toHaveBeenCalled();

            manager.timeCenter.stopUpdateTime();
        });

        it('应该支持从有同步配置重新配置为无同步配置', async () => {
            const mockSyncTime = vi.fn().mockResolvedValue(1000000);

            const manager = createTimeManager({
                syncTime: mockSyncTime,
                syncTimeFrequency: 1000,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 重新配置为无同步
            manager.reconfigure({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            const timer = manager.createTimer();
            timer.start({});

            await vi.advanceTimersByTimeAsync(1000);
            await nextTick();

            expect(timer.status).toBe(TimerStatus.running);
            expect(timer.elapsedTime.total).toBeCloseTo(1000, -2);

            manager.timeCenter.stopUpdateTime();
        });

        it('应该支持从无同步配置重新配置为有同步配置', async () => {
            const manager = createTimeManager();

            const mockSyncTime = vi.fn().mockResolvedValue(2000000);

            manager.reconfigure({
                syncTime: mockSyncTime,
                syncTimeFrequency: 1000,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            expect(mockSyncTime).toHaveBeenCalled();

            manager.timeCenter.stopUpdateTime();
        });
    });

    describe('错误处理和边界情况', () => {
        it('应该处理重新配置时同步函数的错误', async () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
            const failingSyncTime = vi.fn().mockRejectedValue(new Error('Network error'));

            const manager = createTimeManager();

            manager.reconfigure({
                syncTime: failingSyncTime,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            expect(consoleSpy).toHaveBeenCalledWith('服务器时间同步失败:', expect.any(Error));
            expect(manager.timeCenter).toBeDefined();

            // 验证计时器依然可以正常工作
            const timer = manager.createTimer();
            timer.start({});
            expect(timer.status).toBe(TimerStatus.running);

            manager.timeCenter.stopUpdateTime();
            consoleSpy.mockRestore();
        });

        it('应该处理重新配置时传入无效的 syncTime', async () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
            const invalidSyncTime = vi.fn().mockResolvedValue('invalid-timestamp' as any);

            const manager = createTimeManager();

            manager.reconfigure({
                syncTime: invalidSyncTime,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            expect(consoleSpy).toHaveBeenCalledWith('服务器返回无效时间戳:', 'invalid-timestamp');

            manager.timeCenter.stopUpdateTime();
            consoleSpy.mockRestore();
        });

        it('应该处理传入 null 或 undefined 的重新配置', () => {
            const manager = createTimeManager();

            expect(() => manager.reconfigure(undefined)).not.toThrow();
            expect(() => manager.reconfigure(null as any)).not.toThrow();

            expect(manager.timeCenter).toBeDefined();
            expect(manager.createTimer).toBeTypeOf('function');

            manager.timeCenter.stopUpdateTime();
        });

        it('应该处理极端频率值的重新配置', () => {
            const manager = createTimeManager();

            // 极小频率
            expect(() => manager.reconfigure({ syncTimeFrequency: 1 })).not.toThrow();

            // 极大频率
            expect(() => manager.reconfigure({ syncTimeFrequency: Number.MAX_SAFE_INTEGER })).not.toThrow();

            // 零频率
            expect(() => manager.reconfigure({ syncTimeFrequency: 0 })).not.toThrow();

            manager.timeCenter.stopUpdateTime();
        });
    });

    describe('性能和内存测试', () => {
        it('应该能够处理大量重新配置操作', () => {
            const manager = createTimeManager();

            // 执行大量重新配置
            for (let i = 0; i < 100; i++) {
                manager.reconfigure({
                    syncTimeFrequency: i * 1000,
                });
            }

            expect(manager.timeCenter).toBeDefined();
            expect(manager.createTimer).toBeTypeOf('function');

            manager.timeCenter.stopUpdateTime();
        });

        it('应该在重新配置时创建新的时间中心实例', () => {
            const manager = createTimeManager({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            const timeInstances = [manager.timeCenter];

            // 多次重新配置
            for (let i = 0; i < 5; i++) {
                manager.reconfigure({
                    requestAnimationFrame: raf,
                    cancelAnimationFrame: caf,
                });
                timeInstances.push(manager.timeCenter);
            }

            // 验证每次都创建了新的时间中心实例
            const uniqueInstances = new Set(timeInstances);
            expect(uniqueInstances.size).toBe(6); // 原始 + 5 次重新配置

            // 验证当前时间中心是最新的
            expect(manager.timeCenter).toBe(timeInstances[timeInstances.length - 1]);

            // 清理所有时间中心
            timeInstances.forEach((timeCenter) => timeCenter.stopUpdateTime());
        });

        it('应该不影响已存在计时器的性能', async () => {
            const manager = createTimeManager({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 创建多个计时器
            const timers = Array.from({ length: 10 }, () => {
                const timer = manager.createTimer();
                timer.start({});
                return timer;
            });

            // 保存旧的时间中心引用
            const oldTimeCenter = manager.timeCenter;

            // 推进时间
            await vi.advanceTimersByTimeAsync(1000);
            await nextTick();

            const elapsedBefore = timers.map((timer) => timer.elapsedTime.total);

            // 重新配置
            manager.reconfigure({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 继续推进时间
            await vi.advanceTimersByTimeAsync(1000);
            await nextTick();

            const elapsedAfter = timers.map((timer) => timer.elapsedTime.total);

            // 验证所有计时器都继续正常工作
            elapsedAfter.forEach((elapsed, index) => {
                expect(elapsed).toBeCloseTo(elapsedBefore[index] + 1000, -2);
            });

            // 清理新旧时间中心
            oldTimeCenter.stopUpdateTime();
            manager.timeCenter.stopUpdateTime();
        });
    });

    describe('真实场景模拟', () => {
        it('应该支持运行时切换同步服务器', async () => {
            const server1 = vi.fn().mockResolvedValue(1000000);
            const server2 = vi.fn().mockResolvedValue(2000000);

            const manager = createTimeManager({
                syncTime: server1,
                syncTimeFrequency: 1000,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            const timer = manager.createTimer();
            timer.start({});
            // 保存旧的时间中心引用
            const oldTimeCenter = manager.timeCenter;

            // 等待初始同步
            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            expect(server1).toHaveBeenCalled();

            // 模拟切换到备用服务器
            manager.reconfigure({
                syncTime: server2,
                syncTimeFrequency: 1000,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            expect(server2).toHaveBeenCalled();
            expect(timer.status).toBe(TimerStatus.running);

            // 清理新旧时间中心
            oldTimeCenter.stopUpdateTime();
            manager.timeCenter.stopUpdateTime();
        });

        it('应该支持临时禁用同步功能', async () => {
            const mockSyncTime = vi.fn().mockResolvedValue(1000000);

            const manager = createTimeManager({
                syncTime: mockSyncTime,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 临时禁用同步
            manager.reconfigure({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            const timer = manager.createTimer();
            timer.start({});

            await vi.advanceTimersByTimeAsync(1000);
            await nextTick();

            expect(timer.status).toBe(TimerStatus.running);

            // 重新启用同步
            manager.reconfigure({
                syncTime: mockSyncTime,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            expect(manager.timeCenter).toBeDefined();

            manager.timeCenter.stopUpdateTime();
        });
    });
});
