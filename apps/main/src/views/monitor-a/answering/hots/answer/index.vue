<template>
    <div class="answer-content-wrap">
        <AnswerTooltip v-bind="hotsStore.answerToast" />
        <div class="head-wrap">
            <p class="question-title">
                题目<span class="question-title-index">{{ currentQuestion.parentIndex }}-{{ currentQuestion.groupIndex }}</span>
            </p>
            <div class="time-wrap" :class="{ dangerous: questionCountdownTime.total < 30000 }">
                <span :class="{ dangerous: questionCountdownTime.total < 30000 }">本题剩余</span>
                <span class="time">{{ questionCountdownTime.minutes }}:{{ questionCountdownTime.seconds }}</span>
            </div>
        </div>
        <div v-if="templateConfig && templateConfig.templateName" class="content">
            <component :is="templateConfig.templateName" ref="answerRef" :isPreview="isPreview" @onAnswerChange="onAnswerChange">
                <div>
                    <p class="question-desc">
                        {{ currentQuestion.questionTitle }}
                    </p>
                    <p class="operating-instructions">
                        {{ config.instructions[currentQuestion.questionType] }}
                    </p>
                </div>
            </component>
        </div>
        <div class="next-wrap">
            <b-button v-if="hotsStore.currentIndex < hotsStore.questionList.length - 1" type="primary" shape="round" @click="onClick_next(false)"> 下一题 </b-button>
            <b-button v-else type="primary" style="width: 240px" shape="round" @click="onClick_next(true)"> 提交试卷 </b-button>
        </div>
    </div>
</template>

<script setup lang="ts" name="AnswerContent">
import { computed, ref } from 'vue';
import { answerTypeMap, QUESTION_TYPE } from '../constant';
import AnswerTooltip from './answer-tooltip.vue';
import { useHotsStore } from '../store';

const props = defineProps({
    isPreview: {
        type: Boolean,
        default: false,
    },
    config: {
        type: Object,
        default: () => ({}),
    },
});
const emits = defineEmits(['onSubmit', 'onNext', 'onAnswerChange']);
const hotsStore = useHotsStore();
const { questionTimeStart, setAnswer, postQuestion } = hotsStore;
const currentQuestion = computed(() => hotsStore.currentQuestion);
const answerRef = ref();
const questionCountdownTime = computed(() => hotsStore.questionCountdown.remainingTime);
const templateConfig = computed(() => {
    const questionType = currentQuestion.value.questionType;

    return answerTypeMap[questionType];
});

questionTimeStart();

async function onClick_next(isSubmit = false) {
    if (!answerRef.value.validate()) {
        return;
    }

    if (props.isPreview) {
        if (isSubmit) {
            emits('onSubmit', {});
        } else {
            emits('onNext');
        }
        return;
    }

    const params = {
        answerContent: JSON.stringify(answerRef.value.getAnswerData()),
    };

    postQuestion(params, (data: { evaPaperSnapshotId: string; answerContent: any }) => {
        setAnswer(data.evaPaperSnapshotId, data.answerContent);
        if (isSubmit) {
            const commitType = 1;
            emits('onSubmit', { commitType });
        } else {
            emits('onNext');
        }
    });
}

function onAnswerChange(lineData: any) {
    const params = {
        evaPaperSnapshotId: currentQuestion.value.encryptId,
        questionType: QUESTION_TYPE.HOTS_LIGATURE,
        answerContent: JSON.stringify(lineData),
    };
    emits('onAnswerChange', params);
}
</script>

<style lang="less" scoped>
.answer-content-wrap {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    color: #1f1f1f;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    line-height: normal;

    .message {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        top: 12px;
        display: flex;
        padding: 12px 16px;
        gap: 8px;
        justify-content: center;
        align-items: center;
        background: #d6ebff;
        box-shadow: 0px 2px 10px 0px rgba(93, 119, 140, 0.15);
        border: 1px solid #add6ff;
        border-radius: 22px;

        &-warning {
            background: #ffe1d6;
            border: 1px solid #ffd2c1;
        }
    }

    .head-wrap {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;

        .question-title {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: 600;
        }

        .question-title-index {
            margin-left: 6px;
            font-size: 16px;
        }

        .time-wrap {
            padding: 5px 6px;
            border-radius: 6px;
            display: flex;
            align-items: center;

            span {
                color: #808080;
                font-family: 'PingFang SC';
                font-size: 13px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;

                &.time {
                    width: 43px;
                    color: var(--primary-color);
                    margin-left: 6px;
                    font-family: 'Kanzhun';
                    font-size: 16px;
                }

                &.dangerous {
                    color: #f06a39;
                }
            }

            &.dangerous {
                background: #fff0eb;

                .time {
                    color: #f06a39;
                }
            }
        }
    }

    .content {
        flex: 1;
        overflow: auto;

        .question-desc {
            margin: 0 0 12px;
            font-size: 14px;
            font-weight: 400;
            line-height: 22px;
            white-space: pre-wrap;
        }

        .operating-instructions {
            color: #808080;
            font-size: 12px;
            line-height: normal;
            margin-bottom: 12px;
        }
    }

    .next-wrap {
        padding: 24px 20px;
        padding-bottom: 4px;
        text-align: center;

        .b-button {
            padding: 8px 27px;

            & + .b-button {
                margin-left: 20px;
            }
        }
    }
}
</style>
