<template>
    <b-dialog
        v-model="visible"
        wrapClass="dialog-manual-verify"
        :enableEscClose="false"
        :layerClosable="false"
        :confirmButtonProps="{ disabled: !canSubmit }"
        confirmText="确认上传"
        :showCancel="false"
        :width="680"
        @confirm="submit"
        @close="onClose"
    >
        <template #title>
            人工认证
            <p class="sub-title">如需更多帮助请联系{{ HELP_PHONE }}</p>
        </template>
        <div class="verify-container">
            <div class="verify-intro">
                <div class="demo-image">
                    <img src="@/assets/images/monitor/manual-verify-help.png" alt="" />
                </div>
                <div class="notice">
                    <p>注意事项</p>
                    <ul>
                        <li>请拍摄手持身份证在人脸旁拍摄</li>
                        <li>请保证全脸且正脸入镜</li>
                        <li>请保证可看清身份证号码</li>
                    </ul>
                </div>
            </div>
            <div class="verify-area">
                <ItemTab v-model="curIndex" :tabs="tabs" @changeTab="onChangeTab" />
                <component
                    :is="curComponent"
                    ref="verifyRef"
                    v-model="canSubmit"
                    class="verfiry-inner"
                    v-bind="{ manualAuditStatus, manualAuditResult, manualApplyAttachmentUrl }"
                    @close="onClose"
                />
            </div>
        </div>
    </b-dialog>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { HELP_PHONE } from '@crm/exam-constants';
import { computed, nextTick, ref } from 'vue';
import ItemTab from './item-tab.vue';
import ManualCamera from './manual-camera.vue';
import ManualUpload from './manual-upload.vue';

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false,
    },
    manualAuditStatus: {
        type: Number as PropType<number | null>,
        default: null,
    },
    manualAuditResult: {
        type: Number as PropType<number | null>,
        default: null,
    },
    manualApplyAttachmentUrl: {
        type: String,
        default: '',
    },
});
const emit = defineEmits(['update:modelValue']);
const canSubmit = ref(false);
const visible = ref(true);
const tabs = [
    { name: '上传照片', type: '1', component: ManualUpload },
    { name: '拍照认证', type: '2', component: ManualCamera },
];
const curIndex = ref(0);
const curComponent = computed(() => {
    return tabs[curIndex.value].component;
});

function onChangeTab() {
    canSubmit.value = false;
}

function onClose() {
    emit('update:modelValue', false);
}

const verifyRef = ref();
function submit() {
    nextTick(() => {
        if (canSubmit.value) {
            verifyRef?.value.submit();
        }
    });
}
</script>

<style lang="less">
.dialog-manual-verify {
    .sub-title {
        margin-left: 12px;
        color: #808080;
        font-size: 12px;
    }
    .verify-container {
        display: flex;
        .verify-intro {
            display: flex;
            width: 200px;
            flex-shrink: 0;
            flex-direction: column;
            .demo-image {
                width: 200px;
                height: 181px;
                img {
                    display: block;
                    width: 100%;
                }
            }
            .notice {
                flex: 1;
                margin-top: 12px;
                padding: 16px 13px 16px 16px;
                background: #f7f8fb;
                border-radius: 4px;
                line-height: 20px;
                p {
                    font-size: 14px;
                    font-weight: 500;
                }
                ul {
                    li {
                        position: relative;
                        padding-left: 7px;
                        margin-top: 12px;
                        font-size: 14px;
                        color: #808080;
                        &:before {
                            content: '';
                            position: absolute;
                            width: 2px;
                            height: 2px;
                            border-radius: 50%;
                            background-color: #808080;
                            top: 9px;
                            margin-top: -1px;
                            left: 2px;
                        }
                    }
                }
            }
        }
        .verify-area {
            width: 424px;
            margin-left: 8px;
            .tabs-contaienr {
                justify-content: flex-start;
            }
            .verfiry-inner {
                margin-top: 12px;
                height: 318px;
            }
        }
    }
}
</style>
