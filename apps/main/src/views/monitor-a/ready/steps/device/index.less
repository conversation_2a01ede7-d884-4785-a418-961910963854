.setting-container {
    .detection-panel {
        display: flex;
        width: 820px;
        height: 110px;
        margin: 0 auto;
        padding: 24px 42px;
        border-radius: 12px;
        align-items: flex-start;
        .pic {
            width: 92px;
            height: 62px;
            flex-shrink: 0;
            background-size: 100%;
        }
        .result {
            flex: 1;
            padding: 4px 12px;
            .result-title {
                display: flex;
                align-items: center;
                .test-status {
                    line-height: 26px;
                    font-size: 20px;
                    font-weight: 500;
                    color: #2d2d2d;
                }
                .warning-statistics {
                    display: block;
                    position: relative;
                    padding: 2px 8px 2px 20px;
                    margin-left: 7px;
                    line-height: 20px;
                    background: #ffeae8;
                    border-radius: 8px;
                    font-size: 12px;
                    color: #e34d59;

                    flex-grow: 0;
                    &::before {
                        content: '';
                        position: absolute;
                        left: 8px;
                        top: 8px;
                        width: 8px;
                        height: 8px;
                        background: #f66e71;
                        border-radius: 50%;
                    }
                }
            }
            .suggestion {
                margin-top: 8px;
                line-height: 20px;
                font-size: 12px;
                color: #808080;
            }
        }
        .btns {
            flex-shrink: 0;
            padding-top: 12px;
            padding-right: 8px;
        }
        &.default {
            background: linear-gradient(86deg, #e9f0fb 0%, #f7fbff 100%);
            .pic {
                background-image: url(./images/test-default.png);
            }
        }
        &.success {
            background: linear-gradient(86deg, #e9f9fb 0%, #f7fbff 100%);
            .pic {
                background-image: url(./images/test-success.png);
            }
        }
        &.warning {
            background: linear-gradient(90deg, #fffaf5 0%, #fff9f7 100%);
            .pic {
                background-image: url(./images/test-warning.png);
            }
        }
    }
}

@keyframes rotate-loading {
    0% {
        transform: rotate(0);
    }

    100% {
        transform: rotate(360deg);
    }
}

.detection-list {
    width: 820px;
    padding: 17px 49px 17px 51px;
    margin: 0 auto;
    :deep(.detection-item) {
        display: flex;
        position: relative;
        align-items: center;
        padding: 17px 0;
        line-height: 20px;
        &::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 1px;
            background: #e5e5e5;
        }
        .detection-content {
            display: flex;
            width: 216px;
            margin: 0 10px;
            flex-shrink: 0;
            .type {
                font-size: 14px;
                line-height: 20px;
                color: #2d2d2d;
                flex-shrink: 0;
            }
            .result {
                font-size: 12px;
                color: #808080;
                margin-left: 12px;
                &.error {
                    color: #e34d59;
                }
            }
        }
        .detection-suggestion {
            display: flex;
            align-items: center;
            flex: 1;
            font-size: 12px;
            color: #808080;
            .download {
                display: inline-block;
                width: 34px;
                height: 20px;
                margin-left: 12px;
                text-align: center;
                border-radius: 4px;
                &:hover {
                    cursor: pointer;
                    background: #caebeb;
                }
            }
            .op-help {
                display: inline-flex;
                width: 53px;
                height: 20px;
                margin-left: 6px;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                border-radius: 4px;
                color: #4d4d4d;
                &:hover {
                    background: #f2f2f2;
                }
                svg {
                    margin-right: 3px;
                }
            }
        }
        .detection-action {
            position: absolute;
            right: 0;
            .btn {
                min-width: 72px;
                min-height: 28px;
                padding: 0 7px;
                font-size: 12px;
            }
        }
    }
}
