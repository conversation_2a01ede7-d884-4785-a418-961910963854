import AllocaTion from './question/allocation/index.vue';
import AreaJudge from './question/area-judge/index.vue';
import DirectionTask from './question/direction-task/index.vue';
import ExpressDelivery from './question/express-delivery/index.vue';
import Fruit from './question/fruit/index.vue';
import Mine from './question/mine/index.vue';

export const gbaQuestionType = {
    MINE: -10002, // 挖矿
    FRUIT: -10001, // 水果
    ALLOCATION: -10003, // 分配
    DELIVERRY: -10005, // 取快递
    AREAJUDGE: -10004, // 区域划分
    DIRECTIONTASK: -10006, // 方向任务
};

export const GbaQuestionMap = {
    [gbaQuestionType.MINE]: {
        template: Mine,
    },
    [gbaQuestionType.FRUIT]: {
        template: Fruit,
    },
    [gbaQuestionType.DELIVERRY]: {
        template: ExpressDelivery,
    },
    [gbaQuestionType.AREAJUDGE]: {
        template: AreaJudge,
    },
    [gbaQuestionType.DIRECTIONTASK]: {
        template: DirectionTask,
    },
    [gbaQuestionType.ALLOCATION]: {
        template: AllocaTion,
    },
};

// 缓存回合已处理答案标识
export const GBA_QUESTION_ANSWER_KEY = 'SESSION_STORAGE_GBA_QUESTION_ANSWER_KEY';
