<template>
    <div class="detection-item">
        <TestStatus :isTesting="isTesting" :isSuccess="isSuccess" />
        <div class="detection-content">
            <div class="type">
                {{ CHECK_TYPE_NAME }}
            </div>
            <div class="result" :class="{ error: !isSuccess && !isTesting }">
                {{ testResult }}
            </div>
        </div>
        <div v-if="!isSuccess && !isTesting" class="detection-suggestion">
            {{ testSuggestions }}
            <span class="op-help" @click="emits('showGuid')"><SvgIcon name="svg-full-question" width="16" height="16" />帮助</span>
        </div>
        <div v-if="!isSuccess && !isTesting" class="detection-action">
            <b-button type="outline" shape="round" size="small" @click.prevent="test"> 重新检测 </b-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { usePrepareCheckSave } from '@/hooks/usePrepareCheckSave';

import { urls } from '@/shared/url';
import { onBeforeUnmount, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useTestResult } from '../useTestResult';
import TestStatus from './test-status.vue';

// 检测建议

const emits = defineEmits(['update:is-success', 'update:is-testing', 'showGuid']);

const CHECK_TYPE_NAME = '麦克风';

const $route = useRoute();

const isTesting = ref(true); // 是否正在检测
const isSuccess = ref(false); // 是否检测成功
const testResult = ref(''); // 检测结果
const testSuggestions = ref('');
function updateIsTesting(value: boolean) {
    emits('update:is-testing', value);
}
function updateIsSuccess(value: boolean) {
    emits('update:is-success', value);
}
useTestResult(isTesting, isSuccess, updateIsTesting, updateIsSuccess);

function reset() {
    isTesting.value = true;
    isSuccess.value = false;
    testResult.value = '正在检测中…';
    testSuggestions.value = '';
}

let rtc: any;

async function test() {
    reset();

    try {
        let localStream = await rtc?.createStream({ audio: true, disableDeviceChangeDetect: true });
        // 埋点开始
        BossAnalyticsTrack('zhice-pc-exam-device-check-audio-stream-create-success', {
            pData: {
                type: TrackTypeEnum.成功,
                nameZh: '设备检测-成功创建音频流',
            },
        });
        // 埋点结束
        await localStream.initialize();
        // 埋点开始
        BossAnalyticsTrack('zhice-pc-exam-device-check-audio-stream-init', {
            pData: {
                type: TrackTypeEnum.成功,
                nameZh: '设备检测-成功初始化音频流',
            },
        });
        // 埋点结束

        isSuccess.value = true;
        testResult.value = '麦克风已开启';

        usePrepareCheckSave({
            encryptExamId: ($route.query.examId || $route.query.seqId) as string,
            checkItem: 3, // 1 浏览器 2 网速 3麦克风 4 电脑主摄像头 5 电脑屏幕 6手机第二视角
            checkStatus: isSuccess.value ? 1 : 2, // //状态 1成功 2失败
            checkRemark: isSuccess.value ? '' : testResult.value,
        });

        // 关闭
        if (localStream) {
            localStream.close();
            localStream = null;
            // 埋点开始
            BossAnalyticsTrack('zhice-pc-exam-device-check-audio-stream-close', {
                pData: {
                    type: TrackTypeEnum.成功,
                    nameZh: '设备检测-音频流关闭',
                },
            });
            // 埋点结束
        }
    } catch (error: any) {
        isSuccess.value = false;
        testResult.value = '';
        switch (error.name) {
            case 'NotFoundError':
                testResult.value = '缺少可用的麦克风设备';
                testSuggestions.value = '请重新连接麦克风设备';
                break;
            case 'NotAllowedError':
                testResult.value = '麦克风权限未开启';
                testSuggestions.value = '请开启麦克风权限';
                break;
            case 'NotReadableError':
                testResult.value = '无法访问麦克风';
                testSuggestions.value = '请确保当前无其他应用正在使用麦克风';
                break;
            case 'SecurityError':
                testResult.value = '系统禁止访问麦克风';
                testSuggestions.value = '请取消限制后再检测';
                break;
            case 'AbortError':
                testResult.value = '麦克风异常';
                testSuggestions.value = '建议更换浏览器或设备后重新检测';
                break;
            case 'OverConstrainedError':
                testResult.value = 'microphoneId 参数的值无效';
                testSuggestions.value = '确保 microphoneId 传值正确且有效';
                break;
            default:
                testResult.value = '检测失败';
                testSuggestions.value = '初始化时遇到未知错误, 请重试';
                break;
        }
        usePrepareCheckSave({
            encryptExamId: ($route.query.examId || $route.query.seqId) as string,
            checkItem: 3, // 1 浏览器 2 网速 3麦克风 4 电脑主摄像头 5 电脑屏幕 6手机第二视角
            checkStatus: isSuccess.value ? 1 : 2, // //状态 1成功 2失败
            checkRemark: isSuccess.value ? '' : testResult.value,
        });
        // 埋点开始
        BossAnalyticsTrack('zhice-pc-exam-device-check-audio-stream-init', {
            pData: {
                type: TrackTypeEnum.失败,
                message: error.name,
                nameZh: '设备检测-音频流初始化失败',
            },
        });
        // 埋点结束
    }
    isTesting.value = false;
}

onMounted(async () => {
    // web RTC SDK实例
    // @ts-ignore
    rtc = NEBULARTC();
    rtc.setEnv(urls.rtcUrl);
    await test();
});

onBeforeUnmount(() => {});

defineExpose({ test });
</script>
