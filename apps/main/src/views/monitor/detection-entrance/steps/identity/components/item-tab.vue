<template>
    <div class="tabs-contaienr">
        <ul>
            <li v-for="(item, index) in tabs" :key="item.type" :class="{ active: modelValue === index }" @click="onChange(index)">
                {{ item.name }}
            </li>
        </ul>
    </div>
</template>

<script setup lang="ts">
const props = withDefaults(
    defineProps<{
        tabs?: Array<{
            name: string;
            type: string;
        }>;
        modelValue?: number;
    }>(),
    {
        tabs: () => [],
        modelValue: 0,
    },
);

const emit = defineEmits(['update:modelValue', 'changeTab']);

function onChange(index: number) {
    emit('update:modelValue', index);
    emit('changeTab', index, props.tabs[index]);
}
</script>

<style lang="less" scoped>
.tabs-contaienr {
    position: relative;
    display: flex;
    justify-content: center;
    z-index: 99;
    ul {
        display: inline-flex;
        padding: 1px;
        border: 1px solid #dfdfdf;
        background-color: #f2f2f2;
        border-radius: 8px;
        li {
            padding: 8px 24px;
            border-radius: 8px;
            color: #808080;
            cursor: pointer;
            &:hover,
            &.active {
                color: #12ada9;
                font-weight: 500;
            }
            &.active {
                background-color: #ffffff;
            }
        }
    }
}
</style>
