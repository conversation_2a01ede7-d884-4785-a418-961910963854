<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Identity Verification Reactivity Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 3px; }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.fail { background-color: #f8d7da; color: #721c24; }
        .status.verifying { background-color: #d1ecf1; color: #0c5460; }
        .status.init { background-color: #f8f9fa; color: #495057; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        .log { background-color: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 3px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>Identity Verification Reactivity Test</h1>
    <p>This test simulates the API response scenarios to verify that UI updates automatically when backend returns failure states.</p>
    
    <div class="test-section">
        <h3>Simulated API Response</h3>
        <div id="api-response"></div>
        <button onclick="simulateFailureResponse()">Simulate Failure Response</button>
        <button onclick="simulateSuccessResponse()">Simulate Success Response</button>
        <button onclick="simulateInProgressResponse()">Simulate In Progress Response</button>
    </div>
    
    <div class="test-section">
        <h3>PC Component Status</h3>
        <div id="pc-status" class="status init">NOT_INIT</div>
    </div>
    
    <div class="test-section">
        <h3>Mobile Component Status</h3>
        <div id="mobile-status" class="status init">NO_ACTION</div>
    </div>
    
    <div class="test-section">
        <h3>Debug Log</h3>
        <div id="debug-log" class="log"></div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        const MAX_MACHINE_VERIFY_TIMES = 3;
        
        // Simulate reactive data
        let recognizeResult = {
            recognizeFaceFailedCount: 0,
            systemRecognizeResult: 0,
            pcSystemRecognizeResult: 0,
            h5SystemRecognizeResult: 0
        };
        
        let manualResult = {
            manualAuditStatus: null,
            manualAuditResult: null
        };
        
        function log(message) {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toISOString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function updateApiDisplay() {
            document.getElementById('api-response').innerHTML = `
                <pre>${JSON.stringify({
                    recognizeFaceFailedCount: recognizeResult.recognizeFaceFailedCount,
                    systemRecognizeResult: recognizeResult.systemRecognizeResult,
                    pcSystemRecognizeResult: recognizeResult.pcSystemRecognizeResult,
                    h5SystemRecognizeResult: recognizeResult.h5SystemRecognizeResult,
                    manualAuditStatus: manualResult.manualAuditStatus,
                    manualAuditResult: manualResult.manualAuditResult
                }, null, 2)}</pre>
            `;
        }
        
        function changeStatusPC() {
            const oldStatus = document.getElementById('pc-status').textContent;
            let newStatus = '';
            
            if (manualResult.manualAuditStatus) {
                if (manualResult.manualAuditStatus === 1) {
                    newStatus = 'MANUAL_VERIFYING';
                } else {
                    newStatus = manualResult.manualAuditResult === 1 ? 'MANUAL_SUCCESS' : 'MANUAL_FAIL';
                }
            } else if (recognizeResult.recognizeFaceFailedCount < MAX_MACHINE_VERIFY_TIMES) {
                if (recognizeResult.pcSystemRecognizeResult === 0) {
                    newStatus = 'NOT_INIT';
                } else if (recognizeResult.pcSystemRecognizeResult === 1) {
                    newStatus = 'MACHINE_SUCCESS';
                } else if (recognizeResult.pcSystemRecognizeResult === 2) {
                    newStatus = 'MACHINE_FAIL';
                } else if (recognizeResult.pcSystemRecognizeResult === 3) {
                    newStatus = 'MACHINE_VERIFYING';
                } else {
                    newStatus = 'NOT_INIT';
                }
            } else {
                newStatus = recognizeResult.systemRecognizeResult === 1 ? 'MACHINE_SUCCESS' : 'MACHINE_FAIL';
            }
            
            const statusDiv = document.getElementById('pc-status');
            statusDiv.textContent = newStatus;
            statusDiv.className = 'status ' + getStatusClass(newStatus);
            
            if (oldStatus !== newStatus) {
                log(`🔄 PC Component status changed: ${oldStatus} → ${newStatus}`);
            }
        }
        
        function changeStatusMobile() {
            const oldStatus = document.getElementById('mobile-status').textContent;
            let newStatus = '';
            
            if (manualResult.manualAuditStatus) {
                if (manualResult.manualAuditStatus === 1) {
                    newStatus = 'MANUAL_VERIFYING';
                } else {
                    newStatus = manualResult.manualAuditResult === 1 ? 'MANUAL_SUCCESS' : 'MANUAL_FAIL';
                }
            } else if (recognizeResult.recognizeFaceFailedCount < MAX_MACHINE_VERIFY_TIMES) {
                if (recognizeResult.pcSystemRecognizeResult === 1) {
                    newStatus = 'MACHINE_SUCCESS';
                } else if (recognizeResult.pcSystemRecognizeResult === 2) {
                    newStatus = 'MACHINE_FAIL';
                } else if (recognizeResult.pcSystemRecognizeResult === 3) {
                    newStatus = 'NO_ACTION';
                } else if (recognizeResult.h5SystemRecognizeResult === 0) {
                    newStatus = 'NO_ACTION';
                } else if (recognizeResult.h5SystemRecognizeResult === 1) {
                    newStatus = 'MACHINE_SUCCESS';
                } else if (recognizeResult.h5SystemRecognizeResult === 2) {
                    newStatus = 'MACHINE_FAIL';
                } else {
                    newStatus = 'NO_ACTION';
                }
            } else {
                newStatus = recognizeResult.systemRecognizeResult === 1 ? 'MACHINE_SUCCESS' : 'MACHINE_FAIL';
            }
            
            const statusDiv = document.getElementById('mobile-status');
            statusDiv.textContent = newStatus;
            statusDiv.className = 'status ' + getStatusClass(newStatus);
            
            if (oldStatus !== newStatus) {
                log(`🔄 Mobile Component status changed: ${oldStatus} → ${newStatus}`);
            }
        }
        
        function getStatusClass(status) {
            if (status.includes('SUCCESS')) return 'success';
            if (status.includes('FAIL')) return 'fail';
            if (status.includes('VERIFYING')) return 'verifying';
            return 'init';
        }
        
        function updateSystemRecognizeStatus(data) {
            log(`🔄 Updating system recognize status: ${JSON.stringify(data)}`);
            
            recognizeResult.recognizeFaceFailedCount = data.recognizeFaceFailedCount;
            recognizeResult.systemRecognizeResult = data.systemRecognizeResult;
            recognizeResult.pcSystemRecognizeResult = data.pcSystemRecognizeResult;
            recognizeResult.h5SystemRecognizeResult = data.h5SystemRecognizeResult;
            
            updateApiDisplay();
            changeStatusPC();
            changeStatusMobile();
            
            log(`✅ System recognize status updated successfully`);
        }
        
        function simulateFailureResponse() {
            log('🚨 Simulating failure response (the original problem case)');
            updateSystemRecognizeStatus({
                recognizeFaceFailedCount: 2,
                systemRecognizeResult: 2,
                pcSystemRecognizeResult: 2,
                h5SystemRecognizeResult: 0
            });
        }
        
        function simulateSuccessResponse() {
            log('✅ Simulating success response');
            updateSystemRecognizeStatus({
                recognizeFaceFailedCount: 1,
                systemRecognizeResult: 1,
                pcSystemRecognizeResult: 1,
                h5SystemRecognizeResult: 0
            });
        }
        
        function simulateInProgressResponse() {
            log('⏳ Simulating in progress response');
            updateSystemRecognizeStatus({
                recognizeFaceFailedCount: 1,
                systemRecognizeResult: 0,
                pcSystemRecognizeResult: 3,
                h5SystemRecognizeResult: 0
            });
        }
        
        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }
        
        // Initialize
        updateApiDisplay();
        changeStatusPC();
        changeStatusMobile();
        log('🚀 Test initialized');
    </script>
</body>
</html>
