import { computed, nextTick, onMounted, ref, watch } from 'vue';
// import { useHotsStore } from '../../store'

const forwardDirectionCode = 1; // 正向关系-加强
const reverseCode = 2; // 反向关系-减弱
const typeList = [
    {
        label: '加强',
        value: forwardDirectionCode,
    },
    {
        label: '减弱',
        value: reverseCode,
    },
];
const arrowFillMap = {
    [forwardDirectionCode]: '#00A6A7',
    [reverseCode]: '#F06A39',
};
// const hotsStore = useHotsStore()

/**
 * 获取目标输入参数元素所在位置
 * @param element
 */
function getElementPositionRelativeToDocument(element) {
    if (!element) {
        return { left: 0, top: 0 };
    }
    const rect = element.getBoundingClientRect();

    // 获取视口滚动位置
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

    // 计算元素相对于文档的位置
    const leftPosition = rect.left + scrollLeft;
    const topPosition = rect.top + scrollTop;

    // 获取元素的计算样式以包含 margin
    const style = window.getComputedStyle(element);
    const marginLeft = 0; // parseFloat(style.marginLeft);
    const marginTop = 0; // parseFloat(style.marginTop);

    return { left: leftPosition - marginLeft, top: topPosition + marginTop, width: rect.width, height: rect.height };
}

export function useConnectLine(option = {}) {
    const { $emit, getAnswerData, showArrow = true, lineSpacing = 4, positionList = [], handleEndPoint } = option || {};
    const containerRef = ref(); // 画布根节点ref
    const connectType = ref(); // 选择加强或者减弱类型
    const links = ref([]); // 已经连线的数据
    const draw = ref(false); // 是否展示svg划线dom元素
    const currentSelectedId = ref(); // 当前选中的输入参数id
    const popupVisibleMap = ref({}); // popover展示的存储状态
    const endBlockRef = ref();
    const startBlockRef = ref();
    const svgRef = ref();
    const isHovered = ref(null);
    const lineSpacingRef = ref(lineSpacing);
    const hotsStore = option?.hotsStore || {};

    const product10QuestionParamVO = computed(() => hotsStore?.currentQuestion?.product10QuestionParamVO);

    // 左侧输入参数数据
    const inputParamList = computed(() => {
        return product10QuestionParamVO?.value?.inputParamList?.map((item, index) => ({ ...item, label: item.showName, value: index })) || [];
    });
    // 右侧输出参数数据
    const outputParamList = computed(() => {
        return product10QuestionParamVO?.value?.outputParamList?.map((item, index) => ({ ...item, label: item.showName, value: index })) || [];
    });
    // 是否是环形连线题
    const showRoundLinesComponents = computed(() => {
        const { outputParamCount, outputParamList } = product10QuestionParamVO.value || {};
        return !outputParamCount && !outputParamList?.length;
    });
    // 是否支持加强/减弱关系选择（ relationTypeCount =1 : 有关，relationTypeCount=2 ： 增强/减弱;    有关时 传1(增强)就行）
    const showRelationTypePopover = computed(() => product10QuestionParamVO.value.relationTypeCount === 2);
    const connectTypeList = computed(() => {
        return showRelationTypePopover.value ? typeList : [];
    });

    /**
     * 连线题出现弹窗，把当前正在连线中的状态取消
     */

    watch(
        () => [hotsStore?.answerDialogController?.dialogOpened],
        (showDialog) => {
            if (showDialog) {
                handleCancelConnectLine();
            }
        },
    );

    /**
     * 初始化数据回显
     */
    const initLinksData = () => {
        const data = JSON.parse(hotsStore.answerData[hotsStore?.currentQuestion.encryptId] || '[]');
        links.value = data?.map((item) => {
            const { value: from } = inputParamList.value.find((v) => v.paramName === item.startParam) || {};
            const { value: to } = (showRoundLinesComponents.value ? inputParamList.value : outputParamList.value).find((v) => v.paramName === item.endParam) || {};
            return {
                from,
                to,
                type: item.relation,
                startParam: item.startParam,
                endParam: item.endParam,
            };
        });
    };
    /**
     * 开始绘制线条
     */
    let line = null;
    const drawLine = (startX, startY) => {
        line = createLine(startX, startY, startX, startY);
        svgRef.value.appendChild(line);
    };
    /**
     * 移除绘制的线条
     */
    const removeSvgLine = () => {
        if (line) {
            svgRef.value.removeChild(line);
        }
        line = null;
    };
    const createLine = (x1, y1, x2, y2) => {
        const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        line.setAttribute('x1', x1);
        line.setAttribute('y1', y1);
        line.setAttribute('x2', x2);
        line.setAttribute('y2', y2);
        line.setAttribute('stroke', arrowFillMap[connectType.value]);
        line.setAttribute('stroke-width', '1');
        if (connectType.value === reverseCode) {
            line.setAttribute('stroke-dasharray', '3, 3');
        } // 设置虚线
        if (showArrow) {
            line.setAttribute('marker-end', 'url(#arrow)');
        }
        return line;
    };
    /**
     * 连线成功后更新数据
     * @param links
     */
    const updateData = (links) => {
        $emit('update:modelValue', getAnswerData(), links);
        $emit('onAnswerChange', getAnswerData());
    };
    /**
     * 鼠标移动，跟随鼠标移动划线
     * @param e
     */
    const mousemove = (e) => {
        if (!line) {
            return;
        }
        const { left: targetPageX, top: targetPageY } = getElementPositionRelativeToDocument(svgRef.value);
        const toX = e.pageX - targetPageX - lineSpacingRef.value;
        const toY = e.pageY - targetPageY - lineSpacingRef.value;
        line.setAttribute('x2', toX);
        line.setAttribute('y2', toY);
    };
    const svgCanvasListener = () => {
        document?.addEventListener('mousemove', mousemove);
    };
    const removeCanvasListener = () => {
        document?.removeEventListener('mousemove', mousemove);
    };
    const getMarkerInfo = (index) => {
        if (isHovered.value === index) {
            return {
                marker: {
                    markerWidth: 4,
                    markerHeight: 4,
                    refX: 2,
                    refY: 2,
                },
                d: 'M0,0 L2,2 L0,4',
            };
        }
        return {
            marker: {
                markerWidth: 8,
                markerHeight: 8,
                refX: 4,
                refY: 4,
            },
            d: 'M0,0 L4,4 L0,8',
        };
    };
    /**
     * 气泡显示状态改变时触发
     */
    const popupVisibleChange = (value, id) => {
        popupVisibleMap.value[id] = value;
    };
    const getPoint = (args) => {
        const { startDom, rootDom = containerRef.value, position = 'right' } = args || {};
        const { left: x1, top: y1 } = getElementPositionRelativeToDocument(rootDom);
        const { left: x2, top: y2, width = 0, height = 0 } = getElementPositionRelativeToDocument(startDom);
        let startX = x2 - x1;
        let startY = y2 - y1;
        if (position === 'bottom') {
            startX = startX + width / 2;
            startY = startY + height + lineSpacingRef.value;
        }
        if (['rt', 'tr'].includes(position)) {
            startX = startX + width + lineSpacingRef.value;
            startY = startY + lineSpacingRef.value;
        }
        if (position === 'right') {
            startX = startX + width + lineSpacingRef.value;
            startY = startY + height / 2;
        }
        if (['rb', 'br'].includes(position)) {
            startX = startX + width + lineSpacingRef.value;
            startY = startY + height + lineSpacingRef.value;
        }
        if (['lt', 'tl'].includes(position)) {
            startX = startX - lineSpacingRef.value;
            startY = startY + lineSpacingRef.value;
        }
        if (position === 'left') {
            startX = startX - lineSpacingRef.value;
            startY = startY + height / 2;
        }
        if (['lb', 'bl'].includes(position)) {
            startX = startX - lineSpacingRef.value;
            startY = startY + height + lineSpacingRef.value;
        }
        if (position === 'top') {
            startX = startX + width / 2;
            startY = startY - lineSpacingRef.value;
        }
        return { startX, startY };
    };
    /**
     * 选择目标元素操作类型（加强or减弱）
     */
    const handleConnectType = (value, row, index) => {
        const id = row.value;
        connectType.value = value;
        popupVisibleMap.value[id] = false;
        currentSelectedId.value = id;
        svgCanvasListener();
        const { startX, startY } = getPoint({ startDom: startBlockRef.value[index], position: row.position || positionList[index] });
        drawLine(startX, startY);
    };
    /**
     * 取消正在连线中的连线
     */
    const handleCancelConnectLine = () => {
        currentSelectedId.value = '';
        removeCanvasListener();
        removeSvgLine();
    };
    /**
     * 点击连线，删除连线
     * @param item
     * @param index
     */
    const handleDelSvgPath = (item, index) => {
        links.value = links.value.filter((v, i) => i !== index);
        updateData(links.value);
    };
    const handleGetPointBlock = (row, index) => {
        if (!(currentSelectedId.value || currentSelectedId.value === 0)) {
            handleConnectType(forwardDirectionCode, row, index);
            return;
        }
        if (currentSelectedId.value && !showRoundLinesComponents.value) {
            handleCancelConnectLine();
            return;
        }
        if (handleEndPoint) {
            handleEndPoint(row);
        }
    };
    onMounted(() => {
        nextTick(() => {
            draw.value = true;
            initLinksData();
        });
    });
    return {
        isHovered,
        containerRef,
        startBlockRef,
        endBlockRef,
        svgRef,
        draw,
        popupVisibleMap,
        connectType,
        currentSelectedId,
        forwardDirectionCode,
        reverseCode,
        connectTypeList,
        arrowFillMap,
        inputParamList,
        outputParamList,
        links,
        showRelationTypePopover,
        getElementPositionRelativeToDocument,
        removeSvgLine,
        initLinksData,
        updateData,
        svgCanvasListener,
        removeCanvasListener,
        handleCancelConnectLine,
        popupVisibleChange,
        handleConnectType,
        getMarkerInfo,
        getPoint,
        handleDelSvgPath,
        handleGetPointBlock,
    };
}
