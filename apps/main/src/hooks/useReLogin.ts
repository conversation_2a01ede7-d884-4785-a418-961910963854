// 重复登录
import { useMonitorStore } from '@/store/use-monitor-store';
import { useRoute, useRouter } from 'vue-router';
import commitPaper from '../hooks/useCommitPaper';

const monitorStore = useMonitorStore();

export default function () {
    const route = useRoute();
    const router = useRouter();
    if (!monitorStore.examConfig.reLoginRule.reLoginWarning) {
        return; // 未设置重复登录警告
    }
    if (monitorStore.examConfig.reLoginRule.currentReLoginCount === 0) {
        return; // 第一次登录
    }
    if (monitorStore.examConfig.reLoginRule.warningConfirm) {
        return; // 确认过弹窗提醒
    }
    if (monitorStore.examConfig.reLoginRule.currentReLoginCount < monitorStore.examConfig.reLoginRule.maxLoginCount) {
        Dialog.open({
            title: '重复登录',
            content: '检测到您刚刚有重复登录操作，重登超过一定次数会自动交卷，作答期间请勿离开答题页面！',
            type: 'warning',
            showCancel: false,
            showClose: false,
            confirmText: '知道了',
            layerClosable: false,
            enableEscClose: false,
            close() {
                Invoke.exam.postWaningConfirmReLogin({
                    encryptExamId: route.params.examId,
                });
            },
        });
        // 埋点开始
        BossAnalyticsTrack('zhice-pc-exam-reLogin-dialog-warn', {
            pData: {
                type: TrackTypeEnum.失败,
                nameZh: '弹出重复登录弹窗',
            },
        });
        // 埋点结束
    } else if (monitorStore.examConfig.reLoginRule.forceSubmitWhenOverLimit) {
        Dialog.open({
            title: '强制交卷',
            content: '检测到您重复登录次数超过限制，系统已强制交卷！',
            type: 'warning',
            showCancel: false,
            confirmText: '知道了',
        });
        commitPaper('reLogin', route, router);
    }
}
