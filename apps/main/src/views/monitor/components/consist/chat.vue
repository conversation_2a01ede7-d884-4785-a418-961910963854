<template>
    <div class="chat-wrap" click.stop>
        <div class="header">
            <div class="user-img">
                <img src="@/assets/images/monitor/chat-avator.png" alt="" />
            </div>
            <div class="user-name">监考官</div>
            <div class="close-btn" @click="onClose">
                <IconClose size="22" />
            </div>
        </div>
        <div ref="messagePanelRef" class="message-panel">
            <b-scrollbar ref="scrollBar" style="overflow: auto; max-height: 359px" @scroll="onScroll">
                <div class="message-list">
                    <div v-for="(item, index) of messageList" :key="index" class="message-item" :class="[item.sender === 'self' ? 'message-item-self' : 'message-item-opposite']">
                        <div class="name">
                            {{ item.name }}
                        </div>
                        <div class="content">
                            {{ item.content }}
                        </div>
                    </div>
                </div>
            </b-scrollbar>
        </div>
        <div class="input-area">
            <textarea
                v-model="message"
                placeholder="请输入"
                maxlength="2000"
                @keypress.exact.enter.prevent="sendMessage"
                @keyup.ctrl.enter.prevent="onCtrlEnter"
                @keydown.enter.meta.prevent="onMetaEnter"
            />
            <b-button class="btn-send" type="primary" shape="round" size="large" :disabled="!message.trim()" @click="sendMessage"> 发送 </b-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { IconClose } from '@boss/design/es/icon';
import { useMonitorStore } from '@/store/use-monitor-store';
import { chatService } from '@/utils/socket';
import { emitter } from '@crm/exam-utils';
import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

const props = defineProps({
    showChat: {
        type: Boolean,
        defalut: false,
    },
});
const emit = defineEmits(['toggle']);
const $route = useRoute();
const { params } = $route;
const encryptExamId = params.examId;

const monitorStore = useMonitorStore();

function onClose() {
    emit('toggle', false);
}

interface MessageUser {
    roleType: number;
    self: boolean;
    sendUserId: string;
    sendUserName: string;
}
interface MessageItem {
    content: string;
    sender: 'self' | 'opposite';
    name: '我' | '监考官';
    msgType?: number;
    talkTime?: string;
    from?: MessageUser;
}
const messageList = ref<MessageItem[]>([]);

const messagePanelRef = ref();
const message = ref('');
let groupId = '';
const messageIds: any = []; // 消息防重复所用，保留所有消息id

function sendMessage() {
    if (message.value.trim()) {
        chatService.sendMessage({
            examId: encryptExamId,
            content: message.value,
            userId: monitorStore.examBaseInfo.encryptUserId,
            userName: monitorStore.examBaseInfo.userName,
            groupId,
        });
        // 埋点
        BossAnalyticsTrack('zhice-pc-exam-invigilate-send', {
            p3: monitorStore.step + 1,
        });
    }
}
function onSendMessage(payload: any) {
    if (payload.proType !== 1) {
        return;
    }
    // IM消息类型 proType === 1
    messageList.value.push({
        sender: 'self',
        name: '我',
        content: payload.messages[0].text,
    });
    message.value = '';
    scrollToBottom();
    BossAnalyticsTrack('zhice-pc-exam-examinee-send-message', {
        pData: { type: TrackTypeEnum.成功 },
    });
}
function onReceiveMessage(payload: any) {
    if (payload.proType !== 1) {
        return;
    }
    if (messageIds.includes(payload.messages[0].messageId)) {
        return;
    }
    messageIds.push(payload.messages[0].messageId);
    groupId = payload.messages[0].groupId;

    messageList.value.push({
        sender: 'opposite',
        name: '监考官',
        content: payload.messages[0].text,
    });
    scrollToBottom();
    emit('toggle', true); // 有消息自动弹出
}

onMounted(async () => {
    emitter.on('receiveMessage', onReceiveMessage);
    emitter.on('sendMessage', onSendMessage);
    await getHistoryMessage(1);
});
onUnmounted(() => {
    emitter.off('receiveMessage', onReceiveMessage);
    emitter.off('sendMessage', onSendMessage);
});

function getCursorPosition(textarea: any) {
    return {
        start: textarea.selectionStart,
        end: textarea.selectionEnd,
    };
}
function onCtrlEnter(e: KeyboardEvent) {
    onBreakText(e.target);
}
function onMetaEnter(e: KeyboardEvent) {
    onBreakText(e.target);
}
function onBreakText(textarea: any) {
    const { start, end } = getCursorPosition(textarea);
    message.value = `${message.value.slice(0, start)}\n${message.value.slice(end)}`;
    nextTick(() => {
        textarea.setSelectionRange(start + 1, start + 1);
    });
}

const scrollBar = ref();
function scrollToBottom() {
    nextTick(() => {
        scrollBar.value.scrollTo({ top: scrollBar.value?.containerRef?.scrollHeight || 9999999999, behavior: 'auto' });
    });
}

// 拉取历史消息开始
let hasMore = false;
let page = 1;
let isLoading = false;
let lastScrollHeight = 0; // 暂存上次滚动高度
async function getHistoryMessage(page: number) {
    try {
        const params = {
            examId: encryptExamId,
            groupId,
            examineeId: monitorStore.examBaseInfo.encryptUserId,
            page,
            pageSize: 30,
        };
        isLoading = true;
        const { code, data } = await Invoke.exam.getDialogHistoryList(params);
        isLoading = false;

        if (code === 0) {
            hasMore = data.hasMore;
            const items = data.items.map((x: any) => {
                return {
                    sender: x.from.self ? 'self' : 'opposite',
                    name: x.from.self ? '我' : '监考官',
                    content: x.content,
                };
            });
            messageList.value.unshift(...items);
            scrollToPrePosition(items.length);
        }
    } catch (error) {
        isLoading = false;
    }
}

// 加载新数据后，滚动到原来位置
function scrollToPrePosition(length: number) {
    if (!props.showChat) {
        return;
    }
    if (length > 0) {
        const scrollElement = scrollBar.value?.containerRef;
        nextTick(() => {
            scrollElement.scrollTop = scrollElement.scrollHeight - lastScrollHeight;
        });
    }
}
// 下拉加载更多历史消息
async function onScroll(event: any) {
    if (!props.showChat) {
        return;
    }
    const scrollTop = event.target.scrollTop;
    if (scrollTop === 0 && hasMore && !isLoading) {
        lastScrollHeight = scrollBar.value?.containerRef?.scrollHeight || 0;
        getHistoryMessage(++page);
    }
}

watch(
    () => props.showChat,
    () => {
        if (props.showChat) {
            scrollToBottom();
        }
    },
);
</script>

<style lang="less" scoped>
.chat-wrap {
    width: 393px;
    box-shadow: 0px 20px 40px 0px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
    position: absolute;
    z-index: 99;
    left: -410px !important;
    top: -505px !important;
    cursor: default;
}
.header {
    display: flex;
    align-items: center;
    background-color: #00a6a7;
    padding: 0 20px;
    height: 56px;
    color: #ffffff;
    .user-img {
        border-radius: 50%;
        border: 1px solid #ffffff;
        width: 32px;
        height: 32px;
        overflow: hidden;
        img {
            display: block;
            width: 100%;
            height: 100%;
        }
    }
    .user-name {
        flex-grow: 1;
        margin-left: 8px;
    }
    .close-btn {
        cursor: pointer;
    }
}
.message-panel {
    height: 375px;
    background-color: #fff;
    padding-top: 16px;
    .message-list {
        padding: 0 20px;
        margin-bottom: 16px;
        .message-item {
            & + .message-item {
                margin-top: 16px;
            }
            .name {
                color: #9fa6b5;
                margin-bottom: 4px;
            }
            .content {
                white-space: pre-line;
                word-break: break-all;
                padding: 8px 16px;
                display: inline-block;
                max-width: 242px;
            }
            &.message-item-self {
                text-align: right;
                .content {
                    text-align: left;
                    background-color: #00a6a7;
                    color: #ffffff;
                    border-radius: 8px 0px 8px 8px;
                }
            }
            &.message-item-opposite {
                text-align: left;
                .content {
                    background-color: #f4f7f9;
                    color: #393e4c;
                    border-radius: 0px 8px 8px 8px;
                }
            }
        }
    }
}
.input-area {
    background-color: #fff;
    height: 118px;
    border-top: 1px solid #f4f7f9;
    position: relative;
    textarea {
        padding: 12px 20px;
        width: 100%;
        height: 62px;
        border: none;
        resize: none;
    }
    .btn-send {
        position: absolute;
        bottom: 16px;
        right: 20px;
    }
}
</style>
