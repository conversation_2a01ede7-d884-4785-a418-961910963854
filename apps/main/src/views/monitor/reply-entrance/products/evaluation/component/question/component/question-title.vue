<template>
    <!-- 判断是否展示背景信息入口 -->
    <div class="question-title-wrap">
        <div v-if="title || questionInfo.questionTitle" class="question-subject" :class="{ 'force-select': isForceSelect }" v-html="title || questionInfo.questionTitle" />
        <div v-if="questionInfo.files && questionInfo.files.length" class="question-title-img-wrap">
            <ImgWrap :key="questionInfo.encryptId" :imgList="questionInfo.files" />
        </div>
    </div>
</template>

<script setup lang="ts">
import ImgWrap from './img-wrap.vue';

const props = defineProps({
    title: {
        type: String,
        default: () => '',
    },
    questionInfo: {
        type: Object,
        default: () => ({}),
    },
    isForceSelect: {
        type: Boolean,
        default: false,
    },
});
</script>

<style lang="less" scoped>
.question-title-wrap {
    margin-bottom: 20px;
    user-select: none;

    .question-title-img-wrap {
        margin-top: 18px;
    }
}
.question-subject {
    // margin-top: 24px;
    font-size: 16px;
    font-weight: 500;
    color: #141414;
    line-height: 24px;
    word-break: break-all;
    white-space: pre-wrap;

    &.force-select {
        :deep(span.answer) {
            margin: 0 4px;
            display: inline-block;
            width: 40px;
            height: 19px;
            line-height: 20px;
            border-bottom: 1px solid #141414;
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            color: var(--primary-color);
        }
    }
}
</style>
