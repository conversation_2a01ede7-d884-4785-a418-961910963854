export const gbaQuestionType = {
    MINE: -10002, // 挖矿
    FRUIT: -10001, // 水果
    ALLOCATION: -10003, // 分配
    DELIVERRY: -10005, // 取快递
    AREAJUDGE: -10004, // 区域划分
    DIRECTIONTASK: -10006, // 方向任务
};

export const GbaQuestionMap = {
    [gbaQuestionType.MINE]: {
        template: () => import('./question/mine/index.vue'),
    },
    [gbaQuestionType.FRUIT]: {
        template: () => import('./question/fruit/index.vue'),
    },
    [gbaQuestionType.DELIVERRY]: {
        template: () => import('./question/express-delivery/index.vue'),
    },
    [gbaQuestionType.AREAJUDGE]: {
        template: () => import('./question/area-judge/index.vue'),
    },
    [gbaQuestionType.DIRECTIONTASK]: {
        template: () => import('./question/direction-task/index.vue'),
    },
    [gbaQuestionType.ALLOCATION]: {
        template: () => import('./question/allocation/index.vue'),
    },
};

// 缓存回合已处理答案标识
export const GBA_QUESTION_ANSWER_KEY = 'SESSION_STORAGE_GBA_QUESTION_ANSWER_KEY';
