<template>
    <div class="scene-question-wrap">
        <div class="content">
            <div v-if="currentQuestion?.backgroundFiles && currentQuestion.backgroundFiles.length" class="background-image">
                <ImgWrap :imgList="currentQuestion.backgroundFiles" />
            </div>
            <p class="text-desc">
                {{ currentQuestion?.backgroundInfo }}
            </p>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import ImgWrap from './component/img-wrap.vue';
import { usePaperStore } from '../../store';
const paperStore = usePaperStore();
const currentQuestion = computed(() => {
    const { backgroundInfoList, currentQuestion } = paperStore;
    const { backgroundInfoEncryptId } = currentQuestion;
    const data = backgroundInfoList.find((item) => item.encryptId === backgroundInfoEncryptId);

    return data;
});

function checkAnswer() {
    return [true, '', {}];
}
defineExpose({
    check: checkAnswer,
    checkQuestionStatus: checkAnswer,
});
</script>

<style lang="less" scoped>
.scene-question-wrap {
    flex: 1;
    display: flex;
    flex-direction: column;

    .content {
        overflow: scroll;
        -webkit-overflow-scrolling: touch;
        transform: rotateZ(0);
        flex: 1;

        .background-image {
            margin: 12px 0;
            width: 100%;
        }

        .text-desc {
            color: #141414;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px;
            text-align: justify;
        }
    }

    .btn-wrap {
        padding: 10px 20px 20px;

        .btn {
            width: 100%;
        }
    }
}
</style>
