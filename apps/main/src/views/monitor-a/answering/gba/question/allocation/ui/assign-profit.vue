<template>
    <div class="assign-profit-container">
        <div class="title-wrap">
            <div class="left">
                <span style="margin-right: 12px">收益分配</span>
                <Text
                    :font-weight="600"
                    :stroke-width="2"
                    :text="`${Math.floor(roundCountdown?.remainingTime?.total / 1000)}S`"
                    font-size="20px"
                    needAnimation
                    strokeColor="#C95D00"
                    textColor="#FFF9DC"
                />
            </div>
            <div class="right">
                <img src="@/assets/images/gba/coin.png" alt="" />
                <span style="margin-right: 22px">本轮收益：{{ reward }}</span>
                <span style="display: flex">
                    <span>剩余可分配：</span>
                    <span :style="{ color: residueReward < 0 ? '#DE3C3C' : 'inherit', width: '39px' }">{{ residueReward }}</span>
                </span>
            </div>
        </div>
        <div class="ppl-wrap">
            <div v-for="(item, index) of gamePlayers" :key="index" class="ppl-item">
                <PlayerCard
                    :avatarConfig="{
                        show: true,
                        url: item.paramImg,
                    }"
                    :castShadow="false"
                    :coin="rewardMap[item.paramId]"
                    :playerName="item.paramName"
                    :showJoin="item.showJoin"
                    size="small"
                />
                <AssignKit ref="assignKitRef" :currentRoundReward="reward" @change="getAllocationReward($event, item.paramId)" />
            </div>
        </div>
        <div :class="[enableSubmitButton ? 'submit-button-enabled' : 'submit-button-disabled']" class="submit-button" @click="submit">
            <Text
                :font-weight="600"
                :shadowFilterConfig="{ x: 0, y: 0.92, blur: 0.92, color: '#419C02' }"
                :stroke-width="1"
                font-size="20px"
                strokeColor="rgba(81, 164, 15, 1)"
                text="提交"
                textColor="rgba(248, 255, 220, 1)"
            />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { sum } from 'lodash-es';
import { onKeyStroke } from '@crm/vueuse-pro';
import Text from '../../../component/text.vue';
import PlayerCard from './player-card.vue';
import AssignKit from './assign-kit.vue';
import { createTimer } from '@/store/time';
import { useNotification } from '../../../component/notification-tips/index';

defineOptions({
    name: 'AssignProfit',
});

const props = defineProps({
    propName: {
        type: Object,
        default: () => ({}),
    },
    distributeTime: {
        type: Number,
        default: 0,
    },
    reward: {
        type: Number,
        default: 0,
    },
    rewardMap: {
        type: Object,
        default: () => ({}),
    },
    gamePlayers: {
        type: Array,
        default: () => [],
    },
    /**
     * 此组件的动画是否执行完毕
     */
    assignProfitAnimationEnd: {
        type: Boolean,
        default: false,
    },
    type: {
        type: String,
        default: '',
    },
});

const emit = defineEmits(['roundCountdownOnFinished', 'allocationSubmit', 'tips']);

const residueRewardMap = ref<any>({});
const residueReward = computed(() => {
    return props.reward - (sum(Object.values(residueRewardMap.value)) || 0);
});
const enableSubmitButton = computed(() => residueReward.value === 0);
const useNotificationTips = useNotification();

function submit() {
    if (enableSubmitButton.value) {
        emit('allocationSubmit', residueRewardMap.value);
        roundCountdown.destroy();
    }
    if (!enableSubmitButton.value && residueReward.value > 0) {
        useNotificationTips.open({
            type: 'tips-message',
            text: '剩余金币未分配完',
        });
    }
}

const assignKitRef = ref<any>();

function getAllocationReward(value: any, userId: any) {
    residueRewardMap.value[userId] = value;
}

const roundCountdown = createTimer();

function manualAssignSubmit() {
    if (enableSubmitButton.value) {
        emit('allocationSubmit', residueRewardMap.value);
    } else {
        emit('roundCountdownOnFinished');
    }
    roundCountdown.destroy();
}

onKeyStroke(['Enter'], (e) => {
    e.preventDefault();
    submit();
});
watch(
    () => props.assignProfitAnimationEnd,
    (value) => {
        if (value && props.type === 'assign') {
            if (props.distributeTime === 0) {
                manualAssignSubmit();
                roundCountdown.stop();
                return;
            }
            roundCountdown?.start({
                finishTime: (t) => t + props.distributeTime,
                onFinished: () => manualAssignSubmit(),
            });
        }
    },
    { immediate: true },
);
</script>

<style lang="less" scoped>
.assign-profit-container {
    --border-width: 5px;

    // width: 780px;
    height: 100%;
    // background-image: linear-gradient(179.99998deg, #ffc41f 0%, #fdffee 96%);
    // border-radius: 20px;
    // border: var(--border-width) solid #fff;

    position: relative;
    z-index: 1;

    padding: 16px 24px 0;

    display: flex;
    flex-direction: column;
    row-gap: 20px;

    .title-wrap {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .left {
            color: #894d00;
            font-size: 24px;
            font-weight: 600;
            line-height: 34px;
            display: flex;
        }

        .right {
            height: 38px;
            display: flex;
            align-items: center;
            background: linear-gradient(179.99998deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 230, 203, 0.5) 100%);
            border: 1px solid rgba(255, 255, 255, 0.8);
            border-radius: 29.53px;
            padding-right: 15px;
            color: #894d00;
            font-size: 16px;
            font-weight: 600;
            line-height: 20px;

            img {
                width: 52px;
                height: 40px;
                margin-right: -10px;
            }
        }
    }

    .ppl-wrap {
        display: flex;
        justify-content: space-between;

        .ppl-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            row-gap: 20px;
        }
    }

    .submit-button {
        position: absolute;
        bottom: calc(-1px - var(--border-width));
        width: 153px;
        height: 40px;
        left: 50%;
        transform: translateX(-50%);

        display: flex;
        align-items: center;
        justify-content: center;

        background-image: url(https://img.bosszhipin.com/static/file/2024/ypsi2athgt1726796171828.svg);

        &.submit-button-enabled {
            cursor: pointer;
        }

        &.submit-button-disabled {
            // background-image: url(https://img.bosszhipin.com/static/file/2024/lkii6un1iq1726796172017.svg);
            cursor: not-allowed;
            opacity: 0.5;
        }
    }
}
</style>
