<template>
    <div class="question-number-info-wrap">
        <div class="question-number-wrap">
            <span class="current-question-number">{{ currentIndex }}</span>
            <span class="question-number-split">/</span>
            <span class="total-question-number">{{ questionTotal }}</span>
        </div>
        <p v-if="questionType" class="question-type-tag">{{ questionType }}</p>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { usePaperStore } from '../store';
import { questionTypeMap } from '../constant';
const paperStore = usePaperStore();
const currentIndex = computed(() => {
    const number = paperStore.currentQuestionIndex + 1;
    return number < 10 ? `0${number}` : number;
});
const questionTotal = computed(() => paperStore.allQuestionList?.length || 0);
const questionType = computed(() => {
    const { questionTypeName, questionType, backgroundInfoType } = paperStore.currentQuestion || {};
    const backgroundInfoTypeName = questionTypeMap[backgroundInfoType]?.shortName || '';
    const shortName = questionTypeMap[questionType]?.shortName || '';

    return questionTypeName || backgroundInfoTypeName || shortName;
});
</script>
<style lang="less" scoped>
.question-number-info-wrap {
    display: flex;
    font-style: normal;
    font-weight: 400;
    text-align: justify;
    align-items: end;

    .question-number-wrap {
        display: flex;
        // align-items: center;
        align-items: end;

        span {
            color: #292929;
            font-family: 'Kanzhun';
            font-size: 16px;
            line-height: 19px;

            &.current-question-number {
                color: #0d9ea3;
                font-size: 26px;
                line-height: 31px;
                margin-top: 1px;
                transform: translateY(3px);
            }

            &.question-number-split {
                margin: 0 4px;
            }
        }
    }

    .question-type-tag {
        margin-left: 8px;
        margin-bottom: 1px;
        padding: 0px 5px;
        background: #f0f0f0;
        border-radius: 4px;
        color: #2a2a2a;
        font-size: 13px;
        line-height: 18px;
    }
}
</style>
