// invoke API 生成文件

import API from './api';
import { downloadFile, get, post, postForm } from './http';

type type_API = typeof API;

const createRequest = function (obj: type_API[keyof type_API]) {
    const result: { [key: string]: any } = {};
    const array = Object.entries(obj);

    for (let index = 0; index < array.length; index++) {
        const [key, value] = array[index];
        if (key.startsWith('downloadFile')) {
            result[key] = <T extends { [x: string]: any }, U = any>(data: T, options: any) => downloadFile<U>(value, data, options);
        } else if (key.startsWith('postForm')) {
            result[key] = <T extends { [x: string]: any }, U = any>(data: T, options: any) => postForm<U>(value, data, options);
        } else if (key.startsWith('post')) {
            result[key] = <T extends { [x: string]: any }, U = any>(data: T, options: any) => post<U>(value, data, options);
        } else if (key.startsWith('json')) {
            result[key] = <T extends { [x: string]: any }, U = any>(data: T, options: any) => post<U>(value, data, { ...options, requestType: 'json' });
        } else {
            result[key] = <T extends { [x: string]: any }, U = any>(data: T, options: any) => get<U>(value, data, options);
        }
    }

    return result;
};

const createApiInvoke = function (obj: type_API) {
    const result: { [key: string]: any } = {};
    const array = Object.entries(obj);

    for (let index = 0; index < array.length; index++) {
        const [key, value] = array[index];

        if (typeof value === 'object') {
            result[key] = createRequest(value);
        }
    }

    return result;
};

export default createApiInvoke(API);
