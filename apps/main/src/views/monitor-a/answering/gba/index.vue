<template>
    <div class="gba-wrap">
        <div v-if="answerLoading" class="loading-wrap">
            <b-loading type="dot"> 加载中 </b-loading>
        </div>
        <OperationTips v-if="isShowOperationTips" @onOver="onOperationTipsOver" />
        <Guidanc v-else-if="isGuidanc" :simulateStatus="isSimulate" @over="guidancOver" @simulate="startSimulate" />
        <div v-else class="gba-body">
            <div class="gba-inner">
                <component :is="template" v-if="isSimulate" isSimulate @gameOver="simulateOver" @roundOver="onRoundOver" @roundChange="() => {}" />
                <component :is="template" v-else @gameOver="submitGameData" @roundOver="onRoundOver" @roundChange="onRoundChange" />
                <Notification />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { SessionStorage } from '@/utils/storage';
import { computed, nextTick, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import Guidanc from './component/guidanc.vue';
import { useNotification } from './component/notification-tips/index';
import Notification from './component/notification-tips/index.vue';
import OperationTips from './component/operation-tips.vue';
import { useGbaStore } from './store';
import { GBA_QUESTION_ANSWER_KEY, GbaQuestionMap } from './type';
import { useMonitorStore } from '@/store/use-monitor-store';
import { debounce } from 'lodash-es';
import { useEvaluationEvents } from '../hooks/useEvaluationEvents';
import { useRouteParamId } from '../../hooks/useRouteParamId';

const $route = useRoute();
const { seqId, examId } = useRouteParamId();
const { onSubmitPaper, onQuestionChange } = useEvaluationEvents({ examId, seqId });

const useNotificationTips = useNotification();
const gbaStore = useGbaStore();
const monitorStore = useMonitorStore();

const { getGbaAnswerListPreview, getGbaAnswerList, questionNext, modifyCurrentQuestionRoundList, roundDataModify } = gbaStore;

const isSimulate = ref(false);
const answerLoading = ref(true);
const isError = ref(false);
const isGuidanc = ref(true);
const isShowOperationTips = ref(true);

const template = computed(() => {
    let template;
    if (gbaStore.currentQuestion.id) {
        template = GbaQuestionMap[gbaStore.currentQuestion.id as number].template;
    }
    return template;
});

// 引导语页面开启模拟
function startSimulate() {
    gbaStore.restoreSimulateList();

    nextTick(() => {
        isSimulate.value = true;
        isGuidanc.value = false;
    });
}

function simulateOver() {
    useNotificationTips.open({
        type: 'template-message',
        action: 'simulate-over',
        time: 3000,
        onClose: () => {
            isGuidanc.value = true;
            isSimulate.value = false;
        },
    });
}

function onOperationTipsOver() {
    if (!monitorStore.isPreview) {
        postLog('eval_operating_instruction', {
            encExamId: examId as string,
        });
    }

    isShowOperationTips.value = false;
}

// 引导语展示结束
function guidancOver() {
    isGuidanc.value = false;
}

// 回合结束(提交回合数据动作)
function onRoundOver(data: any) {
    const questionId = gbaStore.currentQuestion.id;
    const historyData = SessionStorage.get(GBA_QUESTION_ANSWER_KEY);
    const history = historyData && historyData[questionId] ? historyData[questionId] : [];

    const paramsData = {
        questionType: 17, // gba写死参数 -- 不可修改
        history,
        encSnapshotId: data.encSnapshotId,
        answerContent: {
            ...data,
        },
    };
    const { simulateList, roundList } = gbaStore.currentQuestion;
    let newSimulateList = [...simulateList];
    let newRoundList = [...roundList];

    // 针对快递题进行本地数据修改;
    if (isSimulate.value || monitorStore.isPreview) {
        newSimulateList = roundDataModify(paramsData, simulateList);
    } else {
        newRoundList = roundDataModify(paramsData, roundList);
    }

    gbaStore.currentQuestion = {
        ...gbaStore.currentQuestion,
        simulateList: newSimulateList,
        roundList: newRoundList,
    };

    submitRoundData(paramsData);
}

// 提交回合数据
async function submitRoundData(params: any) {
    const newParams = {
        encryptExamId: examId as string,
        ...params,
    };

    // 预览或者模拟作答 不用提交
    if (isSimulate.value || monitorStore.isPreview) {
        return;
    }

    try {
        const { code, data, message } = await Invoke.gba.postGbaAddAnswer(newParams);

        if (code !== 0) {
            // 如果用户正在答题中被限制 99:被踢出  31:需要强制刷新
            if (code === 99 || code === 31) {
                isGuidanc.value = true;
                return;
            }

            pushSessionStorage(newParams);
        }
    } catch (err) {
        pushSessionStorage(newParams);
    }
}

// 同步游戏回合
const submitGameData = debounce(
    async () => {
        // 预览逻辑处理
        if (monitorStore.isPreview) {
            // 如果是最后一题 调用交卷逻辑
            if (gbaStore.currentIndex === gbaStore.questionList.length - 1) {
                onSubmitPaper({ commitType: 1 }); // 用户点击交卷
            } else {
                isGuidanc.value = true;
                questionNext();
            }

            return;
        }

        const questionId = gbaStore.currentQuestion.id;
        const historyData = SessionStorage.get(GBA_QUESTION_ANSWER_KEY);
        const history = historyData && historyData[questionId] ? historyData[questionId] : [];

        const { code, data, message } = await Invoke.gba.postGbaGameCommit({
            encryptExamId: examId,
            questionId,
            history,
        });

        if (code === 0) {
            // 如果是最后一题 调用交卷逻辑
            if (gbaStore.currentIndex === gbaStore.questionList.length - 1) {
                onSubmitPaper({ commitType: 1 }); // 1: 用户点击交卷
            } else {
                isGuidanc.value = true;
                questionNext();
                useNotificationTips.close();
            }
        }
    },
    300,
    { leading: true },
);

// 新增一条数据
function pushSessionStorage(paramsData: any) {
    const { id } = gbaStore.currentQuestion;
    const data = SessionStorage.get(GBA_QUESTION_ANSWER_KEY);

    const currentQuestionAnswerList = data && data[id] ? data[id] : [];
    const isHas = currentQuestionAnswerList.findIndex((item: { encSnapshotId: any }) => item.encSnapshotId === paramsData.encSnapshotId);
    if (isHas < 0) {
        currentQuestionAnswerList.push({ ...paramsData, history: null });
    } else {
        currentQuestionAnswerList[isHas] = { ...paramsData, history: null };
    }

    SessionStorage.set(GBA_QUESTION_ANSWER_KEY, {
        [gbaStore.currentQuestion.id]: currentQuestionAnswerList,
    });
}

// 获取试卷数据
async function getData() {
    answerLoading.value = true;

    if (monitorStore.isPreview) {
        await getGbaAnswerListPreview({ key: $route.query.key as string });
        answerLoading.value = false;
    } else {
        const res = await getGbaAnswerList({
            encryptExamId: examId as string,
        });

        const { clickOperatingInstruction } = res?.data || { clickOperatingInstruction: 0 };

        // 判断之前是否点击过 操作指引或作答说明
        if (clickOperatingInstruction === 1) {
            isShowOperationTips.value = false;
        }

        checkCurrentQuestionStorage();
    }
    nextTick(() => {
        if (gbaStore.currentQuestion.id) {
            answerLoading.value = false;
        }
    });
}

// 查看是否有缓存答案
function checkCurrentQuestionStorage() {
    const { id, roundList, answerStatus } = gbaStore.currentQuestion;
    const data = SessionStorage.get(GBA_QUESTION_ANSWER_KEY);
    const currentQuestionAnswerList = data && data[id] ? data[id] : [];

    // 查看当前服务端是否有未回答的回合
    const notAnswerList = roundList.filter((item) => item.answerStatus === 0);

    // 服务端没有未答的题 但是当前题没有提交
    if (answerStatus === 0 && notAnswerList.length < 1) {
        isGuidanc.value = false;
        return;
    }

    // 服务端有未答的题
    if (answerStatus === 0 && notAnswerList.length) {
        const sessionStorageAnswerList = [];
        notAnswerList.forEach((item) => {
            // 查看是否有缓存的答案
            const sessionStorageAnswer = currentQuestionAnswerList.find((obj: { encSnapshotId: string }) => obj.encSnapshotId === item.encryptId);
            if (sessionStorageAnswer) {
                modifyCurrentQuestionRoundList({ ...sessionStorageAnswer });
                sessionStorageAnswerList.push({ ...sessionStorageAnswer });
            }
        });
    }

    // 当前试题服务端没有未答的题
    if (notAnswerList.length === 0) {
        isGuidanc.value = false;
    }
}

// 回合切换上报埋点 （埋点计算每道题展示的时间）
function onRoundChange(data: { type: string; currentQuestionId: string }) {
    onQuestionChange(data);
}

onMounted(async () => {
    await getData();
});
</script>

<style lang="less" scoped>
.gba-wrap {
    position: relative;
    height: 100%;
}
.gba-body {
    margin: 0 auto;
    width: 860px;
    height: 423px;
    padding: 24px 24px;
    background: #ffffff;
    border-radius: 12px;
    user-select: none;

    .gba-inner {
        position: relative;
        width: 100%;
        height: 100%;

        .notification-wrap {
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
        }
    }
}

.loading-wrap {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    z-index: 999;
    background: #fff;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
</style>
