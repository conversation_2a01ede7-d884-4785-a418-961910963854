import Bowser from 'bowser';
import { <PERSON><PERSON> } from './tools/cookie';

export function parseURL(url: any) {
    const a = document.createElement('a');
    a.href = url;
    return {
        origin: a.origin,
        pathname: a.pathname,
        source: url,
        protocol: a.protocol.replace(':', ''),
        host: a.hostname,
        port: a.port,
        query: a.search,
        params: (function () {
            const params: any = {};
            const seg = a.search.replace(/^\?/, '').split('&');
            const len = seg.length;
            let p: string[] = [];
            for (let i = 0; i < len; i++) {
                if (seg[i]) {
                    p = seg[i].split('=');
                    params[p[0]] = p[1];
                }
            }
            return params;
        })(),
        hash: a.hash.replace('#', ''),
        path: a.pathname.replace(/^([^/])/, '/$1'),
    };
}

/**
 * 全屏/取消全屏
 * @param element 全屏/取消全屏的元素
 */
export function toggleFullScreen(element: HTMLElement) {
    if (!document.fullscreenElement) {
        element.requestFullscreen?.();
        // @ts-ignore
        element.webkitRequestFullscreen?.(); // Safari
        // @ts-ignore
        element.msRequestFullscreen?.(); // IE11
    } else {
        document.exitFullscreen?.();
        // @ts-ignore
        document.webkitExitFullscreen?.(); // Safari
        // @ts-ignore
        document.msExitFullscreen?.(); // IE11
    }
}

/**
 * 获取uuid
 * @returns
 */
export function getUUID() {
    let d = new Date().getTime();
    const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        const r = (d + Math.random() * 16) % 16 | 0;
        d = Math.floor(d / 16);
        return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
    });
    return uuid;
}

// 设置浏览器唯一标识
export function setUUID() {
    if (!Cookie.get('wd_guid2')) {
        Cookie.set('wd_guid2', getUUID(), undefined, undefined, '/');
    }
    return Cookie.get('wd_guid2');
}

// 清除当前文档中的选中内容（selection）
export function clearSelection() {
    if (typeof window.getSelection !== 'undefined') {
        const sel = window.getSelection();
        if (sel?.removeAllRanges) {
            sel.removeAllRanges();
        } else if (sel?.empty) {
            sel.empty();
        }
    }
}

// 删除当前url中指定的query
export function delUrlParam(keys: string[]) {
    const params = new URLSearchParams(window.location.search);
    for (const key of keys) {
        params.delete(key);
    }
    return `${window.location.origin}${window.location.pathname}?${params.toString()}`;
}

// 打开新 tab
export function openNewTab(url: string) {
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('target', '_blank');
    link.style.display = 'none';
    // 触发点击
    document.body.appendChild(link);
    link.click();
    // 然后移除
    document.body.removeChild(link);
}

// 对象序列化
export function formatParams(obj: { [x: string]: any }, needBlank = false) {
    let str = '';

    const entries = Object.entries(obj);
    entries.forEach((item) => {
        if (needBlank || typeof item[1] === 'number' || item[1]) {
            str += str.length ? `&${item[0]}=${item[1]}` : `${item[0]}=${item[1]}`;
        }
    });

    return str;
}

// 获取浏览器版本
export function getBowser() {
    const ua = window.navigator.userAgent;
    if (!ua) {
        return '';
    }
    const browser = Bowser.getParser(ua);
    const { name, version } = browser.getBrowser();
    return `${name} ${version}`;
}

// 数字补0
export function addZero(val: number): string {
    if (Number.isNaN(val)) {
        return `${val}`;
    }
    return val > 9 ? `${val}` : `0${val}`;
}

// 日期格式化
export function formatDate(val: any, formatStr = 'yyyy-MM-dd') {
    if (!val) {
        return '';
    }
    let date = val;
    if (typeof val === 'string') {
        // 字符类型
        date = new Date(val.replace(/-/g, '/'));
    } else if (typeof val === 'number') {
        // 时间戳
        date = new Date(val);
    }

    let str = formatStr;
    str = str.replace(/yyyy|YYYY/, `${date.getFullYear()}`);
    str = str.replace(/MM/, addZero(date.getMonth() + 1));
    str = str.replace(/M/g, `${date.getMonth() + 1}`);
    str = str.replace(/dd|DD/, addZero(date.getDate()));
    str = str.replace(/d|D/g, `${date.getDate()}`);
    str = str.replace(/hh|HH/, addZero(date.getHours()));
    str = str.replace(/h|H/g, `${date.getHours()}`);
    str = str.replace(/mm/, addZero(date.getMinutes()));
    str = str.replace(/m/g, `${date.getMinutes()}`);
    str = str.replace(/ss|SS/, addZero(date.getSeconds()));
    str = str.replace(/s|S/g, `${date.getSeconds()}`);
    return str;
}

/** 获取表单数据 */
export function getFormData(data: Record<string, any> = {}) {
    const fd = new FormData();
    // 禁止访问原型链上的属性
    for (const key in data) {
        if (Object.prototype.hasOwnProperty.call(data, key)) {
            fd.append(key, data[key]);
        }
    }

    return fd;
}

export function dataURLtoFile(dataurl: any, filename: any) {
    const arr = dataurl.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, { type: mime });
}

/**
 * base64转file
 */
// 将 Base64 编码字符串转换为 Blob 对象

export function base64ToUint8Array(base64: string) {
    const binaryString = window.atob(base64); // 已弃用，但可以使用 TextDecoder 解决
    const len = binaryString.length;
    const bytes = new Uint8Array(len);
    for (let i = 0; i < len; i++) {
        bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes;
}
// 将 Base64 字符串转化为 File 对象
export function base64ToFile(base64String: string, fileName: string) {
    const parts = base64String.split(',') as any;
    const mimeType = parts[0].match(/:(.*?);/)[1];
    const base64Data = parts[1];
    const byteArrays = base64ToUint8Array(base64Data);
    const blob = new Blob([byteArrays], { type: mimeType });
    const file = new File([blob], fileName, { type: mimeType });
    return file;
}

// 获取颜色分布
function getColorDistribution(imageData: any) {
    const colorDist = Array.from({ length: 256 }).fill(0); // 存储每个颜色值的出现次数
    for (let i = 0; i < imageData.length; i += 4) {
        const r = imageData[i];
        const g = imageData[i + 1];
        const b = imageData[i + 2];
        const brightness = 0.2126 * r + 0.7152 * g + 0.0722 * b; // 计算亮度
        colorDist[Math.round(brightness)] = (colorDist[Math.round(brightness)] as number) + 1; // 增加亮度对应的颜色值出现次数
    }
    return colorDist;
}

export function checkCameraBlockage(base64String: string) {
    // 改成promise写法
    return new Promise((resolve, reject) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d') as any;
        const img = new Image();
        img.src = base64String;
        img.onload = () => {
            canvas.width = img.width;
            canvas.height = img.height;
            // 将视频帧绘制到 canvas
            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
            // 获取图像数据
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;

            // 计算整个图像的平均亮度
            let totalBrightness = 0;
            for (let i = 0; i < data.length; i += 4) {
                // RGB 转换为亮度 (0.2126*R + 0.7152*G + 0.0722*B)
                const brightness = 0.2126 * data[i] + 0.7152 * data[i + 1] + 0.0722 * data[i + 2];
                totalBrightness += brightness;
            }
            const averageBrightness = totalBrightness / (data.length / 4);

            // 判断亮度是否低于某个阈值
            if (averageBrightness < 30) {
                // 阈值可以根据实际情况调整
                resolve(true);
            } else {
                // 检查颜色分布是否高度集中
                const colorDist = getColorDistribution(data);
                const maxColorCount = Math.max(...(colorDist as number[]));
                const totalPixels = canvas.width * canvas.height;
                const colorDistributionThreshold = 0.9; // 颜色分布阈值可以根据实际情况调整
                if (maxColorCount > totalPixels * colorDistributionThreshold) {
                    resolve(true);
                } else {
                    resolve(false);
                }
            }
        };
    });
}

/**
 * 截取视频的当前帧
 */
export function getCurrentFrame(video: HTMLVideoElement) {
    const canvas = document.createElement('canvas');
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    const ctx = canvas.getContext('2d');
    ctx?.drawImage(video, 0, 0, canvas.width, canvas.height);
    return canvas.toDataURL('image/png');
}

export * from './libs/proto';
export * from './mitt';
export * from './tools/cookie';
export * from './tools/fullscreen';
export * from './tools/localstorage';
export * from './tools/md5';
export * from './tools/string-operate';
