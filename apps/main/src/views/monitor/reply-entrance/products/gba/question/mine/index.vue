<template>
    <div class="round-content">
        <div class="title-wrap">
            <p class="title">{{ numberToChinese(gbaStore.currentIndex + 1, false) }}、{{ gbaStore.currentQuestion.showName }}</p>
            <div class="progress-wrap">
                <Progress :passed="roundPassed" :total="roundList.length" />
            </div>
            <div class="gold-wrap">
                <p class="gold-icon" />
                <Button theme="orange">
                    <span>金币: </span>
                    <NumberVue :value="goldNumber.num" />
                </Button>
            </div>
        </div>
        <div class="mine-wrap">
            <div v-for="(mine, index) in mines" :key="index" class="mine-item" @click="onSelectMine(mine, index)">
                <div
                    class="mine-item-bg"
                    :style="{
                        backgroundImage: `url(${mine.bgImg})`,
                    }"
                />
                <p class="btn">
                    <Button>{{ index + 1 }}号矿山</Button>
                </p>
                <p v-if="mine?.selectShow" class="shovel" />
                <div class="result-info-wrap">
                    <div v-if="mine?.selectShow" class="result-info">
                        <p class="income">收益：+¥{{ mine.profit }}</p>
                        <p class="outgo">损失：-¥{{ mine.loss }}</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="countdown-info-wrap">倒计时 {{ Math.floor(roundCountdown?.remainingTime.total / 1000) }}S</div>
    </div>
    <div class="container">
        <div class="box" />
    </div>
</template>

<script setup lang="ts">
import { createTimer, timeCenter } from '@/store/time';

import { onKeyStroke } from '@crm/vueuse-pro';

import { sum } from 'lodash-es';
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue';
import NumberVue from '../../component/animation-number.vue';
import Button from '../../component/button.vue';

import { useNotification } from '../../component/notification-tips';
import Progress from '../../component/progress.vue';
import { useGbaStore } from '../../store';
import { usePathAnimation } from './usePathAnimation';
import { numberToChinese } from '@/utils/number-to-chinese';

const props = defineProps({
    isSimulate: {
        type: Boolean,
        default: false,
    },
});

const emits = defineEmits(['tips', 'roundOver', 'gameOver', 'roundChange']);

const gbaStore = useGbaStore();
const tips = useNotification();

const roundList = computed(() => {
    if (props.isSimulate) {
        // 模拟模式下 使用模拟数据
        return gbaStore.currentQuestion.simulateList;
    } else {
        return gbaStore.currentQuestion.roundList;
    }
});

const mines = ref<any>([]);
const roundPassed = ref(0); // 已处理回合数
const operable = ref(true);
const goldNumber = reactive({
    num: gbaStore.currentQuestion.reward,
});

function defaultSelectData() {
    return {
        encSnapshotId: '', // 当前回合id
        paramId: 0, // 用户选择的数据（矿山id）
        chooseTimeTs: 0, // 用户选择的时间点
        showTimeTs: 0, // 当前回合首次展示的时间
        timeoutTs: 0, // 当前回合超时时间点 (与 chooseTime 互斥);
        x: 0, // 金币飞入起始坐标x
        y: 0, // 金币飞入起始坐标y
        profit: 0, // 当前获得的金币
    };
}

// 缓存已选择的数据，用于数据提交与金币飞入坐标
const selectData = ref(defaultSelectData());

const { currentTime } = timeCenter;

const roundTime = computed(() => gbaStore.currentQuestion.chooseTime);
const roundCountdown = createTimer();

const mineingTime = 1 * 1000; // 选择后挖矿动作时长;
const roundOverCountdown = createTimer();

// 键盘选择矿山
onKeyStroke(['1', '2', '3', '4'], (e) => {
    const index = Number(e.key) - 1;
    onSelectMine(mines.value[index], index);
});

const { startAnimation } = usePathAnimation();

// 选择矿山
function onSelectMine(data: any, index: number) {
    if (!operable.value) {
        return;
    }
    if (selectData.value.x || selectData.value.y) {
        return;
    }

    const endElements = document.querySelectorAll('.mine-item');
    const endElement = endElements[index];
    const endElementBound = endElement!.getBoundingClientRect();

    // 设置已选择的数据
    selectData.value = {
        ...selectData.value,
        chooseTimeTs: currentTime.value,
        x: endElementBound!.left + endElementBound!.width / 2,
        y: endElementBound!.top + endElementBound!.height / 2,
        profit: Number(data.profit) - Number(data.loss), // 每回合的金额
        paramId: data.paramId,
    };

    data.selectShow = true;
    roundCountdown?.stop();
    roundOverCountdown.start({
        key: 'roundOverCountdown',
        finishTime: () => Date.now() + mineingTime,
        onFinished: () => {
            // 调用回合结束
            roundEnd();
        },
    });
}

// 回合结束3
function roundEnd(isTimeOut: boolean = false) {
    roundPassed.value += 1;
    operable.value = false;
    emits('roundOver', { ...selectData.value });

    if (!isTimeOut) {
        const endElement = document.querySelector('.gold-wrap');
        const endElementBound = endElement!.getBoundingClientRect();

        startAnimation(
            [
                {
                    size: [100, 50],
                    position: [selectData.value.x, selectData.value.y],
                    opacity: 1,
                },
                {
                    size: [100, 50],
                    position: [(endElementBound.left + selectData.value.x + 20) / 2, (endElementBound.top + (selectData.value.y + 20) * 2) / 3],
                    opacity: 0.75,
                },
                {
                    size: [100, 50],
                    position: [endElementBound.left + 20, endElementBound.top + 20],
                    opacity: 0.5,
                },
            ],
            () => {
                roundContinue();
            },
        );
    } else {
        tips.open({
            type: 'tips-message',
            text: '作答超时',
            time: 1000,
            onClose: () => {
                roundContinue();
            },
        });
    }
}

// 继续下一个回合
function roundContinue() {
    operable.value = true;
    goldNumber.num += selectData.value.profit;

    // 回合结束-游戏结束
    if (roundPassed.value >= roundList.value.length) {
        emits('roundChange', { type: 'end' });
        gameOver();
        return;
    }

    const roundData = roundList.value[roundPassed.value];
    emits('roundChange', { currentQuestionId: roundData.encryptId });

    nextTick(() => {
        setRoundData();
        roundCountdown?.start({
            key: 'roundCountdown',
            finishTime: () => Date.now() + roundTime.value,
            onFinished: () => {
                selectData.value.timeoutTs = currentTime.value;
                roundEnd(true);
            },
        });
    });
}

// 所有回合结束 游戏结束
function gameOver() {
    operable.value = false;

    // 模拟模式下不需要走游戏结束弹窗
    if (!props.isSimulate) {
        tips.open({
            type: 'template-message',
            action: 'mine-view',
            number: goldNumber.num,
            onNext: () => {
                emits('gameOver');
            },
        });
    } else {
        emits('gameOver');
    }
}

// 设置回合默认数据
function setRoundData(index: number = -1) {
    const roundPassedIndex = index > -1 ? index : roundPassed.value;
    const roundData = roundList.value[roundPassedIndex];

    // 设置已选择的数据
    selectData.value = {
        ...defaultSelectData(),
        showTimeTs: currentTime.value, // 设置回合曝光时间
        encSnapshotId: roundData.encryptId, // 回合ID
    };

    mines.value = roundData.paramList.map((item) => {
        const data = gbaStore.currentQuestion.assetsCongig.find((obj) => obj.paramId === item.paramId);
        return {
            ...item,
            selectShow: false,
            bgImg: data?.paramImg || '',
        };
    });
}

onMounted(() => {
    // 查看是否还有未答回合
    const index = roundList.value.findIndex((item) => item.answerStatus === 0);

    // 计算已答回合金额
    const number = sum(
        roundList.value.map((item) => {
            if (item.answerStatus === 1 && item.reward) {
                return item.reward;
            }
            return 0;
        }),
    );

    goldNumber.num = number || 0;

    // 如果所有回合回答完毕 直接展示结算弹窗
    if (index < 0) {
        roundPassed.value = roundList.value.length;
        setRoundData(roundList.value.length - 1);
        gameOver();
    } else {
        roundPassed.value = index;
        setRoundData();
        tips.open({
            type: 'countdown-start-message',
            time: 3500,
            onClose: () => {
                roundContinue();
            },
        });
    }
});
</script>

<style lang="less" scoped>
.coin-class {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    background: red;
}

.boll {
    position: fixed;
    width: 10px;
    height: 10px;
    border-radius: 10px;
    background: red;
    z-index: 9999;
}

.round-content {
    position: relative;
    padding: 32px;
    background: linear-gradient(179.99998deg, #ffd37e 5%, #fff6dd 47%, #fcb936 93%);
    box-shadow: inset 0px 4px 35px 0px rgba(88, 49, 0, 0.4);
    backdrop-filter: blur(18.5px);
    // background-size: 100% auto;
    width: 100%;
    height: 100%;
    overflow: hidden;

    &::after {
        content: '';
        position: absolute;
        left: 0;
        height: 261px;
        right: 0;
        bottom: -5px;
        background-image: url('https://img.bosszhipin.com/static/file/2024/h6xy4k3ehq1725606964354.png.webp');
        background-size: 100% auto;
        z-index: -1;
    }

    .title-wrap {
        position: relative;
        height: 38px;
        display: flex;
        justify-content: space-between;

        .title {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            color: #734100;
            font-size: 24px;
            font-style: normal;
            font-weight: 600;
            line-height: 22px;
        }

        .progress-wrap {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translateX(-50%) translateY(-50%);
        }

        .gold-wrap {
            position: absolute;
            right: 0;

            :deep(.button-wrap) {
                width: 164px;

                .text {
                    margin-left: 10px;
                }
            }

            .gold-icon {
                position: absolute;
                left: -9px;
                width: 38px;
                height: 38px;
                background-image: url(https://img.bosszhipin.com/static/file/2024/mn0equr2ru1725349213782.png.webp);
                background-position: center;
                background-size: 72px;
                z-index: 4;
            }
        }
    }

    .mine-wrap {
        padding: 0 23px;
        margin-top: 49px;
        display: flex;
        justify-content: space-between;

        .mine-item {
            position: relative;
            cursor:
                url(https://img.bosszhipin.com/static/file/2024/ugtr0lrmwu1728704191173.svg) 0 62,
                pointer;
            display: flex;
            justify-content: center;
            width: 142px;
            height: 142px;
            border: 5px solid #fffee0;
            background: linear-gradient(179.99998deg, #ffba38 0%, #ffffff 100%);
            border-radius: 22px;

            &:hover {
                border: 5px solid rgb(255, 221, 0);
            }

            &::after {
                content: '';
                position: absolute;
                left: 50%;
                top: 160px;
                // opacity: 0.20;
                width: 80px;
                height: 18px;
                transform: translateX(-50%);
                background: #000;
                background: radial-gradient(#000 0%, rgba(0, 0, 0, 0) 100%);
                border-radius: 100%;
                opacity: 0.15;
                filter: blur(4px);
            }

            .mine-item-bg {
                margin: 0 auto;
                width: 132px;
                height: 132px;
                background-size: auto 100%;
                background-position: center;
                border-radius: 16px;
                overflow: hidden;
            }

            .mine-mountain {
                margin-top: -14px;
            }

            .btn {
                margin: 0 auto;
                position: absolute;
                top: 100%;
                transform: translateY(-50%);
                width: 98px;
                height: 35px;

                :deep(.button-wrap) {
                    height: 35px;
                }
            }

            // 铁锹
            .shovel {
                position: absolute;
                top: -37px;
                left: 101px;
                width: 62px;
                height: 62px;
                background-image: url(https://img.bosszhipin.com/static/file/2024/8ommig95771725354231646.png.webp);
                background-size: auto 100%;
                animation-name: shovelMining;
                animation-duration: 0.3s;
                animation-iteration-count: 3;
            }

            // 收益结果
            .result-info-wrap {
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translateX(-50%) translateY(-50%);
                width: 110px;
                height: 57px;
                overflow: hidden;

                .result-info {
                    padding: 6px 10px;
                    border-radius: 10px;
                    background: #ffffff;
                    animation-name: resultShow;
                    animation-duration: 1s;
                    animation-iteration-count: 1;
                    transform: scale(0.01);
                    animation-fill-mode: forwards;

                    & > p {
                        font-size: 15px;
                        font-style: normal;
                        font-weight: 500;
                        line-height: normal;
                        white-space: nowrap;

                        &.income {
                            color: #338c00;
                        }

                        &.outgo {
                            margin-top: 3px;
                            color: #db0000;
                        }
                    }
                }
            }
        }
    }

    .countdown-info-wrap {
        position: absolute;
        bottom: -1px;
        left: 50%;
        transform: translateX(-50%);
        width: 150px;
        height: 38px;
        background-image: url(https://img.bosszhipin.com/static/file/2024/2gwkqypex21725348290417.png.webp);
        background-size: 100% 100%;
        text-align: center;
        color: #fff9dc;
        font-size: 18px;
        font-style: normal;
        font-weight: 600;
        text-shadow: 0 0.87px 0.87px rgba(218, 79, 12, 0.7);
        line-height: 42px;
    }
}

@keyframes shovelMining {
    0% {
        transform: rotate(0deg);
    }

    50% {
        transform: rotate(20deg);
    }

    100% {
        transform: rotate(0deg);
    }
}

@keyframes resultShow {
    0% {
        transform: scale(0.01);
    }

    100% {
        transform: scale(1);
    }
}
</style>
