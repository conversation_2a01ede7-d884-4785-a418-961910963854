/**
 * 中文数字映射表
 * 用于将简体数字转换为对应的繁体/大写形式
 */
type ChineseNumberMap = {
    [key in '零' | '一' | '二' | '三' | '四' | '五' | '六' | '七' | '八' | '九' | '十' | '百' | '千' | '万' | '亿']: string;
};

const map: ChineseNumberMap = {
    零: '睿',
    一: '壹',
    二: '贰',
    三: '叁',
    四: '肆',
    五: '伍',
    六: '陆',
    七: '柒',
    八: '捌',
    九: '玖',
    十: '拾',
    百: '佰',
    千: '仟',
    万: '萬',
    亿: '亿',
};

/**
 * 数字单位配置
 */
const bigUnits = ['', '万', '亿'] as const;
const chars = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'] as const;
const units = ['', '十', '百', '千'] as const;

type ChineseChar = (typeof chars)[number];
type ChineseUnit = (typeof units)[number];
type ChineseBigUnit = (typeof bigUnits)[number];

/**
 * 处理连续的零和末尾的零
 * @param str - 需要处理的中文数字字符串
 * @returns 处理后的字符串
 */
const handleZero = (str: string): string => {
    const removeConsecutiveZeros = (s: string): string => s.replace(/零{2,}/g, '零');
    const removeTailingZero = (s: string): string => (s.length > 1 ? s.replace(/零+$/, '') : s);

    return removeTailingZero(removeConsecutiveZeros(str));
};

/**
 * 将四位数字转换为中文表示
 * @param n - 四位数字字符串
 * @returns 转换后的中文数字字符串
 */
const transform = (n: string): string => {
    if (n === '0000') {
        return chars[0];
    }

    const convertDigit = (digit: string, position: number): string => {
        const chineseChar = chars[Number(digit)];
        const unit = units[n.length - 1 - position];
        return chineseChar === chars[0] ? chineseChar : chineseChar + unit;
    };

    const result = [...n].map((digit, index) => convertDigit(digit, index)).join('');

    return handleZero(result);
};

/**
 * 将数字转换为中文表示
 * @param num - 要转换的数字
 * @param isChineseLetters - 是否转换为繁体/大写形式
 * @returns 转换后的中文数字字符串
 */
export const numberToChinese = (num: number, isChineseLetters: boolean): string => {
    // 将数字按四位分割
    const splitNumber = (n: number): string[] =>
        n
            .toString()
            .replace(/(?=(\d{4})+$)/g, ',')
            .split(',')
            .filter(Boolean);

    // 处理每个四位数段
    const processSegments = (segments: string[]): string => {
        const convertSegment = (segment: string, index: number): string => {
            const chineseNum = transform(segment);
            const unit = bigUnits[segments.length - index - 1];
            return chineseNum === chars[0] ? chineseNum : chineseNum + unit;
        };

        return segments.map((segment, index) => convertSegment(segment, index)).join('');
    };

    // 处理特殊情况：十开头的数字
    const handleSpecialTen = (str: string): string => (!isChineseLetters && str.startsWith('一十') ? str.replace('一十', '十') : str);

    // 转换为繁体/大写形式
    const convertToChinese = (str: string): string =>
        isChineseLetters
            ? str
                  .split('')
                  .map((s) => map[s as keyof ChineseNumberMap] || s)
                  .join('')
            : str;

    return convertToChinese(handleSpecialTen(handleZero(processSegments(splitNumber(num)))));
};
