# Media 类文档

该模块负责管理在线考试监控过程中的 WebRTC 媒体流，包括 PC 摄像头、手机摄像头和 PC 屏幕共享。它封装了 `NEBULARTC` SDK 的核心交互逻辑。

## 核心功能

- 初始化 RTC SDK 实例 (`NEBULARTC`)。
- 获取 RTC 鉴权信息 (`_getSign` API)。
- 创建和管理不同类型 (`camera`, `phone`, `screen`) 的 RTC Client 实例。
- 实现加入/离开 RTC 房间的功能。
- 创建、初始化、发布本地媒体流（摄像头、屏幕共享）。
- 订阅和处理远端媒体流（手机摄像头）。
- 监听和处理 RTC 事件（如连接状态变化、流状态变化、错误事件）。
- 更新 Pinia store (`useMonitorStore`) 中的监控状态。
- 在关键操作节点进行埋点上报 (`BossAnalyticsTrack`)。
- 处理常见的媒体设备错误和权限问题。

## 主要类和方法

### `Media` 类

封装所有 RTC 相关操作的主类。

- **`constructor()`**: 初始化时获取考试配置和基础信息。
- **`rtc` (static getter)**: 获取或创建 `NEBULARTC` SDK 的单例。进行 SDK 的环境设置和日志级别配置。
- **`initRtcAuthInfo()` (static private async)**: 获取并缓存 RTC 鉴权信息（appId, signature, authorization）。
- **`Client` (static property)**: 存储 `camera`, `phone`, `screen` 三种类型的 RTC Client 实例。
- **`getClient(type: RtcClientType, userIdPrefix: string = '')` (private async)**: 获取或创建指定类型的 RTC Client 实例。确保每个 Client 实例与唯一的 userId 绑定（通过 `userIdPrefix` 和基础 userId 组合）。处理 Client 的 `error` 事件。
- **`joinRoom(type: RtcClientType)` (private async)**: 使用指定类型的 Client 加入 RTC 房间。根据类型设置不同的角色（`publisher_only` 或 `subscriber`）。

### 公共监控方法

这些方法用于启动对应类型的监控流。

- **`openCameraMonitor(isVirtualTest: boolean = false)`**:
    - 启动 PC 摄像头监控。
    - 获取 `camera` Client 并加入房间。
    - 创建本地音视频流 (`createStream`)。
    - 设置视频分辨率、帧率、码率 (`setVideoProfile`)。
    - 初始化流 (`initialize`)，处理设备权限和错误（如 `NotFoundError`, `NotAllowedError` 等）。
    - 进行虚拟摄像头检测和上报 (`sendVirtualErrorAction`)。
    - 发布流 (`publish`)。
    - 监听流播放状态 (`PLAYER_STATE_CHANGED`) 和连接状态 (`CONNECTION_STATE_CHANGED`)，并更新 store 状态和上报错误 (`sendErrorAction`)。
    - 包含详细的埋点。
- **`openPhoneMonitor(isVirtualTest: boolean = false)`**:
    - 启动手机摄像头监控（拉流）。
    - 获取 `phone` Client（作为 `subscriber`）并加入房间。
    - 订阅指定 userId (`phone_${userId}`) 的远端流。
    - 监听远端流的添加 (`STREAM_ADDED`)、移除 (`STREAM_REMOVED`)、更新 (`STREAM_UPDATED`) 和订阅成功 (`STREAM_SUBSCRIBED`) 事件。
    - 根据远端流状态（是否有音视频）更新 store 状态。
    - 包含详细的埋点。
- **`openScreenMonitor(isVirtualTest: boolean = false)`**:
    - 启动 PC 屏幕共享监控。
    - 获取 `screen` Client 并加入房间。
    - 创建本地屏幕共享流 (`createStream`)。
    - 设置屏幕共享分辨率、帧率、码率 (`setScreenProfile`)。
    - 初始化流 (`initialize`)，处理屏幕共享权限和错误（如 `Permission denied by system`, `NotReadableError` 等）。
    - 发布流 (`publish`)。
    - 监听屏幕共享停止事件 (`screen-sharing-stopped`) 和连接状态 (`CONNECTION_STATE_CHANGED`)，并更新 store 状态和上报错误 (`sendErrorAction`)。
    - 包含详细的埋点。

## 依赖

- **`@/store/use-monitor-store`**: Pinia store，用于存储和更新监控状态。
- **`@/services/http`**: HTTP 请求服务，用于调用 `_getSign` API。
- **`NEBULARTC` SDK**: 全局引入的 WebRTC SDK。
- **`BossAnalyticsTrack`**: 全局埋点函数。
- **`@/shared/url`**: 包含 RTC URL 等配置。
- **`logger`**: 日志记录器。
- **`sendErrorAction`, `sendInitializeErrorAction`, `sendVirtualErrorAction`**: 错误状态上报相关函数（具体实现在别处）。

## 注意事项

- 该类强依赖于 `useMonitorStore` 提供的考试配置 (`examConfig`) 和基础信息 (`examBaseInfo`)。
- `NEBULARTC` SDK 实例是单例模式。
- 每个监控类型 (`camera`, `phone`, `screen`) 使用独立的 Client 实例，并根据需要指定不同的 userId 前缀。
- 错误处理逻辑分散在各个方法的 `try...catch` 块以及事件监听器中，主要通过 `sendErrorAction` 等函数上报状态码，并通过 `BossAnalyticsTrack` 上报埋点。
- 虚拟设备检测仅上报状态，不阻断流程。
- `isVirtualTest` 参数用于某些场景下跳过实际的发布流操作。
