<template>
    <div class="text-progress-container">
        <span class="current">01</span>
        <span>/</span>
        <span class="total">15</span>
    </div>
</template>

<script setup lang="ts">
defineOptions({
    name: 'TextProgress',
});
defineEmits<{
    'update:modelValue': [value: boolean];
}>();
</script>

<style lang="less" scoped>
.text-progress-container {
    display: block;
}
</style>
