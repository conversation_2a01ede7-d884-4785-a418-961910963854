<template>
    <div class="exam-info">
        <div class="name">
            {{ mode === 'PREPARE_MODE' ? '考前设备检测' : examBaseInfo.examInfo?.examName }}
        </div>
        <div class="time">
            <template v-if="mode === 'PREPARE_MODE'"> 时间：{{ debugInfo.startTime }} - {{ debugInfo.endTime }} </template>
            <template v-else> 考试时间：{{ examBaseInfo.examInfo?.examStartTime }} - {{ examBaseInfo.examInfo?.examEndTime }} </template>
        </div>
        <span v-if="hasHelp" class="op-action" @click.prevent="getHelp"><SvgIcon name="question" size="16" />帮助</span>
    </div>
</template>

<script lang="ts" setup>
import { useMonitorStore } from '@/store/use-monitor-store';
import { HELP_PHONE } from '@crm/exam-constants';
import { computed, createVNode } from 'vue';

defineProps({
    hasHelp: {
        type: Boolean,
        default: false,
    },
});
const monitorStore = useMonitorStore();
const { examBaseInfo, debugInfo } = monitorStore;
const mode = computed(() => monitorStore.ExamMode); // PREPARE_MODE : EXAM_MODE

function getHelp() {
    const helpText = `请先尝试使用摄像头自动核验身份，人脸审核多次不通过会自动进入人工审核，如需更多帮助请联系${HELP_PHONE}`;
    Dialog.open({
        title: '更多帮助',
        type: 'info',
        width: 460,
        footer: false,
        content: () => createVNode('div', { style: { marginBottom: '30px' } }, helpText),
    });
}
</script>
