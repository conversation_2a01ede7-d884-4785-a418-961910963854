<template>
    <div v-if="imgList && imgList.length > 0" class="question-image-wrap">
        <template v-for="item in imgList" :key="item.encryptId">
            <img
                :src="item.previewUrl || item.uri"
                class="image-content"
                :style="{
                    maxWidth: `100%`,
                    width: `${item.width}px`,
                }"
                @click="
                    (event) => {
                        event.stopPropagation();
                        previewImageInject?.open(item.previewUrl || item.uri);
                    }
                "
            />
        </template>
    </div>
</template>

<script setup lang="ts">
import { inject } from 'vue';
import { EVALUATION_PREVIEW_IMAGE_KEY } from '../../../constant';
const previewImageInject = inject<{ open: (uri: string) => void }>(EVALUATION_PREVIEW_IMAGE_KEY);
withDefaults(
    defineProps<{
        imgList?: any[]; // 根据实际内容替换 any 类型
        maxHeight?: number;
        maxSizeType?: string; // 如果是特定值可以用联合类型，如 'title' | 'other'
    }>(),
    {
        imgList: () => [], // 数组默认值需要用工厂函数返回
        maxHeight: 400,
        maxSizeType: 'title',
    },
);
</script>

<style lang="less" scoped>
.question-image-wrap {
    width: 100%;

    .image-content {
        margin-right: 18px;
        margin-bottom: 18px;
        border-radius: 8px;
        overflow: hidden;

        background: radial-gradient(76% 46% at 50% 54%, #edeef1 0%, #f7f8f9 100%);

        &:last-child {
            // margin-bottom: 0;
        }
    }
}
</style>
