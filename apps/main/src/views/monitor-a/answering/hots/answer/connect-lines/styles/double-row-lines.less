.common-block {
    max-width: 100px;
    width: 80px;
    line-height: 38px;
    max-height: 40px;
    color: #1F1F1F;
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    text-align: center;
    background: linear-gradient(284.28708deg, #FBFCFD 17%, rgba(251, 252, 253, 0.00) 94%);
    box-shadow: inset -3px -3px 4px 0px rgba(6, 8, 9, 0.20), inset 2px 2px 3px 0px rgba(255, 255, 255, 0.90), 4px 4px 6px 0px rgba(0, 0, 0, 0.04);
    border: 1px solid #ECECEC;
    border-radius: 6px;
    margin-bottom: 16px;
    cursor: pointer;
    flex: 1;
    position: relative;
    .icon {
        right: -6px;
        top: 14px;
        position: absolute;
        display: none;
    }
    &:hover {
        .common-actived();
    }
}
.common-actived {
    background: #CAEBEB;
    box-shadow: inset 2px 3px 3px 0 rgba(0, 107, 107, 0.20), inset -2px -2px 3px 0px rgba(255, 255, 255, 0.50), 2px 2px 3px 0px rgba(115, 206, 207, 0.22);
    border: 1px solid var(--primary-color-6);
    border-radius: 8px;
    .icon {
        display: inline-block;
    }
}
.component-wrap {
    padding: 20px 73px 0;
    .connect-lines-wrap {
        width: 100%;
        display: flex;
        flex-direction: column;
        .container {
            width: 100%;
            flex: 1;
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .left {
                display: flex;
                flex-direction: column;
                float: left;
                .block {
                    .common-block();
                }
                :last-child {
                    margin-bottom: 0;
                }
                .block-actived {
                    .common-actived();
                }
            }
            .right {
                float: right;
                display: flex;
                flex-direction: column;
                .block {
                    .common-block();
                }
                :last-child {
                    margin-bottom: 0;
                }
            }
            .svg-box {
                width: 100%;
                height: 100%;
                position: absolute;
                left: 0;
                top: 0;
                pointer-events: none;
                g {
                    cursor: pointer;
                    line {
                        pointer-events: all;
                    }
                    &:hover {
                        .svg-line {
                            stroke-width: 2;
                            position: relative;
                        }
                    }
                }
            }
        }
    }
    .legend {
        margin-top: 32px;
        display: flex;
        justify-content: center;
        gap: 24px;
        .legend-item {
            color: #808080;
            font-family: PingFang SC;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            display: flex;
            align-items: center;
            gap: 6px;
            span {
                width: 12px;
                height: 2px;
                border-radius: 2px;
                display: inline-block;
            }
        }
    }
}
