<template>
    <div class="guidanc-wrap">
        <div class="title-wrap">
            <p class="title">{{ numberToChinese(gbaStore.currentIndex + 1, false) }}、{{ gbaStore.currentQuestion.showName }}</p>
            <p class="time">
                剩余阅读时长：<span>{{ guidancCountdown.remainingTime.minutes }}:{{ guidancCountdown.remainingTime.seconds }}</span>
            </p>
        </div>
        <div class="content-wrap">
            <RichText :html="gbaStore.currentQuestion.answerDesc" />
            <div class="options-btn-wrap">
                <b-button v-if="simulateNumber < 3 && gbaStore.currentQuestion.simulateList.length" type="outline" shape="round" @click="simulate"> 模拟体验 </b-button>
                <b-button type="primary" shape="round" @click="publicOver"> 阅读完毕，开始答题 </b-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import RichText from '@/components/rich-text/index.vue';
import { createTimer } from '@/store/time';
import { Storage } from '@/utils/storage';
import { computed, nextTick, onMounted, watch } from 'vue';
import { useGbaStore } from '../store';

import { numberToChinese } from '@/utils/number-to-chinese';
import { imgPreload } from '@/utils/index';

defineProps({
    simulateStatus: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(['over', 'simulate']);
// 当前题型模拟次数 本地Storage key
const GBA_QUESTION_SIMULATE_KEY = 'GBA_QUESTION_SIMULATE_KEY';
const gbaStore = useGbaStore();
let guidancCountdown = createTimer();
const simulateNumber = computed(() => {
    let number = 0;
    const data = Storage.get(GBA_QUESTION_SIMULATE_KEY);
    if (data && data[gbaStore.currentQuestion.id]) {
        number = data[gbaStore.currentQuestion.id];
    }
    return number;
});

function publicOver() {
    emit('over', {});
}

function simulate() {
    guidancCountdown.stop();
    setSimulateNumber();
    nextTick(() => {
        emit('simulate', {});
    });
}

function setSimulateNumber() {
    Storage.set(GBA_QUESTION_SIMULATE_KEY, {
        [gbaStore.currentQuestion.id]: simulateNumber.value + 1,
    });
}

onMounted(() => {
    // 图片预加载
    const assetsCongigImg = gbaStore.currentQuestion.assetsCongig.map((item) => item.paramImg);
    const imgList = [
        ...assetsCongigImg,
        'https://img.bosszhipin.com/static/file/2024/h6xy4k3ehq1725606964354.png.webp',
        'https://img.bosszhipin.com/static/file/2024/mn0equr2ru1725349213782.png.webp',
        'https://img.bosszhipin.com/static/file/2024/mgdbnmslt81725873396289.png.webp',
        'https://img.bosszhipin.com/static/file/2024/34hjehwz2k1726036366579.png.webp',
        'https://img.bosszhipin.com/static/file/2024/g8xr90q1k81726036367212.png.webp',
        'https://img.bosszhipin.com/static/file/2024/kvfc7nvx351726036366218.png.webp',
        'https://img.bosszhipin.com/static/file/2024/8ommig95771725354231646.png.webp',
        'https://img.bosszhipin.com/static/file/2024/2gwkqypex21725348290417.png.webp',
        'https://img.bosszhipin.com/static/zhipin/kanjian/282593614637164814.svg',
        'https://img.bosszhipin.com/static/zhipin/kanjian/20359361463715097.svg',
        'https://img.bosszhipin.com/static/zhipin/kanjian/982593614637123394.svg',
        'https://img.bosszhipin.com/static/zhipin/kanjian/562593614637193044.svg',
        'https://img.bosszhipin.com/static/zhipin/313500031537167438.svg',
        'https://img.bosszhipin.com/static/zhipin/523500031537147862.svg',
        'https://img.bosszhipin.com/static/file/2024/xf9kqg1xkr1735121449814.png.webp',
        'https://img.bosszhipin.com/static/file/2024/scios4zmg21735121450086.png.webp',
        'https://img.bosszhipin.com/static/file/2024/vo55glfmdt1735121449429.png.webp',
    ];

    imgPreload(imgList);
});

watch(
    () => gbaStore.currentQuestion.answerDescTime,
    (val) => {
        if (val) {
            guidancCountdown.start({
                key: 'guidancCountdown',
                finishTime: (t) => t + gbaStore.currentQuestion.answerDescTime,
                onFinished: () => {
                    publicOver();
                },
            });
        }
    },
    {
        deep: true,
        immediate: true,
    },
);
</script>

<style lang="less" scoped>
.guidanc-wrap {
    height: 100%;
    position: relative;
    margin: 0 auto;
    margin-bottom: 7px;
    width: 1068px;
    background: linear-gradient(160.13423deg, #f4f7f9 4%, #ffffff 48%);
    box-shadow:
        4px 4px 10px 0 rgba(34, 41, 43, 0.1),
        -5px -4px 14px 0 #ffffff;
    border-radius: 7px;
    display: flex;
    flex-direction: column;
    // overflow: auto;

    .title-wrap {
        padding: 28px 32px 0;
        position: sticky;
        top: 0px;
        // margin-bottom: 29px;
        display: flex;
        justify-content: space-between;

        .title {
            color: #1f1f1f;
            font-size: 18px;
            font-weight: 500;
            line-height: 18px;
        }

        .time {
            display: flex;
            align-items: center;
            color: #808080;
            font-size: 13px;
            font-weight: 400;

            span {
                margin-top: -1px;
                width: 46px;
                color: #00a6a7;
                font-size: 16px;
            }
        }
    }

    .content-wrap {
        flex: 1;
        padding: 0 32px 32px;
        overflow: auto;

        /* 滚动条轨道 */
        &::-webkit-scrollbar-track {
            background: transparent !important; /* 轨道背景色 */
        }
    }

    .options-btn-wrap {
        margin-top: 40px;
        display: flex;
        justify-content: center;

        .b-button {
            margin-right: 12px;

            &.b-button-outline {
                border-color: var(--primary-color);
                color: var(--primary-color);
            }

            &:last-child {
                margin-right: 0;
            }
        }
    }
}
</style>
