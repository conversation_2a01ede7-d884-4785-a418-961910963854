{"extends": "../../tsconfig.base.json", "compilerOptions": {"composite": true, "baseUrl": "./", "types": ["@boss/apm-sdk-types"], "paths": {"@/*": ["src/*"]}}, "include": ["auto-imports.d.ts", "components.d.ts", "vite.config.ts", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "../../packages/hooks/src/time/use-time-center-timer.ts", "../../packages/hooks/src/time/use-time-center.ts", "../../packages/hooks/src/time/types.ts"], "exclude": ["node_modules", "dist", "public"]}