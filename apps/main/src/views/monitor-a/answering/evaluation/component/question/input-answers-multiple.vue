<template>
    <QuestionTitle :questionInfo="questionInfo" />
    <div class="single-choice-wrap">
        <div class="single-choice-item" v-for="(item, index) of questionInfo.questionOptionList" :key="item.encryptId" @click="changeAnswer(item, index + 1)">
            <OptionItem :imgList="item.files" :content="`${index + 1}.${item.optionContent}`" :isChecked="!!findCheckedList(item.encryptId)" />
        </div>
    </div>
    <template v-for="item of checked" :key="item.optionId">
        <div class="input-wrap">
            <p class="input-title">选择“{{ item.serialNumber }}.{{ item.shortQuestion }}”的原因及改进建议</p>
            <b-textarea v-model="item.shortAnswer" :placeholder="placeholder" autosize :showWordLimit="true" :maxLength="300" />
        </div>
    </template>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import OptionItem from './component/option-item.vue';
import QuestionTitle from './component/question-title.vue';

const props = defineProps({
    questionInfo: {
        type: Object,
        default: () => ({}),
    },
    answer: {
        type: [String],
        default: () => '',
    },
});

const placeholder = ref('');
const checked = ref<any[]>([]);

watch(
    () => props.questionInfo,
    async (val: any) => {
        const { required } = val;
        let checkedVal: any = [];

        if (props.answer) {
            checkedVal = JSON.parse(props.answer);
        }

        placeholder.value = required ? '请输入' : '选填';
        checked.value = checkedVal;
    },
    { immediate: true },
);

function findCheckedList(id: string) {
    let flag = 0;
    const list = [...(checked.value || [])];
    const falgIndex = list.findIndex((item: { optionId: string }) => item?.optionId === id);
    if (falgIndex > -1) {
        flag = falgIndex + 1;
    }
    return flag;
}

async function changeAnswer(data: any, serialNumber: number) {
    const { encryptId, shortAnswer, shortQuestion } = data || {};

    const flagIndex = checked.value.findIndex((item: any) => item?.optionId === encryptId);

    if (flagIndex > -1) {
        checked.value.splice(flagIndex, 1);
    } else {
        checked.value.push({
            optionId: encryptId,
            shortAnswer,
            shortQuestion,
            serialNumber,
        });
    }
}

function checkAnswer() {
    const { required, encryptId, questionType } = props.questionInfo;
    let flag = true;
    let message = '';

    const val = [...checked.value];

    if (val.length <= 0) {
        flag = false;
        message = '请选择您的答案';
    } else if (required) {
        const hasEmpty = val.find((item: { shortAnswer: any }) => !item.shortAnswer);
        if (hasEmpty) {
            flag = false;
            message = '请输入您的回答';
        }
    }

    const params = {
        evaPaperSnapshotId: encryptId,
        questionType,
        answerContent: JSON.stringify(val),
    };

    return [flag, message, params];
}

defineExpose({
    check: checkAnswer,
});
</script>

<style lang="less" scoped>
.input-wrap {
    margin-top: 24px;

    .input-title {
        margin-bottom: 8px;
        color: #292929;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
    }

    :deep(.b-textarea-wrapper) {
        .b-textarea {
            height: 64px;
        }

        .b-textarea-word-limit {
            .b-typing {
                color: #b3b3b3;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px;
                text-align: right;
                &.b-typing-secondary {
                    color: #00a6a7;
                }
            }
        }
    }
}

.single-choice-wrap {
    .single-choice-item {
        margin-bottom: 12px;

        &:last-child {
            margin-bottom: 20px;
        }
    }
}
</style>
