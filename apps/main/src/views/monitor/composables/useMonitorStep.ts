import { useMonitorStore } from '@/store/use-monitor-store';
import { RequestStatus } from '@/hooks/useGlobalInit'; // Assuming RequestStatus is here
import type { Ref } from 'vue';
import { useRouteParamId } from './useRouteParamId';

export function useMonitorStep() {
    const monitorStore = useMonitorStore();
    const { encryptExamId, seqId } = useRouteParamId();

    async function initCurrentStep(examInfoRequestStatusRef: Ref<RequestStatus>) {
        try {
            const idToQuery = encryptExamId || seqId;
            if (!idToQuery) {
                logger.error('No ID provided for getStageQuery');
                examInfoRequestStatusRef.value = RequestStatus.FAILURE; // Or handle appropriately
                return;
            }
            const { code, data } = await Invoke.exam.getStageQuery({ encryptExamId: idToQuery });
            if (code === 0 && data) {
                let __step = 0;
                if (encryptExamId) {
                    __step = data.stage === -4 ? 0 : data.stage || 0;
                } else {
                    __step = data.stage || 0;
                }
                monitorStore.step = __step;
                monitorStore.hasInitStep = true; // header判断monitor-status 使用该状态
                examInfoRequestStatusRef.value = RequestStatus.SUCCESS;
            } else {
                // Handle API error or no data case
                examInfoRequestStatusRef.value = RequestStatus.FAILURE;
            }
        } catch (error) {
            logger.error('Error in initCurrentStep:', error);
            examInfoRequestStatusRef.value = RequestStatus.FAILURE;
        }
    }

    async function clearStep() {
        monitorStore.step = 0;
        monitorStore.hasInitStep = false;
    }

    const hasInitStep = computed(() => monitorStore.hasInitStep);

    const step = computed(() => monitorStore.step);

    return {
        step,
        initCurrentStep,
        hasInitStep,
        clearStep,
    };
}
