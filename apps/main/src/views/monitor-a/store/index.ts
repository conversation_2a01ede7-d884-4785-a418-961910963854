import type { BaseInfoFormData, ExamConfig, CameraMonitorRule, MonitorStatus, UpdatePhoneCameraParams } from './types';
import { cleanupLocalMonitor, cleanupPhone } from './utils';
import { NEBULA } from '@/types/nebulartc';
import NebulaMatrix from '@nebula/bodydetect-web';

export const useDebuggingStore = defineStore('useDebuggingStore', () => {
    const hasInitStep = ref(false);
    const step = ref(0);

    const hasComputer = computed(() => examConfig.value.computerCameraMonitor); // 第一视角
    const hasPhone = computed(() => examConfig.value.mobilePerspectiveMonitor); // 第二视角
    const hasScreen = computed(() => examConfig.value.computerScreenMonitor); // 第三视角
    const baseInfoFormData = ref<BaseInfoFormData>({}); // 使用导入的类型 人脸认证相关
    // 考生是否处于答题阶段

    const showMonitorStatus = computed(() => hasInitStep.value && step.value !== -4);

    const examConfig = ref<ExamConfig>({
        canDebug: true,
        recognizeFaceConfirm: true,
        personalVerification: true,
        recognizeFaceFailedCount: 0, // 人脸识别失败次数
        systemRecognizeResult: 1, // 系统人脸识别结果：0-未进行；1-成功;2-失败；3-审核中
        recognizeFile: {
            name: '',
            type: '',
            size: 0, // 文件大小 单位字节
            url: '', // 文件url
            uri: '', // 文件相对路径
        },
        computerCameraMonitor: false,
        mobilePerspectiveMonitor: false,
        computerScreenMonitor: false,
        examineeInfoCanModify: false,
        examineeInfoList: [],
        openCommunicateInlet: true,
        monitorRoomId: '',
        cameraMonitoringRule: {} as CameraMonitorRule,
        switchScreenRule: {
            forceSubmitWhenLevePage: 1,
            maxSwitchCount: 2,
            switchScreenWarning: 1,
            fullScreen: 1,
            warningConfirm: false,
        },
        reLoginRule: {
            maxLoginCount: 1,
            forceSubmitWhenOverLimit: 1,
            reLoginWarning: 1,
            currentReLoginCount: 0,
            warningConfirm: true,
        },
    });

    // 管理三个视角监控状态的集合
    const STATUS = ref<MonitorStatus>({
        // 使用导入的类型
        camera: {
            client: null,
            status: 0,
            videoStatus: 0,
            audioStatus: 0,
            errorText: '',
            videoErrorText: '',
            audioErrorText: '',
            localStream: null,
        },
        phone: {
            client: null,
            status: 0,
            errorText: '',
            remoteStream: null,
            localStream: null,
        },
        screen: {
            client: null,
            status: 0,
            errorText: '',
            localStream: null,
            videoTrack: null,
        },
    });

    // 添加摄像头细化状态的计算属性，方便UI使用
    const cameraStatus = computed(() => ({
        overall: STATUS.value.camera.status,
        video: STATUS.value.camera.videoStatus,
        audio: STATUS.value.camera.audioStatus,
        videoError: STATUS.value.camera.videoErrorText,
        audioError: STATUS.value.camera.audioErrorText,
        overallError: STATUS.value.camera.errorText,
        // 判断是否只是音频问题
        isOnlyAudioIssue: STATUS.value.camera.videoStatus === 2 && STATUS.value.camera.audioStatus === 1,
        // 判断是否只是视频问题
        isOnlyVideoIssue: STATUS.value.camera.videoStatus === 1 && STATUS.value.camera.audioStatus === 2,
        // 判断音视频是否都正常
        isFullyWorking: STATUS.value.camera.videoStatus === 2 && STATUS.value.camera.audioStatus === 2,
    }));

    const updateStatus = (
        type: keyof MonitorStatus, // Use keyof for type safety
        params: {
            status?: number;
            videoStatus?: number;
            audioStatus?: number;
            errorText?: string;
            videoErrorText?: string;
            audioErrorText?: string;
            client?: NEBULA.RtcClient | null; // 使用导入的类型
            localStream?: NEBULA.RtcStream | null; // 使用导入的类型
            remoteStream?: NEBULA.RtcStream | null; // 使用导入的类型
        },
    ) => {
        const target = STATUS.value[type];

        if (params.status !== undefined) {
            target.status = params.status;
        }

        // 处理摄像头的细化状态
        if (type === 'camera') {
            const cameraTarget = target as typeof STATUS.value.camera;

            if (params.videoStatus !== undefined) {
                cameraTarget.videoStatus = params.videoStatus;
            }
            if (params.audioStatus !== undefined) {
                cameraTarget.audioStatus = params.audioStatus;
            }
            if (params.videoErrorText !== undefined) {
                cameraTarget.videoErrorText = params.videoErrorText;
            }
            if (params.audioErrorText !== undefined) {
                cameraTarget.audioErrorText = params.audioErrorText;
            }

            // 根据音视频状态计算整体状态（如果没有显式设置status）
            if (params.status === undefined && (params.videoStatus !== undefined || params.audioStatus !== undefined)) {
                const videoStatus = params.videoStatus !== undefined ? params.videoStatus : cameraTarget.videoStatus;
                const audioStatus = params.audioStatus !== undefined ? params.audioStatus : cameraTarget.audioStatus;

                // 只要视频正常，整体就认为是正常的（因为视频是主要功能）
                if (videoStatus === 2) {
                    cameraTarget.status = 2;
                } else if (videoStatus === 1 || audioStatus === 1) {
                    // 如果视频失败或音频失败，但不影响视频的情况下，状态可以是部分成功
                    cameraTarget.status = videoStatus === 1 ? 1 : 2;
                } else {
                    cameraTarget.status = 0;
                }
            }

            // 组合错误信息
            if (params.videoErrorText !== undefined || params.audioErrorText !== undefined) {
                const videoError = params.videoErrorText !== undefined ? params.videoErrorText : cameraTarget.videoErrorText;
                const audioError = params.audioErrorText !== undefined ? params.audioErrorText : cameraTarget.audioErrorText;

                const errors = [];
                if (videoError) errors.push(`视频: ${videoError}`);
                if (audioError) errors.push(`音频: ${audioError}`);

                cameraTarget.errorText = errors.length > 0 ? errors.join('; ') : '';
            }
        }

        if (params.errorText !== undefined && 'errorText' in target) {
            target.errorText = params.errorText;
        }
        if (params.client !== undefined) {
            target.client = params.client;
        }
        if (params.localStream !== undefined && 'localStream' in target) {
            target.localStream = params.localStream;
        }
        if (params.remoteStream !== undefined && 'remoteStream' in target) {
            // Type assertion needed because target could be CameraStatus or ScreenStatus
            (target as typeof STATUS.value.phone).remoteStream = params.remoteStream;
        }
    };

    const openCameraMonitor = async (isVirtualTest: boolean = false) => {
        if (STATUS.value.camera.status === 2) return;

        const { Media } = await import('./media');
        const media = new Media();
        await media.openCameraMonitor(isVirtualTest);
    };

    const openPhoneMonitor = async (isVirtualTest: boolean = false) => {
        if (STATUS.value.phone.status === 2) return;

        const { Media } = await import('./media');
        const media = new Media();
        await media.openPhoneMonitor(isVirtualTest);
    };

    const openScreenMonitor = async (isVirtualTest: boolean = false) => {
        if (STATUS.value.screen.status === 2) return;

        const { Media } = await import('./media');
        const media = new Media();
        await media.openScreenMonitor(isVirtualTest);
    };

    const updatePhoneCamera = (params: UpdatePhoneCameraParams) => {
        // 使用导入的类型
        Invoke.exam.postPhoneCameraUpdate(params);
    };

    /**
     * 关闭所有 RTC 连接和流
     * Safely attempts to close all active RTC connections (phone, screen, camera),
     * handling errors for each type independently using imported utils.
     * 使用 useTrackableFlow 包装，以便追踪流程执行过程
     */
    async function closeRtcAll(encryptExamId: string) {
        try {
            const result = await cleanupPhone(STATUS.value.phone, hasPhone.value);
            if (result) {
                // 通知第二视角，进行关闭
                updatePhoneCamera({
                    encryptExamId: encryptExamId,
                    status: 0, // 0 means close
                });
            }
        } catch (error) {}

        try {
            await cleanupLocalMonitor('Screen', STATUS.value.screen, hasScreen.value);
        } catch (error) {}

        try {
            await cleanupLocalMonitor(
                'Camera',
                STATUS.value.camera,
                hasComputer.value, // Assuming hasComputer corresponds to camera enabled
            );
        } catch (error) {}
    }

    const setBaseInfoFormData = (data: BaseInfoFormData) => {
        baseInfoFormData.value = data;
    };

    const initExamConfig = (data: ExamConfig) => {
        examConfig.value = data;
    };

    const fetchConfig = async (query: { seqId: string; examId: string }) => {
        const res = await Invoke.common.getConfig(query);

        if (res.code === 0 && res.data) {
            initExamConfig(res.data);
        }

        return res;
    };

    // 同意人脸验证服务确认弹窗
    const recognizeFaceConfirm = () => {
        examConfig.value.recognizeFaceConfirm = true;
    };

    // 获取考试步骤
    const fetchStep = async (query: { seqId: string; examId: string }) => {
        const idToQuery = query.examId || query.seqId;
        const res = await Invoke.exam.getStageQuery({ encryptExamId: idToQuery });

        if (res.code === 0 && res.data) {
            const resStep = res.data.stage || 0;
            let _step = 0;
            if (query.examId) {
                _step = resStep === -4 ? 0 : resStep;
            } else {
                _step = resStep || 0;
            }

            step.value = _step;

            hasInitStep.value = true;
        }
        return res;
    };

    // 活体检测相关逻辑
    const antiCheatStatus = ref({
        needAntiCheat: false,
        isNebulaInitializing: false,
        nebulaLoaded: false,
    });
    const initBodyDetect = async () => {
        const cameraMonitoringRule = examConfig.value.cameraMonitoringRule; // 防作弊配置集
        // 是否配置防作弊
        const needAntiCheat =
            examConfig.value.computerCameraMonitor &&
            cameraMonitoringRule &&
            (cameraMonitoringRule.substituteExam ||
                cameraMonitoringRule.multipleFaces ||
                cameraMonitoringRule.leaveSeat ||
                cameraMonitoringRule.lowerHead ||
                cameraMonitoringRule.lookAround);
        antiCheatStatus.value.needAntiCheat = !!needAntiCheat; // 更新store中的状态

        // 是否配置人脸验证
        const needFaceVerify = examConfig.value.personalVerification;

        // 判断是否初始化活体检测sdk
        if (!antiCheatStatus.value.nebulaLoaded && (needFaceVerify || needAntiCheat)) {
            if (antiCheatStatus.value.isNebulaInitializing) {
                return; // 如果已经在初始化，则提前返回
            }
            antiCheatStatus.value.isNebulaInitializing = true;

            try {
                NebulaMatrix.init({
                    cdnUrl: 'https://img.bosszhipin.com/static/file/2024/nebula-plugins-matrix.2.0.4.data', // 使用cdn版本
                });
                await NebulaMatrix.onRuntimeInitialized(); // 等待 runtime 初始化完成
                antiCheatStatus.value.nebulaLoaded = true;
            } finally {
                antiCheatStatus.value.isNebulaInitializing = false; // 无论成功或失败，都重置标志
            }
        }
    };

    // 定义 store 中的状态变量映射
    const storeState = {
        step,
        hasInitStep,
        baseInfoFormData,
        examConfig,
        STATUS,
        antiCheatStatus,
    } as const;

    const updateData = (key: keyof typeof storeState, data: any) => {
        if (key in storeState) {
            storeState[key].value = data;
        }
    };

    const resetData = () => {
        step.value = 0;
        hasInitStep.value = false;
    };

    return {
        showMonitorStatus,
        antiCheatStatus,
        hasComputer,
        hasPhone,
        hasScreen,
        examConfig,
        fetchConfig,
        fetchStep,
        hasInitStep,
        step,
        baseInfoFormData,
        recognizeFaceConfirm,
        setBaseInfoFormData,
        updateStatus,
        openCameraMonitor,
        openPhoneMonitor,
        openScreenMonitor,
        closeRtcAll,
        initBodyDetect,
        cameraStatus,
        STATUS,
        updatePhoneCamera,
        resetData,
        updateData,
    };
});
