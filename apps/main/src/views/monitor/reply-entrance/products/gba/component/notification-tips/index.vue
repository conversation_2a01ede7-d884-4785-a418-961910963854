<template>
    <div v-if="visible" class="notification-tips-wrap">
        <div class="content-inner">
            <div v-if="notificationInfo.type !== 'tips-message'" class="tips-bg">
                <div v-if="notificationInfo.type === 'countdown-start-message'" class="text">
                    <p>倒计时</p>
                    <p>{{ Math.floor(notificationTips?.remainingTime.total / 1000) }}</p>
                </div>
                <div v-else-if="notificationInfo.type === 'template-message' && notificationInfo.action === 'simulate-over'" class="text">
                    <p>模拟体验结束</p>
                    <div class="simulate-btn">
                        <b-button shape="round" type="outline" @click="close"> 关闭{{ Math.floor(notificationTips?.remainingTime.total / 1000) }}s </b-button>
                    </div>
                </div>
                <template v-else-if="notificationInfo.type === 'template-message'">
                    <div v-if="notificationInfo.action === 'delivery-stage-end-view'" class="delivery-stage-end-view">
                        <div class="gold-info">
                            <span class="gold-icon" />
                            <span>+{{ notificationInfo.number || 0 }}</span>
                        </div>
                        <div class="text-info">
                            {{ notificationInfo.text || 0 }}
                        </div>
                        <div class="countdown-info">
                            <span class="text" style="font-size: 32px !important">{{ Math.floor(notificationTips?.remainingTime.total / 1000) }}s</span>
                            <span class="text" style="transform: translateY(3px)">自动开始</span>
                        </div>
                    </div>
                    <div v-else :class="notificationInfo.action" class="end-type">
                        <p class="tit">恭喜完成本轮游戏</p>
                        <p v-if="notificationInfo.action === 'allocation-end'" class="desc">共获得金币</p>
                        <p v-else class="desc">
                            获得金币
                            <span>{{ notificationInfo.number || 0 }}</span>
                        </p>
                        <div class="gold-wrap">
                            <div class="mine-animation-gold" />

                            <Text
                                v-if="notificationInfo.action === 'allocation-end'"
                                :font-weight="600"
                                :shadowFilterConfig="{ x: 0, y: 0.87, blur: 0.87, color: 'rgba(218, 79, 12, 0.70)' }"
                                :stroke-width="1"
                                :text="`¥${notificationInfo.number}`"
                                font-size="36px"
                                strokeColor="#FDF1BD"
                                textColor="#894D00"
                            />
                        </div>
                        <div class="btn-wrpa">
                            <Button :theme="notificationInfo.theme" isActiveEffect @click="onGameFinished">
                                {{ gbaStore.currentIndex === gbaStore.questionList.length - 1 ? '我知道了' : '进入下一游戏' }}
                            </Button>
                        </div>
                    </div>
                </template>
            </div>
            <!-- 轻提示 -->
            <div v-else class="prompt-tips">
                <div class="text">
                    <p>{{ notificationInfo.text }}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { createTimer } from '@/store/time';

import { watch } from 'vue';
import { useGbaStore } from '../../store';
import Button from '../button.vue';
import Text from '../text.vue';
import { useNotification } from './index';

const gbaStore = useGbaStore();
const { visible, notificationInfo, close } = useNotification();

const infoData = {
    type: 'message', // message 默认1秒关闭 需要传入文案
    time: 1 * 1000, // 倒计时时长
    action: '', // 自定义动作名称
    message: '', // 展示的消息内容
    number: 0, // 金币的金额
    success: () => {}, // 下一步动作
    isPrompt: false,
};

// 倒计时
const notificationTips = createTimer();
const timerStart = () => {
    notificationTips.start({
        key: 'NotificationTips',
        finishTime: (t) => t + notificationInfo.value.time,
        onFinished: () => {
            close();
        },
    });
};

function onGameFinished() {
    notificationInfo.value.onNext();
}

watch(
    () => [visible.value, notificationInfo.value],
    ([val, info]) => {
        if (notificationInfo.value.action === 'delivery-stage-end-view') {
            timerStart();
            return;
        }

        if (notificationInfo.value.action === 'simulate-over') {
            timerStart();
            return;
        }
        if (val && ['countdown-start-message', 'countdown-message', 'tips-message'].includes(notificationInfo.value.type)) {
            timerStart();
        }
    },
    {
        deep: true,
        immediate: true,
    },
);
</script>

<style lang="less" scoped>
.notification-tips-wrap {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.content-inner {
    position: relative;
    width: 100%;
    height: 100%;

    .tips-bg {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        z-index: 20;
        display: flex;
        flex-direction: column;
        justify-content: center;

        .text {
            text-align: center;
            color: #ffffff;
            font-size: 30px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            text-shadow: 0 0.87px 0.87px rgba(218, 79, 12, 0.7);
        }
    }

    .end-type {
        padding: 25px 0;
        position: relative;
        margin: 0 auto;
        width: 210px;
        height: 215px;
        border-radius: 22px;
        border: 5px solid #fff;
        text-align: center;

        &.allocation-end {
            width: 312px;
            height: 211px;
            background: linear-gradient(179.99998deg, #ffba38 0%, #ffffff 100%);

            .tit {
                font-size: 24px;
                font-weight: 600;
            }

            .desc {
                margin: 8px 0;
            }
        }

        &.area-judge-end {
            width: 312px;
            height: 211px;
            background: linear-gradient(179.99998deg, #94ccfb 0%, #d4f1ff 100%);

            .tit {
                color: #000;
                font-size: 24px;
                font-weight: 600;
            }

            .desc {
                color: #000;
                margin: 8px 0;
            }
        }
        &.direction-task-end {
            width: 312px;
            height: 211px;
            background: linear-gradient(179.99998deg, #fbb7b7 0%, #fff9f4 100%);

            .tit {
                color: #000;
                font-size: 24px;
                font-weight: 600;
            }

            .desc {
                color: #000;
                margin: 8px 0;
            }
        }

        &.mine-view {
            background: linear-gradient(179.99998deg, #ffba38 0%, #ffffff 100%);
        }

        &.fruit-view {
            background: linear-gradient(179.99998deg, #42cdf8 0%, #c6f6ff 100%);

            .tit {
                color: #014d65;
            }

            .desc {
                color: #014d65;
            }
        }

        .tit {
            color: #894d00;
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
        }

        .desc {
            margin: 5px 0 7px;
            color: #894d00;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 19.11px;
            display: flex;
            justify-content: center;

            span {
                margin-left: 5px;
            }
        }

        .gold-wrap {
            display: flex;
            justify-content: center;
            align-items: center;

            .mine-animation-gold {
                width: 108px;
                height: 93px;
                background: url(https://img.bosszhipin.com/static/file/2024/k0fq809j501726725553854.png.webp) no-repeat;
                background-size: 100% auto;
            }

            .number {
                color: #894d00;
                font-size: 36px;
                font-style: normal;
                font-weight: 600;
                line-height: normal;
            }
        }

        .btn-wrpa {
            position: absolute;
            top: 100%;
            left: 50%;
            width: 180px;
            transform: translateX(-50%) translateY(-50%);
        }
    }

    // 轻提示
    .prompt-tips {
        position: absolute;
        left: 50%;
        top: 50%;
        z-index: 20;
        transform: translateX(-50%) translateY(-50%);
        border-radius: 12px;
        padding: 20px 36px;
        background: rgba(0, 0, 0, 0.7);

        .text {
            color: #ffffff;
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: 22px;
        }
    }

    // 模拟体验弹窗相关样式
    .simulate-btn {
        margin-top: 24px;

        .b-button {
            width: 111px;
            height: 50px;
            color: #ffffff;
            font-size: 20px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            background: transparent;
            text-shadow: 0 0.87px 0.87px rgba(218, 79, 12, 0.7);
        }
    }

    // 快递阶段升级相关弹窗处理
    .delivery-stage-end-view {
        color: #fff;
        margin: 0 auto;
        text-align: center;

        .gold-info {
            margin-bottom: 26px;
            display: flex;
            justify-content: center;
            font-size: 24px;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
            color: rgba(255, 255, 255, 0.9);

            .gold-icon {
                width: 55px;
                height: 44px;
                background: url(https://img.bosszhipin.com/static/file/2024/k0fq809j501726725553854.png.webp) no-repeat;
                background-size: 100% auto;
            }
        }

        .text-info {
            text-shadow: 0 0.87px 0.87px rgba(218, 79, 12, 0.7);
            color: #ffffff;
            font-size: 26px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
        }

        .countdown-info {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #ffffff;
            font-style: normal;
            font-weight: 400;
            line-height: normal;

            .number {
                font-size: 32px !important;
            }

            .text {
                // padding-bottom: 7px;
                margin-left: 6px;
                font-size: 18px !important;
                font-weight: 500;
            }
        }
    }
}
</style>
