<template>
    <div class="setting-container">
        <div class="detection-panel" :class="testResult.className">
            <div class="pic" />
            <div class="result">
                <div class="result-title">
                    <div class="test-status">
                        {{ testResult.title }}
                    </div>
                    <span v-if="testErrorList.length && !isTesting" class="warning-statistics">{{ testErrorList.length }}项异常</span>
                </div>
                <p class="suggestion">
                    {{ testResult.suggestion }}
                </p>
            </div>
            <div class="btns">
                <b-button type="outline" status="primary" shape="round" size="large" :disabled="!testResult.canRetry" @click.prevent="retry">
                    {{ testResult.retryButtonText }}
                </b-button>
            </div>
        </div>
        <div class="check-list detection-list">
            <component
                :is="itm.component"
                v-for="(itm, idx) in realList"
                ref="refs"
                :key="idx"
                v-model:isTesting="itm.isTesting"
                v-model:isSuccess="itm.isSuccess"
                @showGuid="showGuid"
            />
        </div>
        <!-- 摄像头、麦克风检测异常的帮助信息弹窗 -->
        <DialogHelp v-model="isShowHelpDialog" />
    </div>
</template>

<script setup lang="ts">
import DialogHelp from '@/views/monitor/components/consist/dialog-help.vue';
import { useMonitorStore } from '@/store/use-monitor-store';
import { computed, reactive, ref, watch, markRaw } from 'vue';
import TestAudio from './components/TestAudio.vue'; // 3麦克风检测
import TestBrowser from './components/TestBrower.vue'; // 1浏览器检测
import TestNetwork from './components/TestNetwork.vue'; // 2网络检测
import TestVideo from './components/TestVideo.vue'; // 4摄像头检测

defineProps({
    stepStatus: {
        type: String,
        default: 'wait',
    },
});
const emit = defineEmits(['changeStepClickable', 'update:stepStatus']);

// 基于配置: 是否启用主摄像头监控 computerCameraMonitor, 启用则校验4项，否者校验前2项
const baseTestList = [
    {
        title: '浏览器',
        type: 'browser',
        component: markRaw(TestBrowser),
        isTesting: true,
        isSuccess: false,
    },
    {
        title: '网速',
        type: 'network',
        component: markRaw(TestNetwork),
        isTesting: true,
        isSuccess: false,
    },
    {
        title: '麦克风',
        type: 'audio',
        component: markRaw(TestAudio),
        isTesting: true,
        isSuccess: false,
    },
    {
        title: '摄像头',
        type: 'video',
        component: markRaw(TestVideo),
        isTesting: true,
        isSuccess: false,
    },
];

const monitorStore = useMonitorStore();
const realList = reactive(baseTestList.splice(0, monitorStore.hasComputer ? 4 : 2));

const isTesting = computed(() => {
    return realList.findIndex((itm) => itm.isTesting) > -1;
});

const testErrorList = computed(() => {
    return realList.filter((itm) => itm.isSuccess === false && itm.type !== 'browser');
});

// 忽略浏览器检测结果，浏览器检测通过与否不影响结果展示
const isBrowerTestSuccess = computed(() => {
    return realList.find((itm) => itm.type === 'browser')?.isSuccess;
});

// 修改step × or ✓
watch(
    () => testErrorList.value,
    (val) => {
        if (val.length === 0) {
            emit('update:stepStatus', 'finish');
        } else {
            emit('update:stepStatus', 'danger');
        }
    },
    {
        immediate: true,
    },
);

const testResult = computed(() => {
    if (isTesting.value) {
        return {
            className: 'default',
            title: '正在设备检测中….',
            suggestion: '为了保证在在线考试顺利进行，请按系统提示进行设备检测',
            canRetry: false,
            retryButtonText: '检测中…',
        };
    }
    if (testErrorList.value.length > 0) {
        return {
            className: 'warning',
            title: '设备检测',
            suggestion: '考试环境检测到异常，请调整设备后【重新检测】',
            canRetry: true,
            retryButtonText: '重新检测',
        };
    }
    return {
        className: 'success',
        title: '设备检测',
        suggestion: isBrowerTestSuccess.value ? '考试环境正常' : '建议使用最新版本的Chrome浏览器，发生兼容问题概率更小',
        canRetry: true,
        retryButtonText: '重新检测',
    };
});

const mode = computed(() => monitorStore.ExamMode); // PREPARE_MODE : EXAM_MODE
// 音频、视频、共享屏幕 满足即可下一步
const nextClickable = computed(() => {
    if (mode.value === 'EXAM_MODE') {
        // 正式考试模式下，麦克风、摄像头 检测通过，才可以进入下一步
        return testErrorList.value.filter((item) => item.type === 'audio' || item.type === 'video').length === 0;
    }
    // 考前准备模式下，默认都会显示下一步
    return true;
});

watch(
    () => nextClickable.value,
    () => {
        emit('changeStepClickable', nextClickable.value);
    },
    { immediate: true },
);

function getNextCheckResult(cb: (str?: string) => void) {
    if (!nextClickable.value) {
        return false;
    }
    if (mode.value === 'EXAM_MODE') {
        return true;
    }
    if (mode.value === 'PREPARE_MODE') {
        const errorListWidthoutBrowser = testErrorList.value.filter((item) => item.type !== 'browser');
        if (errorListWidthoutBrowser.length > 0) {
            cb(`您尚有 ${errorListWidthoutBrowser.length} 项未通过检测，正式考试时检测不通过会影响您的考试，确定要先进入下一步吗？`);
            return false;
        }
        return true;
    }
}

const refs = ref();
function retry() {
    refs.value.forEach((item: any) => {
        item.test();
    });
    // 重置监控状态并重新开启摄像头监控
    if (monitorStore.hasComputer) {
        monitorStore.openCameraMonitor(mode.value === 'PREPARE_MODE').catch((error) => {
            logger.error('Failed to restart camera monitor:', error);
        });
    }
    // 埋点
    BossAnalyticsTrack('zhice-pc-DeviceTesting-allRetry', {
        pData: {
            nameZh: '重试设备检测',
        },
    });
}

// 开启主摄像头
// if (monitorStore.hasComputer) {
//     monitorStore.openCameraMonitor(mode.value === 'PREPARE_MODE');
// }

const isShowHelpDialog = ref(false);
function showGuid() {
    isShowHelpDialog.value = true;
}

defineExpose({ getNextCheckResult });
</script>

<style lang="less" scoped>
@import './index.less';
</style>
