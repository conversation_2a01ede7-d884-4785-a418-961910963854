.round-content {
    position: relative;
    padding: 20px 32px 18px;
    background: linear-gradient(179.99998deg, #ffe2b6 1%, #ffe5df 51%, #fd9e9e 100%);
    box-shadow: inset 0 4px 35px 0 rgba(0, 74, 123, 0.23);
    backdrop-filter: blur(18.5px);
    width: 100%;
    height: 100%;
    overflow: hidden;

    &::after {
        content: '';
        position: absolute;
        left: 0;
        height: 261px;
        right: 0;
        bottom: -5px;
        background-image: url('https://img.bosszhipin.com/static/file/2024/h6xy4k3ehq1725606964354.png.webp');
        background-size: 100% auto;
        z-index: -1;
    }

    .title-wrap {
        display: flex;
        align-items: center;
        position: relative;

        .progress-wrap {
            position: absolute;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 179px;
            overflow: hidden;
            width: 340px;
            height: 20px;
            top: 8px;
            left: 205px;

            .bar {
                width: 242px;
                height: 20px;
                background: linear-gradient(
                    179.99998deg,
                    #febfa6 0%,
                    #f5731e 11%,
                    #ffcda6 45%,
                    #f5731e 90%,
                    #febfa6 100%
                );
                box-shadow: inset 1px 0px 3px 0px #ffffff;
                border-radius: 179px 0px 0px 179px;
                transition: all 0.5s;
            }

            .text {
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translateX(-50%) translateY(-50%);
                color: #000000;
                font-size: 13px;
                font-style: normal;
                font-weight: 400;
                line-height: 13px;
            }
        }

        .total-fund-container {
            min-width: 143px;
            flex-shrink: 0;
            position: absolute;
            right: 0;
            display: flex;
            align-items: center;
            padding: 0 12px 0 3px;
        }

        .gold-num-fade {
            position: absolute;
            right: 30px;
            top: 65px;
        }
    }

    .game-box-wrap {
        display: flex;
        align-items: center;
        gap: 115px;
        padding: 40px 49px 0;
        .question-item-box {
            max-width: 416px;
            min-width: 416px;
            display: flex;
            gap: 12px 24px;
            flex-wrap: wrap;

            .question-item {
                min-width: 64px;
                width: 64px;
                height: 64px;
                line-height: 60px;
                background: #ffffff;
                color: #000;
                font-size: 30px;
                font-weight: 800;
                border: 3px solid #ffe6cf;
                border-radius: 16px;
                text-align: center;
                position: relative;
            }
            .position-center {
                border: 3px solid #ff905d;
            }
            .position-1 {
                transform: rotate(-90deg);
            }
            .position-2 {
                transform: rotate(90deg);
            }
            .position-4 {
                transform: rotate(0deg);
            }
            .position-3 {
                transform: rotate(180deg);
            }

            .success::after {
                content: '';
                width: 100%;
                height: 100%;
                border-radius: 20px;
                position: absolute;
                top: 0px;
                left: 0;
                display: inline-block;
                background-image: url('https://img.bosszhipin.com/static/zhipin/313500031537167438.svg');
                background-size: 37px 33px;
                background-position: center;
                background-repeat: no-repeat;
            }

            .fail::after {
                content: '';
                width: 100%;
                height: 100%;
                border-radius: 20px;
                position: absolute;
                top: 0px;
                left: 0;
                display: inline-block;
                background-image: url('https://img.bosszhipin.com/static/zhipin/523500031537147862.svg');
                background-size: 37px 33px;
                background-position: center;
                background-repeat: no-repeat;
            }
            .success,
            .fail {
                img {
                    display: none;
                }
            }
            .transition-state {
                img {
                    opacity: 0.2;
                }
            }
            .position-center {
                opacity: 1;
                img {
                    opacity: 1;
                }
            }
        }
        .option-button-box {
            text-align: center;
            margin-top: 40px;
            .option-button-wrap {
                width: 128px;
                height: 130px;
                position: relative;
                .btn {
                    width: 42px;
                    height: 42px;
                    cursor: pointer;
                }
                .button-inner {
                    &-3 {
                        background-image: url('https://img.bosszhipin.com/static/zhipin/kanjian/782593614637194344.svg');
                        background-size: 42px 42px;
                        background-position: center;
                        background-repeat: no-repeat;
                        position: absolute;
                        top: 42px;
                        left: 0;
                        &:active {
                            background-image: url('https://img.bosszhipin.com/static/zhipin/kanjian/20359361463715097.svg');
                        }
                    }

                    &-1 {
                        background-image: url('https://img.bosszhipin.com/static/zhipin/kanjian/392593614637113927.svg');
                        background-size: 42px 42px;
                        background-position: center;
                        background-repeat: no-repeat;
                        position: absolute;
                        top: 0;
                        left: 42px;
                        &:active {
                            background-image: url('https://img.bosszhipin.com/static/zhipin/kanjian/562593614637193044.svg');
                        }
                    }
                    &-4 {
                        background-image: url('https://img.bosszhipin.com/static/zhipin/kanjian/662593614637123167.svg');
                        background-size: 42px 42px;
                        background-position: center;
                        background-repeat: no-repeat;
                        position: absolute;
                        top: 42px;
                        right: 0;
                        &:active {
                            background-image: url('https://img.bosszhipin.com/static/zhipin/kanjian/282593614637164814.svg');
                        }
                    }
                    &-2 {
                        background-image: url('https://img.bosszhipin.com/static/zhipin/kanjian/872593614637178394.svg');
                        background-size: 42px 42px;
                        background-position: center;
                        background-repeat: no-repeat;
                        position: absolute;
                        bottom: 0;
                        left: 42px;
                        &:active {
                            background-image: url('https://img.bosszhipin.com/static/zhipin/kanjian/982593614637123394.svg');
                        }
                    }
                }
            }
            .key-down-desc {
                color: #000000;
                font-size: 10px;
                font-weight: 400;
                margin-bottom: 6px;
                text-align: center;
                margin-top: 15px;
            }
            .disabled {
                opacity: 0.6;
                .btn {
                    cursor: default;
                }
                div {
                    pointer-events: none;
                }
            }
        }
    }
}
