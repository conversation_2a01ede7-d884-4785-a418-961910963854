import { createSharedComposable } from '@crm/vueuse-pro';
import { nextTick, ref } from 'vue';

const timeMap = {
    'countdown-start-message': 3500,
    'tips-message': 1000,
};

function defaultNotificationInfo() {
    return {
        type: '', // countdown-message: 倒计时消息类型(游戏开始); tips-message:（轻提示）; template-message（html结构消息，根据 action 字段 区分不同的模板）
        time: 3.5 * 1000, // 倒计时时长  countdown-message类型下有用
        action: '', // 自定义动作名称
        text: '', // 展示的消息内容
        number: 0, // 金币的金额
        theme: '',
        onNext: () => {}, // 下一步动作 (template-message 消息 下 按钮点击回调)
        onClose: () => {}, // 关闭回调
    };
}

function useNotificationTips() {
    const visible = ref(true);
    const notificationInfo = ref({
        ...defaultNotificationInfo(),
    });

    const onOopen = (info: any = {}) => {
        notificationInfo.value = {
            ...defaultNotificationInfo(),
            time: timeMap[info.type] || 3.5 * 1000,
            ...info,
        };

        nextTick(() => {
            visible.value = true;
        });
    };

    const onClose = () => {
        visible.value = false;
        notificationInfo.value.onClose();
    };

    return {
        visible,
        notificationInfo,
        open: onOopen,
        close: onClose,
    };
}

export const useNotification = createSharedComposable(useNotificationTips);
