import { useAnswerDialogController } from '@/hooks/useAnswerDialogController';
import { useEventSubscription } from '@/hooks/useEventSubscription';
import { useToast } from '@/hooks/useToast';
import { createTimer, timeCenter } from '@/store/time';
import { useMonitorStore } from '@/store/use-monitor-store';
import { defineStore } from 'pinia';
import { ref } from 'vue';
import { QUESTION_TYPE } from './constant';
import { useEndTimeStore } from '@/store/exam-time';

const monitorStore = useMonitorStore();

function initSceneQuery(sceneList: any) {
    const newSceneQuestionData = <any>{};

    // 匹配所有场景题
    sceneList.forEach((item: any, index: number) => {
        const data = item.questionSnapshot ? item.questionSnapshot : item;
        const { id } = data;
        newSceneQuestionData[id] = {
            isPreview: false,
            index: index + 1,
            data: {
                ...data,
            },
        };
    });
    return newSceneQuestionData;
}

export const useHotsStore = defineStore('hotsStore', () => {
    const { examInfo } = monitorStore.examBaseInfo;
    const currentIndex = ref(0); // 当前答题的下标
    const currentQuestion = ref({
        id: '',
        questionTitle: '',
        questionType: 0,
        parentEncryptId: '',
        encryptId: '',
        questionShowTs: 0,
        product10QuestionParamVO: {} as any,
        parentIndex: 0,
        groupIndex: 0,
    }); // 当前题
    const questionList = ref<any[]>([]); // 所有的题
    const answerData = ref<Record<string, any>>({}); // 所有答案
    const sceneQuestionData = ref<any>({}); // 所有背景信息
    // const displayQuestion = ref([])
    const currentHistoryScheme = ref<any[]>([]); // 当前历史方案（数据set在业务中调用）

    // 答案区 toast 控制
    const answerToast = useToast();

    const { currentTime } = timeCenter;
    const endTimeStore = useEndTimeStore();

    // 被动：题目倒计时结束逻辑
    const handleQuestionTimeFinished = () => {
        answerDialogController.reset();

        const nextIndex = currentIndex.value + 1;

        if (nextIndex === questionList.value.length) {
            // 超时提交
            // basePaperInfo.submitPaper('paper_auto_submit')
            return;
        }

        const currentIndexStr = currentQuestion.value.parentIndex;
        const currentQuestionStr = currentQuestion.value.groupIndex;

        if (questionList.value[nextIndex]) {
            const nextIndexStr = questionList.value[nextIndex].parentIndex;
            const nextQuestionIndexStr = questionList.value[nextIndex].groupIndex;

            answerToast.open({
                overlay: true,
                title: `题目${currentIndexStr}-${currentQuestionStr}作答时间已结束，答案已提交，请继续题目${nextIndexStr}-${nextQuestionIndexStr}作答。`,
                status: 'warning',
                onCloseComplete: async () => {
                    const params = {
                        answerContent: answerData.value[currentQuestion.value.encryptId] || '[]',
                    };
                    await postQuestion(params);
                    nextQuestion();
                },
            });
        }
    };

    const questionCountdown = createTimer();

    /**
     * 答题区逻辑
     */
    const answerDialogController = useAnswerDialogController(currentQuestion, questionCountdown);

    let popupCleanup: (() => void) | null = null;

    const initQuestionData = (data: any) => {
        answerDialogController.init(data.extra_list);

        const { parent_list, answer_done, answer_todo, paper_submit, examEndTimeTs } = data;
        const allQuestionList = [...answer_done, ...answer_todo];

        const allQuestion: any[] = [];
        let groupIndex = 0;
        let groupFlag: null = null;
        const newSceneQuestionData = initSceneQuery(parent_list || []);
        const newAnswer = <any>{};

        // 同步所有问题
        allQuestionList.forEach((item) => {
            const { questionSnapshot, encryptId, tmpAnswerContent, questionShowTs } = item;
            let answer = null;

            if (groupFlag != questionSnapshot.parentId) {
                groupIndex = 0;
                groupFlag = questionSnapshot.parentId;
            }

            // 同步已答问题答案
            // HOTS 连线题与方案题
            if ([QUESTION_TYPE.HOTS_LIGATURE, QUESTION_TYPE.HOTS_SCHEME].includes(questionSnapshot.questionType)) {
                // questionType
                answer = tmpAnswerContent;
            } else if ([QUESTION_TYPE.FORCED].includes(questionSnapshot.questionType)) {
                // 破选题
                answer = questionSnapshot.fillBlankList.map((item: any) => item.answerContent);
            } else {
                const data = (questionSnapshot.questionOptionList || []).find((x: any) => x.selected === 1);
                answer = data?.encryptId ?? '';
            }

            if (answer) {
                newAnswer[encryptId] = answer;
            }

            const parentIndex = parent_list.findIndex((obj: any) => {
                return obj.questionSnapshot.id === questionSnapshot.parentId;
            });

            allQuestion.push({
                ...questionSnapshot,
                groupIndex: (groupIndex += 1),
                encryptId,
                parentEncryptId: questionSnapshot.parentId,
                parentIndex: parentIndex + 1,
                questionShowTs,
            });
        });

        // 计算当前展示的题
        let currentI = answer_done.length;
        if (paper_submit === 0 && answer_done.length === allQuestion.length) {
            currentI = allQuestion.length - 1;
        }

        // 场次结束时间
        endTimeStore.initExamEndTime(examEndTimeTs);

        sceneQuestionData.value = newSceneQuestionData;
        questionList.value = allQuestion;
        answerData.value = newAnswer;
        currentIndex.value = currentI;
        currentQuestion.value = allQuestion[currentI];
    };

    // 获取试卷所有题组信息
    const getQuestion = async (params: any) => {
        // 获取试卷信息
        const res = await Invoke.hots.getAnswerList(params);

        if (res.code !== 0) {
            return;
        }
        initQuestionData(res.data);
        return res;
    };

    const getPreviewInfo = async (params: any) => {
        const res = await Invoke.preview.paperHotsPreview(params);
        if (res.code !== 0) {
            return;
        }
        initQuestionData(res.data);
    };

    // 设置方案题历史记录
    const setCurrentHistoryScheme = (list: any = []) => {
        const currentHistoryList: any[] = [];
        list.forEach((item: { childQuestionId: string }) => {
            if (item.childQuestionId === currentQuestion.value.id) {
                currentHistoryList.push({ ...item });
            }
        });
        currentHistoryScheme.value = currentHistoryList;
    };

    // 设置答案
    const setAnswer = (key: string, val: any) => {
        answerData.value = {
            ...answerData.value,
            [key]: val,
        };
    };

    // 提交试题
    const postQuestion = async (params?: any, fn?: (params: any) => void) => {
        const paramsData = {
            encryptExamId: examInfo?.examId,
            evaPaperSnapshotId: currentQuestion.value.encryptId,
            questionType: currentQuestion.value.questionType,
            ...params,
        };

        const { code } = await Invoke.common.postFormAddAnswer(paramsData);

        if (code === 0) {
            fn?.(paramsData);
        }
    };

    // answerDialogController.subscribeAddDisplayQuestionEvent();

    // 主动：题目倒计时开始逻辑
    const questionTimeStart = () => {
        const { product10QuestionParamVO, questionShowTs } = currentQuestion.value;
        // const questionTime = (currentQuestion.value.product10QuestionParamVO?.answerTime || 0) * 1000
        const questionTime = (product10QuestionParamVO?.answerTime || 0) * 1000;

        questionCountdown.start({
            key: 'QuestionCountdown',
            finishTime: () => (questionShowTs || currentTime.value) + questionTime,
            onFinished: handleQuestionTimeFinished,
        });

        // 本题作答时间剩余30秒
        useEventSubscription(
            'pushQuestion',
            () => {
                const condition = questionCountdown.remainingTime.total < 30000 && questionCountdown.remainingTime.total > 29000;
                return condition;
            },
            () => {
                answerToast.open({
                    title: `本题作答时间剩余30秒`,
                    status: 'warning',
                });
            },
        );

        // 处理弹窗 只有在连线题 才需要关联弹窗
        if (currentQuestion.value.questionType === QUESTION_TYPE.HOTS_LIGATURE) {
            answerDialogController.start();

            // 启动弹窗检查器
            if (popupCleanup) {
                popupCleanup(); // 清理之前的检查器
            }
            popupCleanup = answerDialogController.subscribeAddDisplayQuestionEvent();
        } else {
            if (popupCleanup) {
                popupCleanup();
                popupCleanup = null;
            }
        }
    };

    // 下一题
    const nextQuestion = () => {
        let index = currentIndex.value;
        if (index >= questionList.value.length - 1) {
            return;
        }

        index += 1;
        currentQuestion.value = questionList.value[index];
        currentIndex.value = index;
        questionTimeStart();
    };

    return {
        currentIndex,
        currentQuestion,
        questionList,
        answerData,
        sceneQuestionData,
        currentHistoryScheme,
        answerDialogController,
        answerToast,
        handleQuestionTimeFinished,
        questionCountdown,
        questionTimeStart,
        getQuestion,
        initQuestionData,
        nextQuestion,
        setCurrentHistoryScheme,
        setAnswer,
        postQuestion,
        getPreviewInfo,
    };
});
