{"extends": "@boss/tsconfig/tsconfig.base.json", "compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "bundler", "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noUnusedLocals": true, "noUnusedParameters": true, "lib": ["ESNext", "DOM", "DOM.Iterable"], "baseUrl": ".", "paths": {"@vue/shared": ["./node_modules/@vue/shared"]}, "jsxImportSource": "vue", "esModuleInterop": true, "allowSyntheticDefaultImports": true}, "include": ["env.d.ts", "shims.d.ts"], "exclude": ["node_modules"]}