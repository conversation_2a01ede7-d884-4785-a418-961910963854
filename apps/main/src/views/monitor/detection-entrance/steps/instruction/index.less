.instruction-panel {
    padding-top: 19px;
    .remarks {
        padding: 24px;
        line-height: 22px;
        background: #ecf9f9;
        font-size: 13px;
        border-radius: 12px;
        white-space: pre-line;
    }
    .exam-list {
        padding: 12px 0;
        .exam-item {
            padding: 24px 32px;
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #f0f1f2;
            & + .exam-item {
                margin-top: 12px;
            }
            .title {
                display: flex;
                align-items: center;
                height: 22px;
                h3 {
                    font-size: 16px;
                    font-weight: 500;
                    line-height: 22px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
                .status {
                    flex-shrink: 0;
                    margin-left: 8px;
                    padding: 2px 12px;
                    line-height: 22px;
                    background: rgba(18, 173, 169, 0.08);
                    border-radius: 11px;
                    font-size: 13px;
                    font-weight: 500;
                    color: #12ada9;
                }
            }
            .content {
                display: flex;
                margin-top: 10px;
                line-height: 18px;
                font-size: 13px;
                .label {
                    color: #808080;
                }
                .value {
                    color: #4d4d4d;
                }
                .duration {
                    margin-left: 16px;
                }
            }
        }
    }
}
