<template>
    <Transition name="slide-fade" @afterLeave="onCloseComplete">
        <div v-if="opened" class="message" :class="{ warning: status === 'warning' }">
            <SvgIcon :name="status === 'warning' ? 'toast-warning' : 'toast-info'" width="16" height="16" />
            {{ title }}
        </div>
    </Transition>
    <Transition name="slide-fade">
        <div v-if="opened" :class="{ overlay }" />
    </Transition>
</template>

<script setup lang="ts" name="AnswerTooltip">
import type { CustomToast } from '@/hooks/useToast';

defineProps<CustomToast>();
</script>

<style lang="less" scoped>
.slide-fade-enter-active,
.slide-fade-leave-active {
    transition: all 0.3s ease-out;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
    opacity: 0;
}

.message {
    max-width: 400px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 12px;
    display: flex;
    padding: 12px 16px;
    gap: 8px;
    justify-content: center;
    align-items: center;
    background: #d6ebff;
    box-shadow: 0px 2px 10px 0px rgba(93, 119, 140, 0.15);
    border: 1px solid #add6ff;
    border-radius: 22px;
    text-wrap: pretty;
    width: max-content;
    user-select: none;
    z-index: 200;
}

.warning {
    background: #ffe1d6;
    border: 1px solid #ffd2c1;
}

.overlay {
    position: absolute;
    top: -20px;
    left: -20px;
    background: #ffffff;
    opacity: 0.7;
    width: calc(100% + 40px);
    height: calc(100% + 40px);
    z-index: 100;
    pointer-events: none;
}
</style>
