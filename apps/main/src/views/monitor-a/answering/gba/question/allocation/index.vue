<template>
    <div class="round-content">
        <div class="title-wrap">
            <Text :font-weight="600" :stroke-width="2" :text="`${numberToChinese(gbaStore.currentIndex + 1, false)}、${gbaStore.currentQuestion.showName}`" />
            <div class="progress-wrap">
                <p :style="{ width: `${(roundPassed / roundList.length) * 100}%` }" class="bar" />
                <p class="text">剩余回合 {{ roundList.length - roundPassed }} 轮</p>
            </div>
            <TotalFund :goldNum="sum(Object.values(rewardMap))" />
        </div>
        <div class="allocation-wrap">
            <PlayerCard
                v-for="gamePlayerItem in gamePlayers"
                :key="gamePlayerItem.paramId"
                :avatarConfig="{
                    show: true,
                    url: gamePlayerItem.paramImg,
                }"
                :castShadow="true"
                :coin="rewardMap[gamePlayerItem.paramId] || 0"
                :fundAnimationConfig="getSubFundAnimation(showFundAnimation, gamePlayerItem)"
                :playerName="gamePlayerItem.paramName"
                :showJoin="gamePlayerItem.showJoin"
                :size="gamePlayerItem.paramId === 1 ? 'large' : 'small'"
                @fundAnimationEnd="onPplFundAnimationEnd"
                @fundStartVanish="onPplFundStartVanish"
            />
        </div>
        <div v-if="showWaiting" class="waiting">请等待其他玩家做出选择<span class="dot">...</span></div>
        <div v-if="showBtnGroup || (showCountdown && !showCountdownTip)" class="option-button-wrap">
            <template v-for="item in optionList" :key="item.id">
                <div :class="{ err: item.status }" class="button-inner">
                    <Button :disabled="btnGroupDisabled" :isActiveEffect="!btnGroupDisabled" :theme="item.theme" @click="handleInvest(item)">
                        <img v-if="selectData.playerParticipate === item.id" :src="item.img" alt="" style="margin-top: 2.5px" />
                        <Text
                            v-else
                            :font-weight="600"
                            :shadowFilterConfig="item.shadowFilterConfig"
                            :stroke-width="1"
                            :strokeColor="item.strokeColor"
                            :text="item.content"
                            :textColor="item.color"
                            font-size="20px"
                        />
                    </Button>
                    <p class="key-down-desc">快捷键“{{ item.shortcutKeys }}”</p>
                </div>
            </template>
        </div>
        <div v-if="showCountdown && !showCountdownTip" class="countdown-info-wrap">{{ Math.floor(roundCountdown?.remainingTime.total / 1000) }}S</div>
        <div v-if="showCountdownTip" class="countdown-info-tip countdown-info-wrap">参与投资</div>
        <InvestmentResult
            v-model="isOpen"
            :currentRoundSnapshot="currentRoundSnapshot"
            :distributeTime="gbaStore.currentQuestion.distributeTime"
            :gamePlayers="gamePlayers"
            :realityParticipant="realityParticipant"
            :reward="getTotalReward"
            :rewardMap="rewardMap"
            :successMinParticipant="askParticipant"
            :type="investmentResultType"
            @allocationSubmit="allocationSubmit"
            @assignStartAppear="investmentResultType = 'assign'"
            @dialogCloseAnimationEnd="dialogCloseAnimationEnd"
            @roundCountdownOnFinished="roundEnd(true)"
        />
    </div>
</template>

<script lang="ts" setup>
import { createTimer, timeCenter } from '@/store/time';
import { useNotification } from '../../component/notification-tips/index';
import { useGbaStore } from '../../store';
import { onKeyStroke } from '@crm/vueuse-pro';
import { random, sum } from 'lodash-es';
import { computed, nextTick, onMounted, reactive, ref } from 'vue';
import Button from '../../component/button.vue';
import Text from '../../component/text.vue';
import { delay, generateArray, getRandomElement, getRandomNumbers, OPTION_LIST, resultMap } from './const';
import InvestmentResult from './ui/investment-result.vue';
import PlayerCard from './ui/player-card.vue';
import TotalFund from './ui/total-fund.vue';
import { numberToChinese } from '@/utils/number-to-chinese';

const props = defineProps({
    isSimulate: {
        type: Boolean,
        default: false,
    },
});
const emits = defineEmits(['roundOver', 'gameOver', 'roundStart', 'roundChange']);
const gbaStore = useGbaStore();
const { currentTime } = timeCenter;
const useNotificationTips = useNotification();

// 投票阶段-参与/不参与按钮配置
const optionList = ref<Array<any>>(OPTION_LIST());
// 游戏回合数据
const roundList = computed(() => {
    if (props.isSimulate) {
        // 模拟模式下 使用模拟数据
        return gbaStore.currentQuestion.simulateList;
    } else {
        return gbaStore.currentQuestion.roundList;
    }
});
// 参与游戏的玩家数据(头像、名称)
const gamePlayers: any = ref(gbaStore.currentQuestion.assetsCongig);
// 当前回合数据
const currentRoundData: any = computed(() => roundList.value[roundPassed.value]);
// 当前回合游戏数据快照
const currentRoundSnapshot = computed(() => currentRoundData.value?.roundSnapshot || {});

const rewardMap: any = ref(gbaStore.currentQuestion.rewardMap || {});
// 已处理回合数
const roundPassed = ref(0);
// 当前回合的所处阶段 1：投票阶段、2：投票结果公示、3：待分配金币、4： 分配金币结果、5: 手动分配确认
const currentStage = ref(1);

function defaultSelectData() {
    return {
        encSnapshotId: '', // 当前回合id
        paramId: 0, // 用户选择的数据（矿山id）
        startDistributeTimeTs: 0, // 开始分配时间
        chooseTimeTs: 0, // 用户选择的时间点
        showTimeTs: 0, // 当前回合首次展示的时间
        timeoutTs: 0, // 当前回合超时时间点 (与 chooseTime 互斥);
        result: 0, // 本轮是成功还是失败
        distributeTimeTs: 0, // 分配时间  系统自动分配/分配超时 时为0，单位毫秒
        playerParticipate: null, // 玩家是否参与 0=未参与 1=参与
        participantDetail: [], // 除玩家外，参与人员的信息
        distributeDetail: [], // 分配详情，所有参与人的信息(玩家+机器人)，系统分配也需要。只有分配超时时 这里才为空
    };
}

// 缓存已选择的数据，用于数据提交与金币飞入坐标
let selectData: any = reactive(defaultSelectData());

function getSubFundAnimation(showFundAnimation: boolean, gamePlayerItem: { paramId: number }) {
    const { action, text, manualAssign, show } = fundAnimationConfig.value;
    const tempText = manualAssign ? `${action}￥${manualAssignRewardMap.value[gamePlayerItem.paramId] || 0}` : `${action}￥${text}`;
    const tempShow = showFundAnimation && (manualAssign ? !!manualAssignRewardMap.value[gamePlayerItem.paramId] : true);
    if (show) {
        return {
            ...fundAnimationConfig.value,
            text: tempText,
            show: tempShow,
        };
    }
    return {
        ...fundAnimationConfig.value,
        text: tempText,
        show: tempShow && (selectData.participantDetail.includes(gamePlayerItem.paramId) || (selectData.playerParticipate === 1 && gamePlayerItem.paramId === 1)),
    } as any;
}

// 等待中状态
const showWaiting = ref(false);
// 是否展示倒计时
const showCountdown = ref(true);
// 是否展示参与or不参与按钮
const showBtnGroup = ref(true);
const btnGroupDisabled = ref(false);

// 点击参与or不参与
function handleInvest(row: { id: number }) {
    if (btnGroupDisabled.value) {
        return;
    }
    selectData.playerParticipate = row.id;
    selectData.chooseTimeTs = currentTime.value;

    resetStatus();
    showCountdown.value = false; // 倒计时隐藏
    showWaiting.value = true; // 展示等待其他玩家选择提示
    btnGroupDisabled.value = true;
    if (row.id === 1) {
        // 标记玩家已参与
        const playerIndex = gamePlayers.value.findIndex((player: { paramId: number }) => player.paramId === 1);
        if (playerIndex !== -1) {
            gamePlayers.value[playerIndex] = {
                ...gamePlayers.value[playerIndex],
                showJoin: true,
            };
        }
    }
    // 设置机器人玩家
    setRobotPlayer(row);
    // 停止倒计时
    roundCountdown.stop();
}

// 获取除玩家外，参与人员的信息
function getParticipantDetail(count: number) {
    const numbers = gamePlayers.value.map((item: { paramId: any }) => item.paramId)?.filter((x: number) => x !== 1);
    return getRandomNumbers(numbers, count);
}

// 要求人数
const askParticipant = ref(0);
// 实际参与人数
const realityParticipant = ref(0);
const investmentResultType = ref<any>();

const robotPlayerTotal = computed(() => gamePlayers.value.length - 1);

// 设置机器人玩家
async function setRobotPlayer(row: { id: any }) {
    const { result, successMinParticipant } = currentRoundSnapshot.value;
    let successMinParticipantNum = successMinParticipant;
    let list: number[] = [];
    if (result === 1) {
        // 本轮结果 1=成功
        list = generateArray(robotPlayerTotal.value - successMinParticipantNum + 1, successMinParticipantNum);
        // 玩家参与
        if (row.id === 1) {
            successMinParticipantNum = successMinParticipantNum - 1;
            const length = robotPlayerTotal.value - successMinParticipantNum + 1;
            list = generateArray(length, successMinParticipantNum);
        }
    }
    if (result === 0) {
        // 本轮结果 0=失败
        list = generateArray(successMinParticipantNum, 0);
        // 玩家参与
        if (row.id === 1) {
            successMinParticipantNum = successMinParticipantNum - 1;
            const length = Math.max(successMinParticipantNum, 0);
            list = generateArray(length, 0);
        }
    }
    // 除玩家外，参与人员的信息
    selectData.participantDetail = getParticipantDetail(getRandomElement(list));
    // 实际参与人数
    realityParticipant.value = selectData.participantDetail.length + (row.id === 1 ? 1 : 0);
    // 要求参与人数
    askParticipant.value = successMinParticipant;

    // 更新所有人的投票情况
    await updateInvestReward();
    // 本人不参与游戏并且无机器人玩家参与直接进入投票结果阶段
    if (!selectData.participantDetail.length && row.id === 0) {
        announcementOfResultsStage(row);
    }
}

async function updateInvestReward() {
    // 倒计时结束前，每轮次用户进行选择后，随机1-5秒一个值，进入《2、投票结果公示》
    const interval = random(1, 5);
    await delay(interval);
    showWaiting.value = false;
    showBtnGroup.value = false;
    showFundAnimation.value = true; // 参与投资的玩家金额-5
    gamePlayers.value = gamePlayers.value.map((item: { paramId: number }) => ({
        ...item,
        showJoin: selectData.playerParticipate && item.paramId === 1 ? true : selectData.participantDetail.includes(item.paramId),
    }));
}

// 进入到结果公示阶段
async function announcementOfResultsStage(row: { id: any }) {
    const { result, playerDistribute } = currentRoundSnapshot.value || {};
    currentStage.value = 2;
    animationEndNum.value = 0;
    animationStartNum.value = 0;
    // 避免 any 导致的索引报错，进行类型断言
    investmentResultType.value = resultMap[result as 0 | 1];
    allocationReward(row);
    // 延迟2s(投票结果公示,2秒后状态变更进入到下个阶段)
    await delay(2);
    // 本人不参与游戏并且无机器人玩家参与直接进入投票结果阶段之后直接进入下一回合游戏
    if (!selectData.participantDetail.length && row.id === 0) {
        currentStage.value = 1;
        return;
    }
    // 投资成功
    if (result === 1) {
        // 是否玩家分配 0=否（系统分配） 1=是（手动分配）
        if (playerDistribute === 0) {
            currentStage.value = 4;
        }
        if (playerDistribute === 1) {
            currentStage.value = 3;
            selectData.startDistributeTimeTs = currentTime.value;
        }
    }
    // 如果投资失败，则进入到下一轮次的投票阶段
    if (result === 0) {
        currentStage.value = 1;
    }
}

/**
 * 更新所有人的投票情况
 * @param row
 * 倒计时结束前，每轮次用户进行选择后，随机1-5秒一个值，进入《2、投票结果公示》
 * 投票的小组金币余额在此时-5（有1.2s的动画）
 */
// 分配倒计时时间
const showFundAnimation = ref(false);
const fundAnimationConfig = ref({
    show: false,
    text: '5',
    action: '-',
    manualAssign: false,
});
// 动画渐出开始
const animationStartNum = ref(0);

async function updateRewardsMap(isEqualDistribution: boolean, incrementRewards: any) {
    Object.keys(rewardMap.value).forEach((id) => {
        const reward = isEqualDistribution ? incrementRewards : incrementRewards[id] || 0;
        rewardMap.value[id] = rewardMap.value[id] + reward;
    });
    // 呈现结果1秒后，如果还有剩余轮次则进行下一轮
    await delay(0.3);
    roundEnd();
}

const singlePersonReward = ref(0);
const manualAssignRewardMap = ref<any>({});

// 金币分配结果阶段数据处理
function allocationRewardStageDataProcess() {
    if (currentStage.value === 4 && animationStartNum.value === gamePlayers.value.length) {
        updateRewardsMap(true, singlePersonReward.value);
    }
    if (currentStage.value === 5 && animationStartNum.value === Object.keys(manualAssignRewardMap.value).filter((item) => manualAssignRewardMap.value[item]).length) {
        updateRewardsMap(false, manualAssignRewardMap.value);
    }
}

function onPplFundStartVanish() {
    animationStartNum.value += 1;
    if (currentStage.value === 1 && animationStartNum.value === realityParticipant.value) {
        // 投票的小组金币余额在此时-5
        selectData.participantDetail.forEach((id: string | number) => {
            rewardMap.value[id] = rewardMap.value[id] - 5;
        });
        if (selectData.playerParticipate === 1) {
            rewardMap.value['1'] = rewardMap.value['1'] - 5;
        }
    }
    allocationRewardStageDataProcess();
}

// 投资阶段数据处理
function investmentStageDataProcess() {
    if (currentStage.value === 1 && animationEndNum.value === realityParticipant.value) {
        showFundAnimation.value = false;
        fundAnimationConfig.value = {
            show: false,
            text: '5',
            action: '-',
            manualAssign: false,
        };
        const row = optionList.value.find((item) => item.id === selectData.playerParticipate) || {};
        announcementOfResultsStage(row);
    }
}

// 动画结束
const animationEndNum = ref(0);

function onPplFundAnimationEnd() {
    animationEndNum.value += 1;
    investmentStageDataProcess();
}

const rewardValueNew = ref<any>();

// 手动分配确认提交
function allocationSubmit(rewardValue: { [key: number]: number }) {
    currentStage.value = 5;
    rewardValueNew.value = rewardValue;
}

const getTotalReward = ref(0);

// 系统进行平均分配金币
function allocationReward(row: { id: any }) {
    const len = row.id === 1 ? selectData.participantDetail.length + 1 : selectData.participantDetail.length;
    // 赚取金币数=本金*系数（参与组数）=每组固定5*组数*系数（参与组数）
    getTotalReward.value = 5 * len * len;
    singlePersonReward.value = getTotalReward.value / 5;
}

const isOpen = computed(() => [2, 3].includes(currentStage.value));

async function dialogCloseAnimationEnd(restType: string) {
    const { result, playerDistribute } = currentRoundSnapshot.value;
    if (['success', 'failure'].includes(restType)) {
        // 本人不参与游戏并且无机器人玩家参与直接进入投票结果阶段之后直接进入下一回合游戏
        if (!selectData.participantDetail.length && selectData.playerParticipate === 0) {
            selectData.distributeTimeTs = 0;
            selectData.startDistributeTimeTs = 0;
            roundEnd();
            return;
        }
        // 投资成功
        if (result === 1) {
            // 是否玩家分配 0=否（系统分配） 1=是（手动分配）
            if (playerDistribute === 0) {
                currentStage.value = 4;
                fundAnimationConfig.value = {
                    show: true,
                    text: String(singlePersonReward.value),
                    action: '+',
                    manualAssign: false,
                };
                showFundAnimation.value = true;
                selectData.distributeTimeTs = 0;
                selectData.startDistributeTimeTs = 0;
            }
            if (playerDistribute === 1) {
                currentStage.value = 3;
            }
        }
        // 如果投资失败，则进入到下一轮次的投票阶段
        if (result === 0) {
            selectData.distributeTimeTs = 0;
            selectData.startDistributeTimeTs = 0;
            roundEnd();
        }
    }
    if (['assign'].includes(restType)) {
        animationEndNum.value = 0;
        animationStartNum.value = 0;
        showFundAnimation.value = true;
        manualAssignRewardMap.value = rewardValueNew.value || {};
        fundAnimationConfig.value = {
            show: true,
            action: '+',
            text: '',
            manualAssign: true,
        };
        selectData.distributeTimeTs = currentTime.value;
    }
}

// 设置回合默认数据
function setRoundData(index: number = -1) {
    const roundPassedIndex = index > -1 ? index : roundPassed.value;
    const roundData: any = roundList.value[roundPassedIndex];
    // 设置已选择的数据
    selectData = {
        ...defaultSelectData(),
        showTimeTs: currentTime.value, // 设置回合曝光时间
        encSnapshotId: roundData.encryptId, // 回合ID
        result: roundData.result,
        timeoutTs: 0,
        distributeTimeTs: 0,
    };
}

const roundCountdown = createTimer();

function resetStatus() {
    showWaiting.value = false;
    fundAnimationConfig.value = {
        show: false,
        text: '5',
        action: '-',
        manualAssign: false,
    };
    currentStage.value = 1;
    animationEndNum.value = 0;
    animationStartNum.value = 0;
    showFundAnimation.value = false;
    showCountdown.value = true;
    showBtnGroup.value = true;
    realityParticipant.value = 0;
    askParticipant.value = 0;
    btnGroupDisabled.value = false;
    manualAssignRewardMap.value = {};
    gamePlayers.value = gbaStore.currentQuestion.assetsCongig.map((item) => ({ ...item, showJoin: false }));
}

// 回合结束
function roundEnd(isTimeOut: boolean = false) {
    const { result, playerDistribute } = currentRoundSnapshot.value || {};
    if (!currentRoundSnapshot.value) {
        return;
    }
    const params = {
        ...selectData,
        distributeDetail: result
            ? Object.keys(rewardMap.value).map((id) => ({
                  participantId: id,
                  distributeAmount: playerDistribute ? manualAssignRewardMap.value[id] || 0 : singlePersonReward.value,
              }))
            : [],
        result: currentRoundSnapshot.value?.result,
    };
    if (isTimeOut) {
        params.timeoutTs = currentTime.value;
        params.distributeTimeTs = 0;
        params.distributeDetail = [];
        params.startDistributeTimeTs = 0;
    }
    emits('roundOver', params);
    roundPassed.value += 1;
    if (isTimeOut) {
        useNotificationTips.open({
            type: 'tips-message',
            text: '作答超时',
            onClose: () => {
                roundContinue();
            },
        });
        return;
    }

    nextTick(() => {
        roundContinue();
    });
}

const showCountdownTip = ref(true);

// 继续下一个回合
function roundContinue() {
    resetStatus();
    showCountdownTip.value = false;
    // 回合结束-游戏结束
    if (roundPassed.value >= roundList.value.length) {
        gameOver();
        btnGroupDisabled.value = true;
        emits('roundChange', { type: 'end' });
        return;
    }
    emits('roundChange', { currentQuestionId: currentRoundData.value.encryptId });
    nextTick(() => {
        setRoundData();
        roundCountdown?.start({
            key: 'roundCountdown',
            finishTime: (t) => t + gbaStore.currentQuestion.chooseTime,
            onFinished: () => {
                selectData.timeoutTs = currentTime.value;
                selectData.distributeDetail = [];
                // 倒计时结束时，如果玩家尚未做出选择，则提示"作答超时"，本轮次作废，进入到下一轮次的《1、投票阶段》
                if (selectData.playerParticipate === null) {
                    btnGroupDisabled.value = true;
                    roundEnd(true);
                }
            },
        });
    });
}

// 所有回合结束 游戏结束
function gameOver() {
    if (!props.isSimulate) {
        useNotificationTips.open({
            type: 'template-message',
            action: 'allocation-end',
            number: sum(Object.values(rewardMap.value)),
            onNext: () => {
                emits('gameOver');
            },
        });
    } else {
        emits('gameOver');
    }
}

// 通过键盘点击1、2来选择是否参与
onKeyStroke(['1', '2'], (e) => {
    if (currentStage.value === 3 || showCountdownTip.value) {
        return;
    }
    e.preventDefault();
    const data = optionList.value.find((item) => item.shortcutKeys === Number(e.key));
    if (data) {
        handleInvest(data);
    }
});
onMounted(() => {
    // 查看是否还有未答回合
    const index = roundList.value.findIndex((item) => item.answerStatus === 0);

    // 如果所有回合回答完毕 直接展示结算弹窗
    if (index < 0) {
        roundPassed.value = roundList.value.length;
        setRoundData(roundList.value.length - 1);
        gameOver();
    } else {
        roundPassed.value = index;
        setRoundData();
        useNotificationTips.open({
            type: 'countdown-start-message',
            onClose: () => {
                roundContinue();
            },
        });
    }
});
</script>

<style lang="less" scoped>
@keyframes dot-animation {
    33% {
        transform: translateY(-2em);
    }

    66% {
        transform: translateY(-1em);
    }
}
</style>

<style lang="less" scoped>
@import 'index';
</style>
