import { useRouter } from 'vue-router';
import { ExamStatusEnum } from '@crm/exam-types';
import { useTrackableFlow } from '@/store/track/use-trackable-flow';

export function useExamActions() {
    const $router = useRouter();

    const { execute: executeCommitPaper } = useTrackableFlow('提交试卷', async (ctx, encryptExamId: string, reasonText: string = '检测到作弊行为，系统已强制交卷') => {
        try {
            const params = { encryptExamId };
            ctx.logStep('开始提交试卷API调用', { params: params });
            const res = await Invoke.exam.postCommitPaper(params);
            ctx.logStep('提交试卷API调用完成', { res: res });
            if (res.code === 0) {
                ctx.logStep('试卷提交成功，跳转至状态页');
                $router.replace(`/status/${encryptExamId}?status=${ExamStatusEnum.已交卷}&text=${encodeURIComponent(reasonText)}`);
            } else {
                ctx.logStep('试卷提交失败', { status: 'failure', message: res.message });
                // Handle commit paper API failure (e.g., show a notification to the user)
                logger.error('Failed to commit paper:', res);
                // Optionally, provide feedback to the user here
            }
        } catch (error: any) {
            ctx.logStep('提交试卷过程中发生错误', { status: 'failure', error: error.message });
            logger.error('Error in commitPaper:', error);
            // Optionally, provide feedback to the user here
            throw error; // 重新抛出错误，以便调用方知道发生了错误
        }
    });

    // Potentially other exam-related actions could be added here in the future

    return {
        commitPaper: executeCommitPaper,
    };
}
