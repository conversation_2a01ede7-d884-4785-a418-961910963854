<template>
    <PaperTime v-if="!isExam" />
    <RouterView />
    <!-- <component :is="componentsMap[type]" v-if="componentsMap[type]" @onSubmitPaper="onSubmitPaper" @onQuestionChange="onQuestionChange" /> -->
    <DialogExamMediaMonitorHint v-model="dialogVisibleMonitorHintCamera" v-on="{ dialogClosed }" />
    <DialogExamPhoneMonitorHint v-model="dialogVisibleMonitorHintPhone" v-on="{ dialogClosed }" />
    <DialogExamScreenMonitorHint v-model="dialogVisibleMonitorHintScreen" v-on="{ dialogClosed }" />
    <NetworkDetect />
    <div id="body_detect" style="display: none" />
</template>

<script lang="ts" setup>
import DialogExamMediaMonitorHint from '@/components/dialog-exam-media-monitor-hint.vue';
import DialogExamPhoneMonitorHint from '@/components/dialog-exam-phone-monitor-hint.vue';
import DialogExamScreenMonitorHint from '@/components/dialog-exam-screen-monitor-hint.vue';
import NetworkDetect from '@/components/network-detect.vue';
import useAntiCheat from '@/hooks/useAntiCheat';
import useDialogHint from '@/hooks/useDialogHint';
import useMonitor from '@/hooks/useMonitor';
import { useReLoginFlow } from '@/hooks/flows/useReLoginFlow';
import useFullScreen from '@/hooks/useFullScreen';
import useSwitchScreen from '@/hooks/useSwitchScreen';
import { watch, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import PaperTime from '../components/paper-time.vue';
import commitPaper from '@/hooks/useCommitPaper';
import { useReplyType } from './composables/useReplyType';
import { useEvaluationEvents } from './composables/useEvaluationEvents';
import { useTrackableFlow } from '@/store/track/use-trackable-flow';
import { useEndTimeStore } from '@/store/exam-time';

const endTimeStore = useEndTimeStore();
const $route = useRoute();
const $router = useRouter();

// 使用新的组合式函数
const { isExam } = useReplyType();
const { onSubmitPaper, onQuestionChange } = useEvaluationEvents();
const { dialogVisibleMonitorHintCamera, dialogVisibleMonitorHintPhone, dialogVisibleMonitorHintScreen, dialogClosed } = useDialogHint();

const forceSubmitExam = (examSubmitType: string = 'timeout') => {
    if (isExam.value) {
        commitPaper(examSubmitType, $route, $router);
    } else {
        onSubmitPaper({ commitType: 3 }); // commitType 3 for timeout
    }
};

const { execute: executeInit } = useTrackableFlow('考试答题入口初始化', async (ctx) => {
    useMonitor();
    ctx.logStep('考试监控初始化完成');

    useFullScreen();
    ctx.logStep('全屏初始化完成');

    useSwitchScreen({
        examId: $route.params.examId as string,
        forceSubmitExamFn: () => {
            forceSubmitExam('switchScreenForce');
        },
    });
    ctx.logStep('切换屏幕初始化完成');

    useAntiCheat();
    ctx.logStep('防作弊初始化完成');

    useReLoginFlow().execute();
    ctx.logStep('重复登录弹窗流程初始化完成');
});

executeInit();

watch(
    () => [endTimeStore.examEndTime.remainingTime.total, endTimeStore.examEndTimeStatus],
    ([time, status]) => {
        if (time <= 0 && status === 1) {
            forceSubmitExam('timeout');
        }
    },
);

onUnmounted(() => {
    endTimeStore.clearExamEndTime();
});
</script>
