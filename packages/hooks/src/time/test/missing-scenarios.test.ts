import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ref, nextTick } from 'vue';
import { createTimeManager } from '../create-time-manager';
import { useTimeCenter } from '../use-time-center';
import { useTimer } from '../use-timer';
import { TimerStatus } from '../types';

describe('缺失的测试场景', () => {
    beforeEach(() => {
        vi.useFakeTimers();
        vi.clearAllMocks();
        global.performance = global.performance || {};
        global.performance.now = vi.fn(() => Date.now());
    });

    afterEach(() => {
        vi.useRealTimers();
        vi.restoreAllMocks();
    });

    describe('时间中心 - syncTime 边界情况', () => {
        it('应该处理syncTime返回字符串数字的情况', async () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
            const mockSyncTime = vi.fn().mockResolvedValue('1234567890' as any);

            const raf = vi.fn((callback: FrameRequestCallback) => {
                const id = setTimeout(() => callback(performance.now()), 16);
                return id as unknown as number;
            });
            const caf = vi.fn((id: number) => clearTimeout(id));

            const timeCenter = useTimeCenter({
                syncTime: mockSyncTime,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            expect(consoleSpy).toHaveBeenCalledWith('服务器返回无效时间戳:', '1234567890');

            timeCenter.stopUpdateTime();
            consoleSpy.mockRestore();
        });

        it('应该处理syncTime执行时间超过syncTimeFrequency的情况', async () => {
            let syncInProgress = false;
            const mockSyncTime = vi.fn().mockImplementation(() => {
                syncInProgress = true;
                return new Promise(resolve => {
                    setTimeout(() => {
                        syncInProgress = false;
                        resolve(Date.now());
                    }, 200); // 比syncTimeFrequency更长
                });
            });

            const raf = vi.fn((callback: FrameRequestCallback) => {
                const id = setTimeout(() => callback(performance.now()), 16);
                return id as unknown as number;
            });
            const caf = vi.fn((id: number) => clearTimeout(id));

            const timeCenter = useTimeCenter({
                syncTime: mockSyncTime,
                syncTimeFrequency: 100, // 短于sync执行时间
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 快速触发多次可能的同步
            for (let i = 0; i < 3; i++) {
                await vi.advanceTimersByTimeAsync(120);
                await nextTick();
            }

            // 验证同步不会重叠执行
            expect(mockSyncTime).toHaveBeenCalled();

            timeCenter.stopUpdateTime();
        });

        it('应该处理在同步过程中多次调用stopUpdateTime的情况', async () => {
            const mockSyncTime = vi.fn().mockImplementation(() => {
                return new Promise(resolve => {
                    setTimeout(() => resolve(Date.now()), 100);
                });
            });

            const raf = vi.fn((callback: FrameRequestCallback) => {
                const id = setTimeout(() => callback(performance.now()), 16);
                return id as unknown as number;
            });
            const caf = vi.fn((id: number) => clearTimeout(id));

            const timeCenter = useTimeCenter({
                syncTime: mockSyncTime,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 触发同步
            await vi.advanceTimersToNextTimerAsync();

            // 在同步过程中多次调用停止
            timeCenter.stopUpdateTime();
            timeCenter.stopUpdateTime();
            timeCenter.stopUpdateTime();

            expect(caf).toHaveBeenCalled();
        });
    });

    describe('时间中心 - 环境兼容性测试', () => {
        it('应该处理performance.now()返回异常值的情况', async () => {
            // 模拟performance.now()返回NaN
            global.performance.now = vi.fn().mockReturnValue(NaN);

            const raf = vi.fn((callback: FrameRequestCallback) => {
                const id = setTimeout(() => callback(NaN), 16);
                return id as unknown as number;
            });
            const caf = vi.fn((id: number) => clearTimeout(id));

            const timeCenter = useTimeCenter({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            expect(timeCenter.currentTime.value).toBeTypeOf('number');
            timeCenter.stopUpdateTime();
        });

        it('应该处理requestAnimationFrame为undefined的降级情况', () => {
            const originalRaf = globalThis.requestAnimationFrame;
            const originalCaf = globalThis.cancelAnimationFrame;

            // 模拟不支持requestAnimationFrame的环境
            delete (globalThis as any).requestAnimationFrame;
            delete (globalThis as any).cancelAnimationFrame;

            expect(() => {
                const timeCenter = useTimeCenter();
                timeCenter.stopUpdateTime();
            }).not.toThrow();

            // 恢复
            globalThis.requestAnimationFrame = originalRaf;
            globalThis.cancelAnimationFrame = originalCaf;
        });

        it('应该在没有console对象的环境下不崩溃', async () => {
            const originalConsole = globalThis.console;
            delete (globalThis as any).console;

            const mockSyncTime = vi.fn().mockRejectedValue(new Error('Test error'));

            const raf = vi.fn((callback: FrameRequestCallback) => {
                const id = setTimeout(() => callback(performance.now()), 16);
                return id as unknown as number;
            });
            const caf = vi.fn((id: number) => clearTimeout(id));

            expect(() => {
                const timeCenter = useTimeCenter({
                    syncTime: mockSyncTime,
                    requestAnimationFrame: raf,
                    cancelAnimationFrame: caf,
                });
                vi.advanceTimersToNextTimerAsync();
                timeCenter.stopUpdateTime();
            }).not.toThrow();

            globalThis.console = originalConsole;
        });
    });

    describe('计时器 - watch回调边界情况', () => {
        it('应该处理销毁后仍有watch回调触发的情况', async () => {
            const mockCurrentTime = ref(1000000);
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({});
            timer.destroy();

            // 尝试触发watch回调
            mockCurrentTime.value += 1000;
            await nextTick();

            expect(timer.status).toBe(TimerStatus.destroyed);
        });

        it('应该处理currentTime突然变为负数的情况', async () => {
            const mockCurrentTime = ref(1000000);
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value,
            });

            // 时间突然变为负数
            mockCurrentTime.value = -500000;
            await nextTick();

            expect(timer.elapsedTime.total).toBeLessThan(0);
            expect(timer.status).toBe(TimerStatus.running);
        });

        it('应该处理currentTime变为Infinity的情况', async () => {
            const mockCurrentTime = ref(1000000);
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value,
                finishTime: () => mockCurrentTime.value + 5000,
            });

            mockCurrentTime.value = Infinity;
            await nextTick();

            expect(timer.status).toBe(TimerStatus.stopped);
        });
    });

    describe('计时器 - 回调函数复杂交互', () => {
        it('应该处理beginTime函数抛出异步错误的情况', async () => {
            const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
            const mockCurrentTime = ref(1000000);
            const timer = useTimer({ currentTime: mockCurrentTime });

            const problematicBeginTime = () => {
                throw new Error('Sync error');
            };

            timer.start({
                beginTime: problematicBeginTime as any,
            });

            await nextTick();

            expect(timer.beginTime).toBe(mockCurrentTime.value);
            expect(consoleSpy).toHaveBeenCalled();

            consoleSpy.mockRestore();
        });

        it('应该处理在onTick回调中修改计时器配置的情况', async () => {
            const mockCurrentTime = ref(1000000);
            const timer = useTimer({ currentTime: mockCurrentTime });

            let tickCount = 0;
            const onTick = vi.fn((t) => {
                tickCount++;
                if (tickCount === 1) {
                    // 在回调中尝试重新配置计时器
                    t.stop();
                    t.start({
                        beginTime: () => mockCurrentTime.value,
                        finishTime: () => mockCurrentTime.value + 2000,
                    });
                }
            });

            timer.start({
                onTick,
                beginTime: () => mockCurrentTime.value,
                finishTime: () => mockCurrentTime.value + 1000,
            });

            mockCurrentTime.value += 500;
            await nextTick();

            expect(tickCount).toBeGreaterThan(0);
            expect(timer.status).toBe(TimerStatus.running);
        });

        it('应该处理多个计时器共享同一个回调函数的情况', async () => {
            const mockCurrentTime = ref(1000000);
            const timer1 = useTimer({ currentTime: mockCurrentTime });
            const timer2 = useTimer({ currentTime: mockCurrentTime });

            const sharedCallback = vi.fn();

            timer1.start({ onTick: sharedCallback });
            timer2.start({ onTick: sharedCallback });

            mockCurrentTime.value += 100;
            await nextTick();

            expect(sharedCallback).toHaveBeenCalledTimes(2);
        });
    });

    describe('状态转换边界情况', () => {
        it('应该处理在suspended状态下直接destroy的流程', () => {
            const mockCurrentTime = ref(1000000);
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({});
            timer.pause();
            expect(timer.status).toBe(TimerStatus.suspended);

            timer.destroy();
            expect(timer.status).toBe(TimerStatus.destroyed);
        });

        it('应该处理在回调执行期间状态被外部修改的情况', async () => {
            const mockCurrentTime = ref(1000000);
            const timer = useTimer({ currentTime: mockCurrentTime });

            const onTick = vi.fn(() => {
                // 在回调执行期间暂停计时器
                timer.pause();
            });

            timer.start({ onTick });

            mockCurrentTime.value += 100;
            await nextTick();

            expect(timer.status).toBe(TimerStatus.suspended);
        });
    });

    describe('极端数值和计算精度', () => {
        it('应该处理时间戳接近JavaScript最大安全整数的情况', async () => {
            const largeTimestamp = Number.MAX_SAFE_INTEGER - 1000;
            const mockCurrentTime = ref(largeTimestamp);
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => largeTimestamp,
                finishTime: () => largeTimestamp + 500,
            });

            mockCurrentTime.value = largeTimestamp + 600;
            await nextTick();

            expect(timer.status).toBe(TimerStatus.stopped);
        });

        it('应该处理小于1毫秒的时间间隔', async () => {
            const mockCurrentTime = ref(1000000.5);
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value,
                finishTime: () => mockCurrentTime.value + 0.1,
            });

            mockCurrentTime.value += 0.2;
            await nextTick();

            expect(timer.status).toBe(TimerStatus.stopped);
        });

        it('应该正确处理浮点数累积误差', async () => {
            const mockCurrentTime = ref(1000000);
            const timer = useTimer({ currentTime: mockCurrentTime });

            timer.start({
                beginTime: () => mockCurrentTime.value,
            });

            // 累积大量小的浮点数增量
            for (let i = 0; i < 100; i++) {
                mockCurrentTime.value += 0.33; // 一个会产生浮点误差的数字
            }
            await nextTick();

            expect(timer.elapsedTime.total).toBeCloseTo(33, 0);
        });
    });

    describe('复杂业务流程', () => {
        it('应该处理计时器链式依赖中的异常中断', async () => {
            const { timeCenter, createTimer } = createTimeManager({
                requestAnimationFrame: (cb) => setTimeout(() => cb(performance.now()), 0) as unknown as number,
                cancelAnimationFrame: clearTimeout,
            });
            const timerA = createTimer();
            const timerB = createTimer();
            const timerC = createTimer();

            const onFinishedB = vi.fn(() => {
                // 模拟在启动C时发生错误
                throw new Error('Chain error');
            });

            const onFinishedA = vi.fn(() => {
                timerB.start({
                    finishTime: () => timeCenter.currentTime.value + 1000,
                    onFinished: onFinishedB,
                });
            });

            const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

            timerA.start({
                finishTime: () => timeCenter.currentTime.value + 500,
                onFinished: onFinishedA,
            });

            await vi.advanceTimersByTimeAsync(600);
            await nextTick();

            expect(onFinishedA).toHaveBeenCalled();
            expect(timerB.status).toBe(TimerStatus.running);

            await vi.advanceTimersByTimeAsync(1200);
            await nextTick();

            expect(onFinishedB).toHaveBeenCalled();
            expect(consoleSpy).toHaveBeenCalledWith('useTimer: 回调函数执行错误:', expect.any(Error));
            expect(timerC.status).toBe(TimerStatus.idled);

            consoleSpy.mockRestore();
            timeCenter.stopUpdateTime();
        });

        it('应该处理时间服务异常时的降级策略', async () => {
            let syncFailCount = 0;
            const mockSyncTime = vi.fn().mockImplementation(() => {
                syncFailCount++;
                if (syncFailCount <= 3) {
                    return Promise.reject(new Error('Network error'));
                }
                return Promise.resolve(Date.now());
            });

            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

            const raf = vi.fn((callback: FrameRequestCallback) => {
                const id = setTimeout(() => callback(performance.now()), 16);
                return id as unknown as number;
            });
            const caf = vi.fn((id: number) => clearTimeout(id));

            const { timeCenter, createTimer } = createTimeManager({
                syncTime: mockSyncTime,
                syncTimeFrequency: 100,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            const timer = createTimer();
            timer.start({});

            // 触发多次同步失败
            for (let i = 0; i < 5; i++) {
                await vi.advanceTimersByTimeAsync(150);
                await nextTick();
            }

            expect(consoleSpy).toHaveBeenCalledTimes(3);
            expect(timer.status).toBe(TimerStatus.running);

            timeCenter.stopUpdateTime();
            consoleSpy.mockRestore();
        });
    });

    describe('内存和资源管理', () => {
        it('应该处理大量计时器同时到达finishTime的性能', async () => {
            const raf = vi.fn((callback: FrameRequestCallback) => {
                const id = setTimeout(() => callback(performance.now()), 16);
                return id as unknown as number;
            });
            const caf = vi.fn((id: number) => clearTimeout(id));
            const { timeCenter, createTimer } = createTimeManager({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });
            const timerCount = 50;
            const timers = Array.from({ length: timerCount }, () => createTimer());
            const finishCallbacks = Array.from({ length: timerCount }, () => vi.fn());
            const endTime = timeCenter.currentTime.value + 1000;

            // 同时启动所有计时器，都在相同时间结束
            timers.forEach((timer, index) => {
                timer.start({
                    finishTime: () => endTime,
                    onFinished: finishCallbacks[index],
                });
            });

            await vi.advanceTimersByTimeAsync(1200);
            await nextTick();
            await vi.runOnlyPendingTimersAsync();
            await nextTick();

            // 验证所有回调都被调用了
            finishCallbacks.forEach(callback => {
                expect(callback).toHaveBeenCalled();
            });

            timeCenter.stopUpdateTime();
        });

        it('应该在Vue组件销毁时完整清理计时器', async () => {
            const { createTimer } = createTimeManager();
            const timer = createTimer();
            const onTick = vi.fn();

            timer.start({ onTick });

            // 模拟组件销毁
            timer.destroy();

            // 尝试触发更新
            await vi.advanceTimersByTimeAsync(100);
            await nextTick();

            expect(onTick).not.toHaveBeenCalled();
            expect(timer.status).toBe(TimerStatus.destroyed);
        });

        it('销毁timeCenter后，计时器应停止工作', async () => {
            const { createTimer } = createTimeManager();
            const timer = createTimer();
            const onTick = vi.fn();

            timer.start({ onTick });

            // 模拟组件销毁
            timer.destroy();

            // 尝试触发更新
            await vi.advanceTimersByTimeAsync(100);
            await nextTick();

            expect(onTick).not.toHaveBeenCalled();
            expect(timer.status).toBe(TimerStatus.destroyed);
        });
    });
});
