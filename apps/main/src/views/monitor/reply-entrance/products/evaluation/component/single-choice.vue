<template>
    <div class="single-choice-container">
        <div class="single-choice-title">
            你所在的科技公司今年效益不佳，据分析，主要是领导将更多的精⼒放在了拓展市场上，导致现有业务承接与执⾏的投⼊不⾜，业务存在延期交付的情况，部分客户因此流失。你认为以下哪个⽅法是解决这个问题的关键?
        </div>
        <div class="options-wrap">
            <b-radio-group v-model="radioValue">
                <b-radio :value="1"> 集中精⼒做好现有业务，确保按时⾼质量交付提⾼客户满意度 </b-radio>
                <b-radio :value="2"> 加强市场拓展，增加业务量，提高公司收入 </b-radio>
                <b-radio :value="3"> 加强团队建设，提高员工素质，提升业务能力 </b-radio>
            </b-radio-group>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

defineOptions({
    name: 'SingleChoice',
});
defineEmits<{
    'update:modelValue': [value: boolean];
}>();
const radioValue = ref(1);
</script>

<style lang="less" scoped>
.single-choice-container {
    .single-choice-title {
        color: #141414;
        font-size: 16px;
        font-weight: 500;
        line-height: 24px; /* 1.5 */
        text-align: justify;
        margin-bottom: 20px;
    }
    .options-wrap {
        color: #292929;
        display: flex;
        flex-direction: column;
        row-gap: 12px;
    }
}
</style>
