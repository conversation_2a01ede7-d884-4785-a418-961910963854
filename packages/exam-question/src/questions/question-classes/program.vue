<template>
    <div :id="`question-${question.encryptQuestionId?.replace(/~/g, '')}`">
        <QuestionTitle
            :config="{
                number: number2Serial(index + 1, nested),
                title: question.title,
                score: question.score,
                imgList: question.attachments,
            }"
        />
        <div class="divide-line" />
        <div class="language-select-wrap">
            <b-select v-model="currentLanguage" class="language-select" @change="changeLanguage">
                <template #prefix> 编程语言： </template>
                <b-option v-for="item of EDITOR_LANGUAGES" :key="item.showLanguage" :value="item.editorLanguage" :label="item.showLanguage" />
            </b-select>
            <div v-if="languageStatus === LANGUAGE_STATUS.未选" class="language-select-tip">请选择编程语言</div>
        </div>
        <div class="editor-wrap">
            <MonacoEditor v-if="loaded" v-model="answer" :options="editorOptions" :max="MAX_EDITOR_LENGTH" />
            <div class="editor-count">
                <span :class="{ 'over-length': answer?.length >= MAX_EDITOR_LENGTH }">{{ answer?.length ?? 0 }}</span> / <span>{{ MAX_EDITOR_LENGTH }}</span>
            </div>
        </div>
        <div class="self-operate-wrap">
            <div style="margin-bottom: 10px">自测输入</div>
            <div class="operate-area">
                <b-textarea v-model="selfTest" placeholder="请输入" />
                <b-button type="primary" :disabled="executeStatus === EXECUTE_STATUS.运行中" @click="executeProgram"> 执行代码 </b-button>
            </div>
        </div>
        <div class="operate-result-wrap">
            <div class="status-wrap">
                <span class="status-title">执行状态：</span>
                <span
                    v-if="executeStatus >= 0"
                    class="tag"
                    :style="{ '--color-text': STATUS_CONFIG[executeStatus].colorText, '--color-background': STATUS_CONFIG[executeStatus].colorBackground }"
                >
                    <span v-if="executeStatus === EXECUTE_STATUS.运行中" class="icon-wrap" v-html="STATUS_CONFIG[EXECUTE_STATUS.运行中].iconConfig.content" />
                    <span>{{ STATUS_CONFIG[executeStatus].name }}</span>
                </span>
            </div>
            <div class="result-inner">
                <div class="result-title">代码执行结果</div>
                <div class="content-wrap">
                    <div class="content-inner in">
                        <div>我的输入</div>
                        <div class="content" :class="{ empty: !selfTestAfterExecute }">
                            {{ selfTestAfterExecute }}
                        </div>
                    </div>
                    <div class="content-inner out">
                        <div>我的输出</div>
                        <div class="content" :class="{ empty: !output }">
                            {{ output }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { editorOptionsType } from '../components/monaco-editor/type';
import type { Question } from '../types';
import { debounce } from 'es-toolkit';

import { inject, onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { number2Serial } from '../../utils/methods';
import { EDITOR_LANGUAGES, EXECUTE_STATUS, LANGUAGE_STATUS, MAX_EDITOR_LENGTH, STATUS_CONFIG } from '../components/monaco-editor/constants';
import MonacoEditor from '../components/monaco-editor/index.vue';
import QuestionTitle from '../components/question-title.vue';

import { useQuestionInject } from './hook/use-question';
import { previewData } from './program-previw';

defineOptions({
    name: 'Program',
});
const question = defineModel<Question>({
    required: true,
});
const props = defineProps<{
    index: number;
    nested: boolean;
}>();

const emit = defineEmits<{
    answerSave: [params: any];
}>();

const mode = inject<'preview' | 'exam'>('mode');

const questionConfig = useQuestionInject();

const route = useRoute();
const languageStatus = ref<LANGUAGE_STATUS>(LANGUAGE_STATUS.初始);
const currentLanguage = ref();
const editorOptions = ref<editorOptionsType>({
    language: currentLanguage.value,
    minimap: { enabled: false },
    hideCursorInOverviewRuler: true,
    contextmenu: false,
    stickyScroll: { enabled: false },
});
function changeLanguage(value: string) {
    editorOptions.value.language = value;
    setLanguage();
    languageStatus.value = LANGUAGE_STATUS.已选;
}

const selfTest = ref('');
const executeStatus = ref(EXECUTE_STATUS.未运行);
const selfTestAfterExecute = ref('');
const output = ref('');

/**
 * 执行代码
 */
async function executeProgram() {
    if (mode === 'preview') {
        previewData.executeProgramPreview({
            executeStatus,
            output,
            selfTest,
            selfTestAfterExecute,
        });
        return;
    }
    if ([LANGUAGE_STATUS.初始, LANGUAGE_STATUS.未选].includes(languageStatus.value)) {
        languageStatus.value = LANGUAGE_STATUS.未选;
        return;
    }
    if (executeStatus.value === EXECUTE_STATUS.运行中) {
        return;
    }
    executeStatus.value = EXECUTE_STATUS.运行中;
    output.value = '';
    selfTestAfterExecute.value = '';
    const params = {
        code: answer.value,
        language: EDITOR_LANGUAGES.find((x) => x.editorLanguage === currentLanguage.value)?.serverLanguage,
        input: selfTest.value,
        questionId: question.value.encryptQuestionId,
    };
    try {
        const res = await questionConfig.program?._programExecute(params);
        if (res.code === 0) {
            executeStatus.value = res.data.result;
            // 这四种情况下，不展示运行结果
            if (![EXECUTE_STATUS.时间超限, EXECUTE_STATUS.内存超限, EXECUTE_STATUS.输出超限, EXECUTE_STATUS.系统错误].includes(executeStatus.value)) {
                output.value = res.data.output;
            }
        } else if (res.code === 1) {
            // 接口报系统服务错误时，执行结果显示系统错误。
            executeStatus.value = EXECUTE_STATUS.系统错误;
        } else {
            executeStatus.value = EXECUTE_STATUS.未运行;
        }
        // 编程题执行结果埋点
        questionConfig.BossAnalyticsTrack('zhice-pc-exam-program-execute', {
            pData: {
                type: questionConfig.TrackTypeEnum.成功,
                message: res.data?.result,
                nameZh: '编程题执行结果',
            },
        });
    } catch (error: any) {
        executeStatus.value = EXECUTE_STATUS.未运行;
        // 编程题执行请求失败埋点
        questionConfig.BossAnalyticsTrack('zhice-pc-exam-program-execute', {
            pData: {
                type: questionConfig.TrackTypeEnum.失败,
                message: error.message || error.name,
                nameZh: '编程题执行请求失败',
            },
        });
    } finally {
        selfTestAfterExecute.value = selfTest.value;
    }
}

let timer = -1;
async function getStatus() {
    const params = {
        questionId: question.value.encryptQuestionId,
    };
    try {
        const res = await questionConfig.program?._programExecuteStatus(params);
        if (res.code === 0) {
            executeStatus.value = res.data.result;
            if (executeStatus.value === EXECUTE_STATUS.运行中) {
                pollingStatus();
            } else {
                cancelPollingStatus();
            }
        } else {
            pollingStatus();
        }
    } catch (error) {
        pollingStatus();
    }
}
function pollingStatus() {
    timer = window.setTimeout(() => {
        getStatus();
    }, 2000);
}
function cancelPollingStatus() {
    window.clearTimeout(timer);
    timer = -1;
}
async function setLanguage() {
    if (mode === 'preview') {
        return;
    }
    const params = {
        questionId: question.value.encryptQuestionId,
        language: EDITOR_LANGUAGES.find((x) => x.editorLanguage === currentLanguage.value)?.serverLanguage,
    };
    try {
        const res = await questionConfig.program?._programSetLanguage(params);
        if (res.code === 0) {
            // do something
        }
    } catch (error) {
        // console.log('%c [ error ]-166', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
}
async function getLanguage() {
    if (mode === 'preview') {
        previewData.getLanguagePreview({
            currentLanguage,
            editorOptions,
            languageStatus,
        });
        return;
    }
    const params = {
        questionId: question.value.encryptQuestionId,
    };
    try {
        const res = await questionConfig.program?._programGetLanguage(params);
        if (res.code === 0) {
            if (res.data) {
                currentLanguage.value = EDITOR_LANGUAGES.find((x) => x.serverLanguage === res.data)?.editorLanguage;
                editorOptions.value.language = currentLanguage.value;
                languageStatus.value = LANGUAGE_STATUS.已选;
            }
        }
    } catch (error) {
        // console.log('%c [ error ]-166', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
}

const answer = ref();
const saveAnswer = debounce(async () => {
    const params = {
        encryptExamId: route.params.examId,
        encryptId: question.value.encryptId,
        answerList: [
            {
                encryptQuestionId: question.value.encryptQuestionId,
                contentList: [answer.value],
                rank: question.value.rank,
                answerAttachments: [],
            },
        ],
    };
    emit('answerSave', params);
}, 500);

const loaded = ref(false);
onMounted(async () => {
    answer.value = question.value.answerContentList?.[0] || '';

    watch(answer, (val) => {
        question.value.answer = val;
        if (val && val.trim() !== '') {
            question.value.answerStatus = 1;
        } else {
            question.value.answerStatus = 0;
        }
        saveAnswer();
    });

    executeStatus.value = mode === 'preview' ? EXECUTE_STATUS.已完成 : (question.value.codeQuestionInfo?.result as EXECUTE_STATUS);
    if (executeStatus.value > 0) {
        pollingStatus();
    }
    await getLanguage();

    loaded.value = true;
});
</script>

<style lang="less" scoped>
.choices-wrap {
    color: #2d2d2d;
    font-size: 13px;
}
.divide-line {
    border-top: 1px solid #ececec;
    margin: 20px 0;
}
.language-select-wrap {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    :deep(.language-select) {
        width: 294px;
        margin-right: 12px;
        .b-select-view-prefix {
            color: #1f1f1f;
        }
    }
    .language-select-tip {
        color: #c7152b;
        font-size: 13px;
    }
}
.editor-wrap {
    position: relative;
    :deep(.monaco-editor-container) {
        .monaco-editor,
        .monaco-diff-editor,
        .monaco-component {
            --vscode-editorGutter-background: #f7f7f7;
        }

        height: 300px;
        border: 1px solid #d3d8e6;
        border-radius: 8px;
        overflow: hidden;
        .monaco-editor {
            border-radius: 8px;
            .overflow-guard {
                border-radius: 8px;
            }
        }
    }
    .editor-count {
        position: absolute;
        right: 0;
        bottom: -20px;
        color: #5d7080;
        font-size: 13px;
        .over-length {
            color: #c7152b;
        }
    }
}
.self-operate-wrap {
    margin-top: 10px;
    color: #5d7080;
    font-size: 13px;
    line-height: 18px;

    .operate-area {
        display: flex;
        align-items: flex-start;
        .b-button {
            width: 144px;
            flex-shrink: 0;
            margin-left: 26px;
        }
    }
}
.operate-result-wrap {
    margin-top: 20px;
    .status-wrap {
        color: #1f1f1f;
        font-size: 14px;
        line-height: 20px;
        margin-bottom: 10px;
        height: 24px;
        display: flex;
        align-items: center;
        .status-title {
            font-weight: 500;
        }
        .tag {
            color: var(--color-text);
            background-color: var(--color-background);
            font-weight: 400;
            padding: 2px 8px;
            display: flex;
            align-items: center;
            border-radius: 2px;
            .icon-wrap {
                display: inline-flex;
                margin-right: 4px;
                :deep(svg) {
                    animation: runRotate 3s linear infinite;
                    @keyframes runRotate {
                        0% {
                            transform: rotate(0deg);
                        }
                        100% {
                            transform: rotate(360deg);
                        }
                    }
                }
            }
        }
    }
    .result-inner {
        border: 1px solid #d3d8e6;
        border-radius: 8px;
        overflow: hidden;
        .result-title {
            background-color: #f7f7f7;
            color: #1f1f1f;
            font-size: 14px;
            line-height: 20px;
            padding: 10px 0;
            display: flex;
            align-items: center;

            &::before {
                content: '';
                width: 4px;
                height: 12px;
                background: #12ada9;
                border-radius: 0px 2px 2px 0px;
                margin-right: 11px;
            }
        }
        .content-wrap {
            padding: 16px 15px 15px;
            .content-inner {
                & + .content-inner {
                    margin-top: 16px;
                }
                color: #5d7080;
                font-size: 13px;
                line-height: 18px;
                .content {
                    margin-top: 8px;
                    padding: 8px 12px;
                    white-space: pre-wrap;
                    word-break: break-all;
                    background: #f7f7f7;
                    border-radius: 4px;
                    color: #1f1f1f;
                    font-size: 13px;
                    line-height: 18px;
                    max-height: 160px;
                    overflow: auto;
                    &.empty {
                        height: 32px;
                    }
                }
            }
        }
    }
}
</style>
