<template>
    <span style="white-space: nowrap">{{ data?.num && typeof data.num === 'number' ? formatNumber(data?.num) : 0 }}</span>
</template>

<script setup lang="ts">
import gsap from 'gsap';
import { reactive, watch } from 'vue';

interface IProps {
    value?: number;
    reservedDecimalPlaces?: number;
}

const props = withDefaults(defineProps<IProps>(), {
    value: 0,
    reservedDecimalPlaces: 0,
});
const emit = defineEmits<{
    changeNumber: [val: number];
}>();
interface IProps {
    value?: number;
    reservedDecimalPlaces?: number;
}
function formatNumber(number: number) {
    return number.toFixed(props.reservedDecimalPlaces || 0);
    // if (props.reservedDecimalPlaces !== 0) {
    //     return number.toFixed(props.reservedDecimalPlaces)
    // } else if (number % 1 === 0) {
    //     return number.toFixed(props.reservedDecimalPlaces)
    // } else {
    //     return number.toFixed(props.reservedDecimalPlaces)
    // }
}

const data = reactive({
    num: 0,
});

function numA() {
    gsap.to(data, {
        duration: 0.3,
        num: props.value,
    });
}
numA();
watch(
    () => props.value,
    () => {
        numA();
    },
);
watch(
    () => data.num,
    () => {
        emit('changeNumber', data.num);
    },
);
</script>
