import { cloneDeep } from 'es-toolkit';
import { inject, provide, ref, toValue } from 'vue';

interface QuestionConfigType {
    BossAnalyticsTrack?: any;
    TrackTypeEnum?: any;
    program?: {
        _programExecute: (...arg: any) => Promise<any>;
        _programExecuteStatus: (...arg: any) => Promise<any>;
        _programSetLanguage: (...arg: any) => Promise<any>;
        _programGetLanguage: (...arg: any) => Promise<any>;
    };
    showToast: any;
    onFileUpload: any;
    onFileDownload: any;
}

const KEY = 'questionConfig';

export function useQuestion(props: QuestionConfigType) {
    provide(KEY, cloneDeep(props));

    return props;
}

export function useQuestionInject() {
    const question = inject<QuestionConfigType>('questionConfig')!;

    return question;
}
