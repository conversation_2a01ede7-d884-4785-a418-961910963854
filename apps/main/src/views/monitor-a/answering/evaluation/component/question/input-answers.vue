<template>
    <QuestionTitle :questionInfo="questionInfo" />
    <div class="input-wrap">
        <b-textarea v-model="answerContentVal" :placeholder="placeholder" autosize :showWordLimit="true" :maxLength="300" />
        <!-- <VanField  class="input-content" :placeholder="placeholder"   type="textarea" maxlength="300" /> -->
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import QuestionTitle from './component/question-title.vue';

const props = defineProps({
    questionInfo: {
        type: Object,
        default: () => ({}),
    },
    answer: {
        type: [String, Array],
        default: () => '',
    },
});

const answerContentVal = ref<any>('');
const placeholder = ref('');

watch(
    () => props.questionInfo,
    async (val: any) => {
        const { required } = val;
        answerContentVal.value = props.answer || '';
        placeholder.value = required ? '请输入' : '选填';
    },
    {
        deep: true,
        immediate: true,
    },
);

function checkAnswer() {
    const { required, encryptId, questionType } = props.questionInfo;
    let flag = true;
    let message = '';
    const val = answerContentVal.value;

    if (required) {
        if (!val) {
            flag = false;
            message = '请输入您的回答';
        }
    }

    const params = {
        evaPaperSnapshotId: encryptId,
        questionType,
        answerContent: val,
    };

    return [flag, message, params];
}

defineExpose({
    check: checkAnswer,
});
</script>

<style lang="less" scoped>
.input-wrap {
    :deep(.b-textarea-wrapper) {
        .b-textarea {
            height: 177px;
        }

        .b-textarea-word-limit {
            .b-typing {
                color: #b3b3b3;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px; /* 1.571 */
                text-align: right;
                &.b-typing-secondary {
                    color: #00a6a7;
                }
            }
        }
    }
}
</style>
