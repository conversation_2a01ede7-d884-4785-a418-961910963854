<template>
    <div v-if="hasInit" class="page-wrap-monitor">
        <component :is="stageComponent" />
        <!-- mqtt沟通入口 -->
        <DragCell v-if="hasChat">
            <div class="chat-entrance">
                <div class="chat-entrance-trigger" @click="toggleChat(!showChat, 'entrance')"><SvgIcon name="svg-empty-kefu" width="24" height="24" /> 监考官</div>
                <Transition name="chat-fade">
                    <Chat v-show="showChat" :showChat="showChat" @toggle="toggleChat" />
                </Transition>
            </div>
        </DragCell>
    </div>
    <b-pagetip v-else :status="isLoading" loadingType="dot">
        <template #error-content>
            <b-space direction="vertical">
                <p class="b-pagetip-text">数据加载失败，请点击按钮重试！</p>
                <b-button type="primary" style="margin: 0 auto" @click.prevent="retry"> 重试 </b-button>
            </b-space>
        </template>
    </b-pagetip>
</template>

<script lang="ts" setup>
import { RequestStatus, useGlobalInit } from '@/hooks/useGlobalInit';
import Chat from '@/views/monitor/components/consist/chat.vue';
import PrepareDone from '@/views/monitor/components/prepare-done/index.vue';
import Detection from '@/views/monitor/detection-entrance/index.vue';
import Reply from '@/views/monitor/reply-entrance/index.vue';
import { DragCell } from '@crm/exam-components';
import { onBeforeUnmount } from 'vue';
import { useTrackableFlow } from '@/store/track/use-trackable-flow';
import { useChat } from './hooks/useChat';
import { useBodyDetection } from './hooks/useBodyDetection';
import { useMonitorStep } from './hooks/useMonitorStep';
import { MonitorStage } from './config';

const stageComponents: any = {
    [MonitorStage.Reply]: Reply, // 正式考试答题阶段
    [MonitorStage.PrepareDone]: PrepareDone, // 考前准备阶段完成
    default: Detection, // 设备调试阶段
};

const { examInfoRequestStatus, fetchSeqOrExamInfo, examExit, retry } = useGlobalInit();

// Initialize useChat here to get all its functions, including initMqtt
const { hasChat, showChat, toggleChat, initMqtt, removeMqtt, receiveMessageEmitter, removeReceiveMessageEmitter } = useChat();

// Initialize other composables
const { initBodyDetect } = useBodyDetection();
const { initCurrentStep: initializeCurrentStep, step, hasInitStep, clearStep } = useMonitorStep();

const stageComponent = computed(() => stageComponents[step.value] || stageComponents.default);
const hasInit = computed(() => hasInitStep.value && examInfoRequestStatus.value === RequestStatus.SUCCESS);
const isLoading = computed(() => (hasInit.value ? 'loading' : examInfoRequestStatus.value === RequestStatus.FAILURE ? 'error' : 'loading'));

const { execute: executeInit } = useTrackableFlow('考试基础信息以及功能初始化流程', async (ctx) => {
    if (await fetchSeqOrExamInfo()) {
        ctx.logStep('重定向，考试基础信息以及功能初始化流程结束，跳转至考试列表');
        return;
    }

    await initializeCurrentStep(examInfoRequestStatus);
    ctx.logStep('初始化当前步骤完成');

    await initMqtt();
    ctx.logStep('MQTT 初始化流程已调用');

    await initBodyDetect();
    ctx.logStep('人体检测功能初始化流程已调用');

    receiveMessageEmitter();
    ctx.logStep('MQTT消息接收器初始化完成');
});

const { execute: executeExit } = useTrackableFlow('考试退出流程', async (ctx) => {
    await removeMqtt();
    ctx.logStep('MQTT 清理流程已调用');

    examExit();
    ctx.logStep('考试退出');

    removeReceiveMessageEmitter();
    ctx.logStep('MQTT消息接收器断开连接');
});

executeInit();

onBeforeUnmount(() => {
    executeExit();
    clearStep();
});
</script>

<style lang="less">
@import './index.less';
</style>
