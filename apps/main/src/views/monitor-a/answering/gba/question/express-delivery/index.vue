<template>
    <div class="round-content">
        <div class="title-wrap">
            <p class="title">{{ numberToChinese(gbaStore.currentIndex + 1, false) }}、{{ gbaStore.currentQuestion.showName }}</p>
            <div class="progress-wrap">
                <p class="round-info-wrap">
                    回合
                    <span>{{ roundPassed }}</span>
                </p>
                <span class="stage-boll-icon" :style="{ backgroundColor: currentRoundStageConfig.color }" />
                <p class="stage-info" :style="{ color: currentRoundStageConfig.color }">
                    <span>{{ currentRoundStageConfig.text }}</span>
                </p>
            </div>
            <div class="gold-wrap">
                <p class="gold-icon" />
                <p class="text">金币:</p>
                <div class="number-wrap">
                    <NumberVue :value="goldNumber.num" :reservedDecimalPlaces="1" />
                </div>
            </div>
        </div>
        <div class="cabinet-wrap">
            <div
                v-for="(item, index) in cabinet"
                :key="item.key"
                class="cabinet-item"
                :class="{
                    select: item.isShow && currentRoundData.isChoose,
                    fake: item.isShow && item.showType === 'fake',
                    real: item.isShow && item.showType === 'real',
                    err: item.isShow && item.showType === 'err',
                }"
                :style="
                    item.isShow && item.showType !== 'err'
                        ? {
                              backgroundImage: `url(${item.showType === 'real' ? assetsImgCongig.real : assetsImgCongig.fake})`,
                          }
                        : {}
                "
                @click="onSelectCabinet(item)"
            >
                <!-- {{ item.isShow }} -->
            </div>
        </div>
        <div v-if="currentRoundData.isChoose" class="countdown-info-wrap">倒计时 {{ Math.floor(roundCountdown?.remainingTime.total / 1000) }} S</div>
    </div>
</template>

<script setup lang="ts">
import { createTimer, timeCenter } from '@/store/time';
import { debounce, maxBy, sampleSize, shuffle, sum } from 'lodash-es';
import { computed, onMounted, reactive, ref } from 'vue';
import NumberVue from '../../component/animation-number.vue';
import { useNotification } from '../../component/notification-tips/index';
import { useGbaStore } from '../../store';
import { defineCabinetList, defineRoundData, roundStageMap } from './const';

import { numberToChinese } from '@/utils/number-to-chinese';

const props = defineProps({
    isSimulate: {
        type: Boolean,
        default: false,
    },
});
const emits = defineEmits(['tips', 'roundOver', 'gameOver', 'roundChange']);
const useNotificationTips = useNotification();
const gbaStore = useGbaStore();

const roundList = computed(() => {
    if (props.isSimulate) {
        // 模拟模式下 使用模拟数据
        return gbaStore.currentQuestion.simulateList;
    } else {
        return gbaStore.currentQuestion.roundList;
    }
});

const cabinet = ref(defineCabinetList()); // 快递柜

// 当前难度下的数据
const currentRoundData = ref(defineRoundData());

// 已处理回合数
const roundPassed = computed(() => {
    const number = roundList.value.filter((item) => item.answerStatus === 1).length;
    return number + roundPassedCompletion.value;
});
// 展示补齐  如果回合结束 则不需要补齐
const roundPassedCompletion = ref(1);

// 当前回合下阶段配置
const currentRoundStageConfig = computed(() => {
    const { roundStage } = currentRoundData.value;
    return roundStageMap[roundStage];
});

// 当前真假盒子的配图
const assetsImgCongig = computed(() => {
    let real = 'https://img.bosszhipin.com/static/file/2024/scios4zmg21735121450086.png.webp';
    let fake = 'https://img.bosszhipin.com/static/file/2024/xf9kqg1xkr1735121449814.png.webp';
    const { assetsCongig } = gbaStore.currentQuestion;

    // 如果服务配置 快递图片
    if (assetsCongig && assetsCongig.length) {
        real = assetsCongig.find((item) => item.paramId === 1)?.paramImg || '';
        fake = assetsCongig.find((item) => item.paramId === 2)?.paramImg || '';
    }

    return {
        real,
        fake,
    };
});

const goldNumber = reactive({
    num: gbaStore.currentQuestion.reward,
});

const { currentTime } = timeCenter;
const roundTime = gbaStore.currentQuestion?.repeatTime ?? 60 * 1000;

// 是否选择 时间控制
const roundCountdown = createTimer();

// 作答超时提示
function roundTimeout() {
    currentRoundData.value.timeoutTs = currentTime.value || 0;
    currentRoundData.value.errNum += 1;

    useNotificationTips.open({
        type: 'tips-message',
        time: 1500,
        text: '作答超时',
        onClose: () => {
            openNextStageTips(true);
        },
    });
}

// 回合结束
function openNextStageTips(isTimeout: boolean = false) {
    const { result, profit, showList, selectList, encSnapshotId, errNum, difficultyStage, timeoutTs, repeatTimeTs, showTimeTs } = currentRoundData.value;

    emits('roundOver', {
        encSnapshotId,
        result,
        profit,
        errNum,
        timeoutTs,
        showTimeTs,
        repeatTimeTs,
        repeatDetail: {
            appearDetail: showList.map((item) => ({
                index: item.index,
                packageType: item.showType === 'real' ? 0 : 1,
            })),
            chooseTimeTs: selectList.map((item) => item.chooseTimeTs),
            chooseIndex: selectList.map((item) => item.index),
        },
    });

    // 当前难度下错误，并且没有满足最大错误尝试次数 直接重新尝试
    if (result === 0 && errNum <= 2) {
        openMemoryStage();
        return;
    }

    // 最大难度
    const maxDifficulty = maxBy(roundList.value, 'roundSnapshot.stage')?.roundSnapshot.stage || 0;
    let upgrade: 'end' | 'jump' | 'next' = 'next';
    let statusText = result === 1 ? '复现成功' : '复现失败';
    let tipsTime = 3500;

    // 下一回合数据
    const nextRound = roundList.value[getNextIndex()];

    // 有没有更高难度的题
    const hasHigherDifficulty = difficultyStage < maxDifficulty;
    // 回答错误并且是最大难度下
    const isErrAndMaxStage = result === 0 && !hasHigherDifficulty;

    if (!nextRound || isErrAndMaxStage) {
        // 最后一题 或 回答错误并且是最大难度下
        upgrade = 'end';
        tipsTime = 1500;
    } else if (nextRound?.roundSnapshot.stage > difficultyStage) {
        upgrade = 'jump';
        statusText = `${statusText}，下一轮难度升级`;
    } else {
        upgrade = 'next';
        statusText = `${statusText}，恭喜进入下一轮`;
    }

    // 当前回合得分
    const scoreNumber = result === 1 ? profit : 0;

    if (isTimeout) {
        if (scoreNumber) {
            goldNumber.num += scoreNumber;
        }

        if (upgrade === 'end') {
            gameOver();
        } else {
            openMemoryStage();
        }
    } else {
        useNotificationTips.open({
            type: 'template-message',
            action: 'delivery-stage-end-view',
            text: statusText,
            number: scoreNumber,
            time: tipsTime,
            onClose: () => {
                if (scoreNumber) {
                    goldNumber.num += scoreNumber;
                }

                if (upgrade === 'end') {
                    gameOver();
                } else {
                    openMemoryStage();
                }
            },
        });
    }
}

// 选择
const onSelectCabinet = debounce((data) => {
    const currentData = {
        ...data,
        chooseTimeTs: currentTime.value,
    };

    const { selectList, showList, isChoose } = currentRoundData.value;
    const newSelectList = [...selectList];
    const cabinetItemData = cabinet.value[currentData.index] ?? {};

    // 正确答案
    const realList = showList.filter((item) => item.showType === 'real');

    let isEnd = false;

    // 非选择时间 禁止选择
    if (!isChoose || cabinetItemData.isShow === true) {
        return;
    }
    newSelectList.push(currentData);
    // 需要对比的数据;
    const correctResultData = realList[newSelectList.length - 1];

    if (correctResultData && correctResultData.key === currentData.key) {
        // 进入下一难度
        if (newSelectList.length && newSelectList.length === realList.length) {
            isEnd = true;
            currentRoundData.value.result = 1; // 标记当前回合选择正确
        }
    } else {
        isEnd = true;
        currentRoundData.value.result = 0; // 标记当前回合选择错误
        cabinetItemData.showType = 'err';
        currentRoundData.value.errNum += 1;
    }

    cabinetItemData.isShow = true;
    cabinet.value[data.index] = cabinetItemData;
    currentRoundData.value.selectList = newSelectList;

    if (isEnd === true) {
        // 停止回合时间
        roundCountdown?.stop();

        setTimeout(() => {
            openNextStageTips();
        }, 300);
    }
}, 300);

// 依次展示快递盒子
let previousCabinetIndexIndex = 0;
function showRounds() {
    const { showList } = currentRoundData.value;
    recursiveTimeout(500, showList.length, (type) => {
        cabinet.value[previousCabinetIndexIndex].isShow = false;

        if (type === 'end') {
            previousCabinetIndexIndex = 0;
            openReproductionStage();
            return;
        }
        const data = showList[type];

        if (data) {
            cabinet.value[data.index] = {
                ...data,
                isShow: true,
            };
            previousCabinetIndexIndex = data.index;
        }
    });
}
// 递归处理 依次展示盒子
function recursiveTimeout(delay, maxCount, callback, currentCount = 0) {
    if (currentCount > maxCount) {
        callback('end');
        return;
    }

    setTimeout(() => {
        callback(currentCount); // 执行回调，传入当前计数
        recursiveTimeout(1000, maxCount, callback, currentCount + 1); // 下一次递归
    }, delay);
}

// 生成并记录数据
function initCurrentRoundData() {
    const roundIndex = getNextIndex();
    const config = roundList.value[roundIndex];

    if (!config) {
        return;
    }

    const { errNum, encryptId, roundSnapshot } = config;
    const { score, stage, memoryCount, disturbCount } = roundSnapshot;

    let fakeList = []; // 假盒子数量
    let realList = []; // 真盒子数量
    let showList = []; // 需要依次展示的盒子数量

    // 初始化快递柜
    const cabinetList = defineCabinetList();

    realList = sampleSize(cabinetList, memoryCount);

    realList.forEach((item) => {
        item.showType = 'real';
    });

    // 设置假盒子
    if (disturbCount) {
        // 获取除去真盒子外的柜子列表
        const remainingBoxList = cabinetList.filter((item) => !realList.some((showItem) => showItem.key === item.key));
        fakeList = sampleSize(remainingBoxList, disturbCount);
        fakeList.forEach((item) => {
            item.showType = 'fake';
        });
    }

    // 真盒子数据拆分，保留最后一个真盒子
    const lastReal = { ...realList[realList.length - 1] };
    const newRealList = realList.slice(0, realList.length - 1);

    // 打乱顺序
    showList = shuffle(fakeList.concat(newRealList));

    // 确保最后一个盒子为真盒子
    showList.push(lastReal);

    currentRoundData.value = {
        ...defineRoundData(),
        encSnapshotId: encryptId,
        selectList: [],
        showList, // 需要展示的盒子
        roundStage: 1, // 当前阶段
        difficultyStage: stage,
        isChoose: false, // 是否可以作答
        errNum, // 当前回合下 用户错误的次数统计;
        profit: score,
        showTimeTs: currentTime.value,
    };

    cabinet.value = cabinetList; // 重置快递柜
}

// 打开复现阶段
function openReproductionStage() {
    currentRoundData.value.roundStage = 2;

    useNotificationTips.open({
        type: 'tips-message',
        time: 1500,
        text: '请复现顺序与位置',
        onClose: () => {
            // 可以回答  开启倒计时
            roundCountdown?.start({
                key: 'roundCountdown',
                finishTime: (t) => t + roundTime,
                onFinished: () => {
                    roundTimeout(); // 超时结束
                },
            });

            currentRoundData.value = {
                ...currentRoundData.value,
                isChoose: true,
                repeatTimeTs: currentTime.value,
            };
        },
    });
}

// 打开记忆阶段
function openMemoryStage() {
    initCurrentRoundData();

    useNotificationTips.open({
        type: 'tips-message',
        time: 1500,
        text: '记忆阶段',
        onClose: () => {
            showRounds();
        },
    });
}

function getNextIndex() {
    return roundList.value.findIndex((item) => item.answerStatus === 0 || item.answerStatus === 2);
}

// 所有回合结束 游戏结束
function gameOver() {
    roundPassedCompletion.value = 0;

    // 模拟模式下不需要走游戏结束弹窗
    if (!props.isSimulate) {
        useNotificationTips.open({
            type: 'template-message',
            action: 'mine-view',
            reservedDecimalPlaces: 1,
            number: goldNumber.num ? goldNumber.num?.toFixed(1) : goldNumber.num,
            onNext: () => {
                emits('gameOver');
            },
        });
    } else {
        emits('gameOver');
    }
}

function init() {
    const roundIndex = getNextIndex();

    // 计算已答回合金额
    const number = sum(
        roundList.value.map((item) => {
            if (item.answerStatus === 1 && item.reward) {
                return item.reward;
            }
        }),
    );

    goldNumber.num = number || 0;

    if (roundIndex < 0) {
        gameOver();
        return;
    }

    useNotificationTips.open({
        type: 'countdown-start-message',
        time: 3500,
        onClose: () => {
            openMemoryStage();
        },
    });
}

onMounted(() => {
    init();
});
</script>

<style lang="less" scoped>
.boll {
    position: fixed;
    width: 10px;
    height: 10px;
    border-radius: 10px;
    background: red;
    z-index: 9999;
}

.round-content {
    position: relative;
    padding: 20px 32px 30px;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: linear-gradient(179.99998deg, #ffd190 1%, #ffedbe 47%, #ffb95c 99%);
    box-shadow: inset 0px 4px 35px 0px rgba(207, 113, 0, 0.4);
    backdrop-filter: blur(18.5px);

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        height: 80px;
        width: 100%;
        border: 1px solid red;
        background: linear-gradient(179.99998deg, rgba(227, 255, 250, 0.4) 0%, rgba(235, 255, 247, 0.4) 100%);
        filter: blur(25px);
    }

    &::after {
        content: '';
        position: absolute;
        left: 0;
        height: 261px;
        right: 0;
        bottom: -5px;
        background-image: url('https://img.bosszhipin.com/static/file/2024/h6xy4k3ehq1725606964354.png.webp');
        background-size: 100% auto;
        z-index: -1;
    }

    .title-wrap {
        position: relative;
        height: 38px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
            // position: absolute;
            top: 50%;
            // transform: translateY(-50%);
            color: #000000;
            font-size: 24px;
            font-style: normal;
            font-weight: 600;
            line-height: 24px;
        }

        .progress-wrap {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translateX(-50%) translateY(-50%);
            text-align: center;
            display: flex;
            align-items: center;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;

            .round-info-wrap {
                padding: 4px 16px;
                color: #000000;

                background: #ffeec8;
                border-radius: 20px;

                & > span {
                    margin-left: 1px;
                    color: #de4600;
                }
            }

            .stage-boll-icon {
                margin-left: 8px;
                margin-right: 6px;
                width: 5px;
                height: 5px;
                border-radius: 5px;
            }

            .stage-info {
                color: #389900;
                font-weight: 500;
            }
        }

        .gold-wrap {
            display: flex;
            align-items: center;
            padding: 4px;
            padding-right: 12px;
            height: 38px;
            background: linear-gradient(179.99998deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 238, 203, 0.5) 100%);
            border: 1px solid rgba(255, 255, 255, 0.8);
            border-radius: 29.53px;

            color: #2d2d2d;
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 20px;
            text-align: right;

            .gold-icon {
                // position: absolute;
                // left: -9px;
                width: 32px;
                height: 28px;
                background-image: url(https://img.bosszhipin.com/static/file/2024/mn0equr2ru1725349213782.png.webp);
                background-position: center;
                background-size: 58px;
                z-index: 4;
            }

            .text {
                margin-left: 5px;
                color: #2d2d2d;
            }

            .number-wrap {
                width: 58px;
                text-align: right;
            }
        }
    }

    .cabinet-wrap {
        position: relative;
        z-index: 2;
        margin: 5px auto;
        // padding: 0 23px;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        width: 500px;

        .cabinet-item {
            margin-right: 32px;
            width: 60px;
            height: 60px;
            background-color: #ffffff;
            background-repeat: no-repeat;
            background-position: center;
            border: 3px solid #ffd3ab;
            border-radius: 12px;

            &:nth-child(n + 6) {
                margin-top: 10px;
            }

            &:nth-child(5n) {
                margin-right: 0px;
            }

            &.fake {
                background-image: url('https://img.bosszhipin.com/static/file/2024/xf9kqg1xkr1735121449814.png.webp');
                background-size: 100% auto;
            }

            &.real {
                background-image: url('https://img.bosszhipin.com/static/file/2024/scios4zmg21735121450086.png.webp');
                background-size: 100% auto;
            }

            &.err {
                border: 3px solid #ff7c40 !important;
                background-image: url('https://img.bosszhipin.com/static/file/2024/vo55glfmdt1735121449429.png.webp');
                background-size: 34px 33px;
            }

            // 选择状态
            &.select {
                border: 3px solid #389900;
            }
        }
    }

    .countdown-info-wrap {
        position: absolute;
        bottom: -60px;
        left: 50%;
        transform: translateX(-50%);
        width: 150px;
        height: 38px;
        background-image: url(https://img.bosszhipin.com/static/file/2024/2gwkqypex21725348290417.png.webp);
        background-size: 100% 100%;
        text-align: center;
        color: #fff9dc;
        font-size: 18px;
        font-style: normal;
        font-weight: 600;
        text-shadow: 0 0.87px 0.87px rgba(218, 79, 12, 0.7);
        line-height: 42px;
        animation: markedTagShow 0.3s forwards;
    }
}

@keyframes rotateIn {
    from {
        transform: rotateY(-180deg);
        opacity: 0;
    }
    to {
        transform: rotateY(0deg);
        opacity: 1;
    }
}

@keyframes markedTagShow {
    from {
        bottom: -60px;
    }
    to {
        bottom: -1px;
    }
}
</style>
