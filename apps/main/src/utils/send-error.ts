import { postForm } from '@/services/http';
import { useMonitorStore } from '@/store/use-monitor-store';
import { VIRTUAL_DEVICE_LABLE_LIST } from '@crm/exam-constants';

/**
 * 考生异常行为记录(用于监考侧展示)
 *
 * errType 枚举
    ERR_SUCC_COMMON_OK(1, "正常", ""),

    ERR_CAMERA_ABSENCE(101, "无摄像头", ""),
    ERR_CAMERA_OCCUPY(102, "摄像头被占用", ""),
    ERR_CAMERA_FORBIDDEN(103, "摄像头被禁用", ""),
    ERR_CAMERA_VIRTUAL(104, "考生使用虚拟摄像头", ""),

    ERR_ACTION_CLOSE_PAGE(201, "考生已关闭作答页面", ""),
    ERR_ACTION_CLOSE_CAMERA(202, "摄像头权限未开启", ""),
    ERR_ACTION_CLOSE_SCREEN(203, "考生关闭投屏", ""),
    ERR_ACTION_COVER_CAMERA(204, "考生遮挡摄像头", ""),

    ERR_MICROPHONE_ABSENCE(301, "无麦克风设备", ""),
    ERR_MICROPHONE_FAULT(302, "麦克风故障", ""),
    ERR_MICROPHONE_CLOSE(303, "麦克风权限未开启", ""),

    ERR_OTHER_OPERATOR_CLOSE_RECORD(401, "监考停止录制", ""),
    ERR_OTHER_OTHER(402, "其他异常", ""),
 */
export function _postErrAction(params: any) {
    return postForm('/wapi/web/exam/examinee/errAction.json', params);
}

enum ERROR_ACTION_TYPE {
    // 正常
    ERR_SUCC_COMMON_OK = 1,
    // 无摄像头
    ERR_CAMERA_ABSENCE = 101,
    // 摄像头被占用
    ERR_CAMERA_OCCUPY = 102,
    // 摄像头被禁用
    ERR_CAMERA_FORBIDDEN = 103,
    // 虚拟摄像头
    ERR_CAMERA_VIRTUAL = 104,

    // 摄像头权限未开启
    ERR_ACTION_CLOSE_CAMERA = 202,
    // 考生关闭投屏
    ERR_ACTION_CLOSE_SCREEN = 203,
    // 考生遮挡摄像头
    ERR_ACTION_COVER_CAMERA = 204,

    // 无麦克风
    ERR_MICROPHONE_ABSENCE = 301,
    // 麦克风故障
    ERR_MICROPHONE_FAULT = 302,
    // 麦克风权限未开启
    ERR_MICROPHONE_CLOSE = 303,

    // 其他异常
    ERR_OTHER_OTHER = 402,
}

type ERROR_ACTION_TYPE_VALUES =
    | ERROR_ACTION_TYPE.ERR_SUCC_COMMON_OK
    | ERROR_ACTION_TYPE.ERR_CAMERA_ABSENCE
    | ERROR_ACTION_TYPE.ERR_CAMERA_OCCUPY
    | ERROR_ACTION_TYPE.ERR_CAMERA_FORBIDDEN
    | ERROR_ACTION_TYPE.ERR_CAMERA_VIRTUAL
    | ERROR_ACTION_TYPE.ERR_ACTION_CLOSE_CAMERA
    | ERROR_ACTION_TYPE.ERR_ACTION_CLOSE_SCREEN
    | ERROR_ACTION_TYPE.ERR_ACTION_COVER_CAMERA
    | ERROR_ACTION_TYPE.ERR_MICROPHONE_ABSENCE
    | ERROR_ACTION_TYPE.ERR_MICROPHONE_FAULT
    | ERROR_ACTION_TYPE.ERR_MICROPHONE_CLOSE
    | ERROR_ACTION_TYPE.ERR_OTHER_OTHER;

// 标志位 是否已经判定过虚拟设备，如果已经判定后不再进行1恢复
let isVirtualDevice = false;
export function sendErrorAction(errType: ERROR_ACTION_TYPE_VALUES, source?: 1 | 2 | 3, examId?: string) {
    try {
        const sourceType = source || 1;

        if (sourceType === 1 && isVirtualDevice && errType === ERROR_ACTION_TYPE.ERR_SUCC_COMMON_OK) {
            return;
        }
        if (sourceType === 1 && errType === ERROR_ACTION_TYPE.ERR_CAMERA_VIRTUAL) {
            isVirtualDevice = true;
        }

        // todo: 获取加密考试ID，取小没有再取大
        const encryptExamId = examId || location.pathname.split('/')[3] || location.pathname.split('/')[2];

        // 上报异常
        _postErrAction({
            encryptExamId,
            errType,
            source: sourceType,
        });
    } catch (error) {
        logger.error(error);
    }
}

const errorCodes = {
    video: {
        NotFoundError: 101,
        NotReadableError: 102,
        SecurityError: 103,
        NotAllowedError: 202,
    },
    audio: {
        NotFoundError: 301,
        NotAllowedError: 303,
        AbortError: 302,
        SecurityError: 302,
        NotReadableError: 302,
    },
};

export function sendInitializeErrorAction(type: keyof typeof errorCodes, name: keyof (typeof errorCodes)[typeof type]) {
    try {
        const errorType = errorCodes[type][name] || 402;
        sendErrorAction(errorType);
    } catch (error) {
        logger.error(error);
    }
}

// 虚拟设备上报
export function sendVirtualErrorAction(audioLabel: string, videoLabel: string) {
    try {
        const hasVirtualDevice = VIRTUAL_DEVICE_LABLE_LIST.some(
            (brand) => audioLabel.toLowerCase().includes(brand.toLowerCase()) || videoLabel.toLowerCase().includes(brand.toLowerCase()),
        );

        if (!hasVirtualDevice) {
            return;
        }

        sendErrorAction(ERROR_ACTION_TYPE.ERR_CAMERA_VIRTUAL);
    } catch (error) {
        logger.error(error);
    }
}
