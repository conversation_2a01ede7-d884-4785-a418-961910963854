.page-wrap-monitor {
    padding: 8px 8px 0;
    // width: 1350px;
    // margin: 0 auto;
    height: calc(100vh - 64px);

    .monitor-inner {
        display: flex;
        flex-direction: column;
        height: calc(100% - 9px);
        .exam-info {
            display: flex;
            padding: 0 16px;
            line-height: 18px;
            height: 64px;
            align-items: center;
            flex-shrink: 0;
            .name {
                flex-shrink: 0;
                font-size: 18px;
                font-weight: 500;
                color: #2d2d2d;
            }
            .time {
                flex: 1;
                margin-left: 12px;
                font-size: 13px;
                color: #5d7080;
            }
        }
        .exam-setting {
            display: flex;
            flex: 1;
            flex-direction: column;
            background-color: #fff;
            border-radius: 8px;
            overflow: hidden;
            .setting-area {
                flex: 1;
                padding-top: 34px;
                overflow-y: auto;
                .b-step {
                    margin: 0 100px 30px;
                    margin-bottom: 30px;
                    .b-step-item:not(:last-child) .b-step-item-tail {
                        height: var(--border-width-2);
                    }
                    .b-step-item:not(:last-child) .b-step-item-tail::after {
                        background-color: transparent;
                        background-image: repeating-linear-gradient(to right, #12ada9, #12ada9 3px, #fff 3px, #fff 7px);
                    }
                    .b-step-item-node {
                        font-weight: 400;
                    }
                    .b-step-icon {
                        line-height: 22px;
                        font-size: 12px;
                    }
                    .b-step-item-content {
                        line-height: 16px;
                    }
                    .b-step-item-title {
                        margin-top: 10px;
                        font-size: 12px;
                        line-height: 16px;
                    }
                    .b-step-item-danger {
                        .b-step-icon {
                            color: var(--danger-color-6);
                            background-color: #fff;
                            border-color: var(--danger-color-6);
                        }
                    }
                    .b-step-item-finish {
                        .b-step-icon {
                            color: var(--primary-color);
                            background-color: #fff;
                            border-color: var(--primary-color);
                        }
                    }
                    .b-step-item-wait {
                        .b-step-item-title {
                            color: var(--text-color-5);
                        }
                        .b-step-icon {
                            color: #939cbc;
                            background-color: #fff;
                            border: 1px solid #939cbc;
                        }
                    }
                    .b-step-item-active {
                        .b-step-item-title {
                            color: var(--primary-color);
                        }
                    }
                }
            }
            .setting-footer {
                flex-shrink: 0;
                width: 820px;
                padding: 23px 0;
                margin: 0 auto;
                text-align: right;
                .btn + .btn {
                    margin-left: 12px;
                }
                .op-help {
                    display: inline-flex;
                    align-items: flex-start;
                    padding: 2px 5px;
                    margin-right: 15px;
                    line-height: 20px;
                    color: #808080;
                    vertical-align: middle;
                    border-radius: 4px;
                    cursor: pointer;
                    svg {
                        margin-top: 2px;
                        margin-right: 2px;
                    }
                    &:hover {
                        background: #f2f2f2;
                        color: #4d4d4d;
                    }
                }
            }
            .setting-inner {
                .setting-inner-title {
                    line-height: 26px;
                    font-size: 20px;
                    font-weight: 500;
                    color: #2d2d2d;
                    text-align: center;
                    margin-bottom: 5px;
                    & + .setting-inner-content {
                        margin-top: 16px;
                    }
                }
                .setting-inner-desc {
                    margin-bottom: 24px;
                    line-height: 20px;
                    font-size: 12px;
                    color: #808080;
                    text-align: center;
                }
                .setting-inner-content {
                    display: flex;
                    width: 872px;
                    margin: 0 auto;
                }
                .setting-inner-mini {
                    width: 820px;
                    margin: 0 auto;
                }
            }
        }
    }
}

.chat-entrance {
    position: relative;
    .chat-entrance-trigger {
        display: flex;
        align-items: center;
        width: 79px;
        height: 36px;
        padding-left: 6px;
        white-space: nowrap;
        background: #29292d;
        font-size: 12px;
        color: #fff;
        border-top-left-radius: 18px;
        border-bottom-left-radius: 18px;
        cursor: pointer;
        &:hover {
            background: #3a3a3e;
        }
        svg {
            margin-right: 6px;
        }
    }
}
.chat-fade-enter-active,
.chat-fade-leave-active {
    transition: all 0.3s ease-out;
}
.chat-fade-enter-from,
.chat-fade-leave-to {
    transform: translateX(20px);
    opacity: 0;
}
