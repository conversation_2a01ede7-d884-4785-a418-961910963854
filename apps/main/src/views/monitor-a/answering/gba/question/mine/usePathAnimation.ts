import { onBeforeUnmount, ref } from 'vue';
import { gsap } from 'gsap';
import { MotionPathPlugin } from 'gsap/MotionPathPlugin';

gsap.registerPlugin(MotionPathPlugin);

interface PathOption {
    size: [number, number];
    position: [number, number];
    opacity?: number;
}

export function usePathAnimation() {
    const animation = ref<gsap.core.Tween | null>(null);

    // 创建动画函数
    const startAnimation = async (path: PathOption[], onComplete?: () => void) => {
        if (path.length < 1) {
            return;
        }
        const startPoint = path[0];
        const animateDomOuter = createCoinElement(startPoint);

        // 设置元素初始位置为起点位置，忽略当前 DOM 节点的实际位置
        gsap.set(animateDomOuter, {
            xPercent: -50,
            yPercent: -50,
            transformOrigin: '50% 50%',
        });

        const handleComplete = () => {
            document.body.removeChild(animateDomOuter);
            onComplete?.();
        };

        animation.value = gsap.to(animateDomOuter, {
            motionPath: {
                path: path.map(({ position, size, opacity }) => ({
                    x: position[0] - startPoint.position[0],
                    y: position[1] - startPoint.position[1],
                    scaleX: size[0] / startPoint.size[0],
                    scaleY: size[1] / startPoint.size[1],
                    opacity: opacity ?? 1,
                })),
                curviness: 2,
            },
            duration: 0.3,
            onComplete: handleComplete,
        });
    };

    const reset = () => {
        if (animation.value) {
            animation.value.restart();
        }
    };

    onBeforeUnmount(() => {
        // 组件卸载时销毁动画
        if (animation.value) {
            animation.value.kill();
        }
    });

    return {
        startAnimation,
        reset,
    };
}

function createCoinElement(options: PathOption) {
    const animateDomOuter = document.createElement('div');
    const animateDomInner = document.createElement('img'); // 使用 img 标签
    const cdnUrl = 'https://img.bosszhipin.com/static/file/2024/h0mnebt9f41728704191012.svg';

    animateDomOuter.appendChild(animateDomInner);
    animateDomOuter.style.position = 'fixed';
    animateDomOuter.style.width = `${options.size[0]}px`;
    animateDomOuter.style.height = `${options.size[1]}px`;
    animateDomOuter.style.left = `${options.position[0]}px`;
    animateDomOuter.style.top = `${options.position[1]}px`;
    animateDomOuter.style.zIndex = '9999';

    // 设置 img 标签的 src 属性为远程 CDN 的 SVG 链接
    animateDomInner.src = cdnUrl;
    animateDomInner.style.width = '100%';
    animateDomInner.style.height = '100%';
    animateDomInner.style.opacity = '1';

    document.body.append(animateDomOuter);

    return animateDomOuter;
}
