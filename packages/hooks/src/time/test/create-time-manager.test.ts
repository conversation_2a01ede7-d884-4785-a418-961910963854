import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createTimeManager } from '../create-time-manager';
import { TimerStatus } from '../types';
import { nextTick } from 'vue';

const raf = (callback: FrameRequestCallback) => {
    const id = setTimeout(() => callback(performance.now()), 16);
    return id as unknown as number;
};
const caf = vi.fn((id: number) => {
    clearTimeout(id);
});

describe('createTimeManager', () => {
    beforeEach(() => {
        vi.useFakeTimers();
        vi.clearAllMocks();
        caf.mockClear();

        // Mock performance.now
        global.performance = global.performance || {};
        global.performance.now = vi.fn(() => Date.now());
    });

    afterEach(() => {
        vi.useRealTimers();
        vi.restoreAllMocks();
    });

    describe('基本功能', () => {
        it('应该创建时间管理器实例', () => {
            const { timeCenter, createTimer } = createTimeManager();

            expect(timeCenter).toBeDefined();
            expect(createTimer).toBeTypeOf('function');
        });

        it('应该使用默认配置', () => {
            const { timeCenter } = createTimeManager();

            expect(timeCenter.currentTime.value).toBeTypeOf('number');
            expect(timeCenter.accumulatedTime.value).toBe(0);
        });

        it('应该使用自定义配置', () => {
            const customOptions = {
                syncTimeFrequency: 20000,
            };

            const { timeCenter } = createTimeManager(customOptions);

            expect(timeCenter).toBeDefined();
        });
    });

    describe('reconfigure 功能', () => {
        it('应该能够重新配置时间管理器', async () => {
            const { timeCenter, reconfigure, createTimer } = createTimeManager();

            // 创建一个计时器使用原时间中心
            const timer1 = createTimer();
            timer1.start({});
            const originalBeginTime = timer1.beginTime;

            // 等待一小段时间确保时间变化
            await vi.advanceTimersByTimeAsync(10);
            await nextTick();

            reconfigure({
                syncTimeFrequency: 30000,
            });

            // 创建新计时器使用新时间中心
            const timer2 = createTimer();
            timer2.start({});

            // 验证新计时器使用了不同的时间基准
            expect(timer2.beginTime).toBeGreaterThan(originalBeginTime);
            expect(timeCenter).toBeDefined();
        });

        it('应该在重新配置后使用新的配置', async () => {
            const originalSyncTime = vi.fn().mockResolvedValue(1000000);
            const newSyncTime = vi.fn().mockResolvedValue(2000000);

            const { timeCenter, reconfigure } = createTimeManager({
                syncTime: originalSyncTime,
                syncTimeFrequency: 1000,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 等待初始同步
            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            expect(originalSyncTime).toHaveBeenCalled();

            // 重新配置
            reconfigure({
                syncTime: newSyncTime,
                syncTimeFrequency: 2000,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 等待新配置的同步
            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            expect(newSyncTime).toHaveBeenCalled();

            timeCenter.stopUpdateTime();
        });

        it('应该在重新配置后创建新的时间中心实例', () => {
            const rafSpy = vi.fn((callback: FrameRequestCallback) => {
                const id = setTimeout(() => callback(performance.now()), 16);
                return id as unknown as number;
            });
            const cafSpy = vi.fn((id: number) => {
                clearTimeout(id);
            });

            const manager = createTimeManager({
                requestAnimationFrame: rafSpy,
                cancelAnimationFrame: cafSpy,
            });

            expect(rafSpy).toHaveBeenCalled();
            const originalTimeCenter = manager.timeCenter;

            manager.reconfigure({
                requestAnimationFrame: rafSpy,
                cancelAnimationFrame: cafSpy,
            });

            const newTimeCenter = manager.timeCenter;

            // 验证创建了新的时间中心实例
            expect(newTimeCenter).not.toBe(originalTimeCenter);
            expect(newTimeCenter).toBeDefined();
            expect(typeof newTimeCenter.currentTime.value).toBe('number');

            // 手动清理
            originalTimeCenter.stopUpdateTime();
            newTimeCenter.stopUpdateTime();
        });

        it('应该支持重新配置为无 syncTime 的配置', async () => {
            const originalSyncTime = vi.fn().mockResolvedValue(1000000);

            const { timeCenter, reconfigure } = createTimeManager({
                syncTime: originalSyncTime,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 重新配置为不使用 syncTime
            reconfigure({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            expect(timeCenter).toBeDefined();
            expect(timeCenter.currentTime.value).toBeTypeOf('number');

            timeCenter.stopUpdateTime();
        });

        it('应该支持多次重新配置', async () => {
            const syncTime1 = vi.fn().mockResolvedValue(1000000);
            const syncTime2 = vi.fn().mockResolvedValue(2000000);
            const syncTime3 = vi.fn().mockResolvedValue(3000000);

            const { timeCenter, reconfigure, createTimer } = createTimeManager({
                syncTime: syncTime1,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 等待初始同步
            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            // 第一次重新配置
            reconfigure({
                syncTime: syncTime2,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 等待第一次重新配置的同步
            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            expect(syncTime2).toHaveBeenCalled();

            // 第二次重新配置
            reconfigure({
                syncTime: syncTime3,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 等待第二次重新配置的同步
            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            expect(syncTime3).toHaveBeenCalled();

            // 验证配置确实生效了
            const timer = createTimer();
            timer.start({});
            expect(timer.status).toBe(TimerStatus.running);

            timeCenter.stopUpdateTime();
        });

        it('重新配置后新创建的计时器应该使用新的时间中心', async () => {
            const { createTimer, reconfigure } = createTimeManager();

            // 创建第一个计时器
            const timer1 = createTimer();
            timer1.start({});
            const timer1StartTime = timer1.beginTime;

            // 等待一段时间
            await vi.advanceTimersByTimeAsync(1000);
            await nextTick();

            // 重新配置
            reconfigure({
                syncTimeFrequency: 30000,
            });

            // 创建第二个计时器
            const timer2 = createTimer();
            timer2.start({});

            // 验证两个计时器的开始时间不同（因为时间已经过去了）
            expect(timer2.beginTime).toBeGreaterThan(timer1StartTime);
            expect(timer1.status).toBe(TimerStatus.running);
            expect(timer2.status).toBe(TimerStatus.running);
        });

        it('应该正确处理重新配置时的无效参数', () => {
            const { timeCenter, reconfigure } = createTimeManager();

            // 传入 undefined 应该使用默认配置
            expect(() => reconfigure(undefined)).not.toThrow();
            expect(timeCenter).toBeDefined();

            // 传入空对象应该使用默认配置
            expect(() => reconfigure({})).not.toThrow();
            expect(timeCenter).toBeDefined();

            timeCenter.stopUpdateTime();
        });

        it('应该在重新配置时保持已有计时器的独立性', async () => {
            const { createTimer, reconfigure, timeCenter } = createTimeManager({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 创建并启动第一个计时器
            const timer1 = createTimer();
            timer1.start({});

            // 保存旧的时间中心引用
            const oldTimeCenter = timeCenter;

            // 推进时间
            await vi.advanceTimersByTimeAsync(1000);
            await nextTick();

            const timer1ElapsedBefore = timer1.elapsedTime.total;
            expect(timer1ElapsedBefore).toBeCloseTo(1000, -2);

            // 重新配置
            reconfigure({
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 旧的计时器继续使用旧的时间中心，继续正常工作
            expect(timer1.status).toBe(TimerStatus.running);

            // 创建新的计时器应该使用新的时间中心
            const timer2 = createTimer();
            timer2.start({});
            expect(timer2.status).toBe(TimerStatus.running);

            // 推进时间，新计时器应该正常工作
            await vi.advanceTimersByTimeAsync(500);
            await nextTick();

            expect(timer2.elapsedTime.total).toBeCloseTo(500, -2);
            // 旧计时器也应该继续工作
            expect(timer1.elapsedTime.total).toBeCloseTo(1500, -2);

            // 清理新旧时间中心
            oldTimeCenter.stopUpdateTime();
            timeCenter.stopUpdateTime();
        });

        it('应该正确处理重新配置时同步函数的错误', async () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
            const failingSyncTime = vi.fn().mockRejectedValue(new Error('Sync failed'));

            const { timeCenter, reconfigure } = createTimeManager();

            reconfigure({
                syncTime: failingSyncTime,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            expect(consoleSpy).toHaveBeenCalledWith('服务器时间同步失败:', expect.any(Error));
            expect(timeCenter).toBeDefined();

            timeCenter.stopUpdateTime();
            consoleSpy.mockRestore();
        });

        it('应该支持重新配置同步频率', async () => {
            let syncCallCount = 0;
            const mockSyncTime = vi.fn().mockImplementation(() => {
                syncCallCount++;
                return Promise.resolve(Date.now() + syncCallCount * 1000);
            });

            const { timeCenter, reconfigure } = createTimeManager({
                syncTime: mockSyncTime,
                syncTimeFrequency: 100, // 快速频率
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 触发几次快速同步
            await vi.advanceTimersToNextTimerAsync();
            await nextTick();
            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            const callCountBefore = mockSyncTime.mock.calls.length;

            // 重新配置为更慢的频率
            reconfigure({
                syncTime: mockSyncTime,
                syncTimeFrequency: 10000, // 慢频率
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            // 验证新配置生效
            expect(timeCenter).toBeDefined();

            timeCenter.stopUpdateTime();
        });
    });

    describe('计时器创建', () => {
        it('应该能够创建计时器', () => {
            const { createTimer } = createTimeManager();
            const timer = createTimer();

            expect(timer).toBeDefined();
            expect(timer.status).toBe(TimerStatus.idled);
        });

        it('应该能够创建多个独立的计时器', () => {
            const { createTimer } = createTimeManager();
            const timer1 = createTimer();
            const timer2 = createTimer();

            expect(timer1).not.toBe(timer2);
            expect(timer1.status).toBe(TimerStatus.idled);
            expect(timer2.status).toBe(TimerStatus.idled);
        });

        it('创建的计时器应该使用相同的时间中心', async () => {
            const { createTimer } = createTimeManager();
            const timer1 = createTimer();
            const timer2 = createTimer();

            // 启动两个计时器
            timer1.start({});
            timer2.start({});

            expect(timer1.status).toBe(TimerStatus.running);
            expect(timer2.status).toBe(TimerStatus.running);
        });
    });

    describe('多计时器管理', () => {
        it('应该能够独立控制多个计时器', () => {
            const { createTimer } = createTimeManager();
            const timer1 = createTimer();
            const timer2 = createTimer();

            timer1.start({});
            timer1.pause();

            timer2.start({});

            expect(timer1.status).toBe(TimerStatus.suspended);
            expect(timer2.status).toBe(TimerStatus.running);
        });

        it('应该能够处理计时器的不同生命周期', async () => {
            const { createTimer } = createTimeManager();
            const timers = [createTimer(), createTimer(), createTimer()];

            // 启动三个计时器，不同的结束时间
            timers[0].start({ finishTime: () => Date.now() + 1000 });
            timers[1].start({ finishTime: () => Date.now() + 2000 });
            timers[2].start({ finishTime: () => Date.now() + 3000 });

            // 停止第一个计时器
            timers[0].stop();

            expect(timers[0].status).toBe(TimerStatus.stopped);
            expect(timers[1].status).toBe(TimerStatus.running);
            expect(timers[2].status).toBe(TimerStatus.running);
        });
    });

    describe('时间同步', () => {
        it('应该为所有计时器提供统一的时间源', async () => {
            const mockSyncTime = vi.fn().mockResolvedValue(2000000);

            const { timeCenter, createTimer } = createTimeManager({
                syncTime: mockSyncTime,
                syncTimeFrequency: 1000,
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            });

            const timer1 = createTimer();
            const timer2 = createTimer();

            // 等待初始同步
            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            timer1.start({});
            timer2.start({});

            expect(mockSyncTime).toHaveBeenCalled();
            // 验证计时器的时间基于同步时间
            expect(timer1.beginTime).toBeGreaterThan(0);
            expect(timer2.beginTime).toBeGreaterThan(0);

            // 清理
            timeCenter.stopUpdateTime();
        });
    });

    describe('性能和资源管理', () => {
        it('应该能够创建大量计时器而不影响性能', () => {
            const { createTimer } = createTimeManager();
            const timers = [];

            const startTime = performance.now();
            for (let i = 0; i < 100; i++) {
                timers.push(createTimer());
            }
            const endTime = performance.now();

            expect(endTime - startTime).toBeLessThan(100); // 应该在100ms内完成
            expect(timers).toHaveLength(100);
        });

        it('应该能够正确清理已销毁的计时器', () => {
            const { createTimer } = createTimeManager();
            const timer = createTimer();

            timer.start({});
            expect(timer.status).toBe(TimerStatus.running);

            timer.destroy();
            expect(timer.status).toBe(TimerStatus.destroyed);
        });
    });

    describe('配置传递', () => {
        it('应该正确传递自定义配置到时间中心', async () => {
            const customOptions = {
                syncTimeFrequency: 20000,
                syncTime: vi.fn().mockResolvedValue(Date.now()),
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            };

            const manager = createTimeManager(customOptions);

            expect(manager.timeCenter).toBeDefined();

            // 验证配置被正确传递（通过行为验证）
            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            expect(customOptions.syncTime).toHaveBeenCalled();

            // 清理
            manager.timeCenter.stopUpdateTime();
        });

        it('应该处理同步函数错误', async () => {
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
            const customOptions = {
                syncTime: vi.fn().mockRejectedValue(new Error('Sync failed')),
                requestAnimationFrame: raf,
                cancelAnimationFrame: caf,
            };

            const manager = createTimeManager(customOptions);

            await vi.advanceTimersToNextTimerAsync();
            await nextTick();

            expect(consoleSpy).toHaveBeenCalledWith('服务器时间同步失败:', expect.any(Error));

            manager.timeCenter.stopUpdateTime();
            consoleSpy.mockRestore();
        });
    });

    describe('工厂函数行为', () => {
        it('每次调用 createTimer 都应该返回新的实例', () => {
            const { createTimer } = createTimeManager();

            const timer1 = createTimer();
            const timer2 = createTimer();
            const timer3 = createTimer();

            expect(timer1).not.toBe(timer2);
            expect(timer2).not.toBe(timer3);
            expect(timer1).not.toBe(timer3);
        });

        it('所有创建的计时器都应该共享同一个时间中心', () => {
            const { createTimer } = createTimeManager();

            const timer1 = createTimer();
            const timer2 = createTimer();

            // 启动计时器并检查它们是否使用相同的时间基准
            timer1.start({});
            timer2.start({});

            // 如果使用相同的时间中心，它们的开始时间应该非常接近
            expect(Math.abs(timer1.beginTime - timer2.beginTime)).toBeLessThan(50);
        });
    });
});
