<template>
    <urlQrCode v-if="h5ExamMonitorUrl" :url="h5ExamMonitorUrl" :width="width" :height="height" />
</template>
<script lang="ts" setup>
import { watchEffect } from 'vue';
import urlQrCode from './url-qr-code.vue';
import { getH5ExamMonitor } from '@/utils/index';
import { useMonitorStore } from '@/store/use-monitor-store';

defineOptions({
    name: 'H5ExamMonitorQrCode',
});

const props = defineProps({
    width: {
        type: Number,
        default: 150,
    },
    height: {
        type: Number,
        default: 150,
    },
});

const $monitorStore = useMonitorStore();

const h5ExamMonitorUrl = ref('');

watchEffect(async (newVal) => {
    const { encryptUserId, examInfo, seqInfo } = $monitorStore.examBaseInfo;

    const examId = examInfo?.examId || seqInfo?.seqId;

    if (encryptUserId && examId) {
        h5ExamMonitorUrl.value = await getH5ExamMonitor({ examId, encryptUserId });
    }
});
</script>
