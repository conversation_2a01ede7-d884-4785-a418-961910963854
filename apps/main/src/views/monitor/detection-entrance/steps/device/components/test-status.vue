<template>
    <div class="detection-status">
        <SvgIcon v-if="isTesting" name="svg-full-loading" width="14" height="14" class="rotating" />
        <SvgIcon v-else-if="isSuccess" name="svg-full-success-hollow" width="16" height="16" />
        <SvgIcon v-else :name="`svg-full-${errorLevel}-hollow`" width="16" height="16" />
    </div>
</template>

<script setup lang="ts">
const props = defineProps({
    // 是否正在检测
    isTesting: {
        type: Boolean,
        default: true,
    },
    // 是否检测成功
    isSuccess: {
        type: Boolean,
        default: false,
    },
    // 失败展示警告级别 warning or error
    errorLevel: {
        type: String,
        default: 'error',
    },
});
</script>

<style scoped lang="less">
@keyframes rotate-loading {
    0% {
        transform: rotate(0);
    }

    100% {
        transform: rotate(360deg);
    }
}
.rotating {
    animation: rotate-loading 1s infinite linear;
}
</style>
