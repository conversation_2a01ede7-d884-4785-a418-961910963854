<template>
    <svg width="440" height="440" viewBox="0 0 440 440">
        <defs>
            <linearGradient id="gradient1" x1="1" y1="0" x2="0" y2="0">
                <stop offset="0%" stop-color="#7cd2d0" />
                <stop offset="100%" stop-color="#ecf9f9" />
            </linearGradient>
            <linearGradient id="gradient2" x1="1" y1="0" x2="0" y2="0">
                <stop offset="0%" stop-color="#7cd2d0" />
                <stop offset="100%" stop-color="#14ada9" />
            </linearGradient>
        </defs>
        <g transform="matrix(0,-1,1,0,0,440)">
            <circle cx="220" cy="220" r="170" stroke-width="7" stroke="url('#gradient1')" fill="none" stroke-dasharray="1069 1069" />
            <circle cx="220" cy="220" r="170" stroke-width="7" stroke="url('#gradient2')" fill="none" stroke-dasharray="534.5 1069" />
        </g>
    </svg>
</template>
