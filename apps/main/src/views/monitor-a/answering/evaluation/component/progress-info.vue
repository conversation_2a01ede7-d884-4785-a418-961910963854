<template>
    <div class="progress-info-wrap">
        <p class="progress-info-title">
            {{ isCountdown ? '倒计时' : '已作答时间' }}
        </p>
        <div class="progress-time-wrap" v-if="isStart">
            <span
                v-if="isCountdown"
                :class="{
                    'time-warn': answeringTimeCountdown.total <= timeWarn,
                    'time-end': answeringTimeCountdown.total < 1000,
                }"
            >
                {{ answeringTimeCountdown?.hours }}:{{ answeringTimeCountdown?.minutes }}:{{ answeringTimeCountdown?.seconds }}
            </span>
            <span v-else> {{ answeringTime?.hours }}:{{ answeringTime?.minutes }}:{{ answeringTime?.seconds }} </span>
        </div>
        <div class="progress-bar-info">
            <span class="number">{{ currentIndex }} / {{ questionTotal }}</span>
            <div class="progress-bar-wrap">
                <div class="progress-bar-inner" :style="{ width: `${(Number(currentIndex) / questionTotal) * 100}%` }">
                    <div class="progress-bar-boll"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { usePaperStore } from '../store';
import { TimerStatus } from '@crm/exam-hooks';

defineOptions({
    name: 'ProgressInfo',
});

const timeWarn = 30 * 1000;
const paperStore = usePaperStore();

const questionTotal = computed(() => paperStore.allQuestionList.length);
const currentIndex = computed(() => {
    let number = paperStore.allQuestionList.filter((item) => item.status === 1).length;
    number = number + 1;
    if (number >= questionTotal.value) {
        number = questionTotal.value;
    }

    return number < 10 && questionTotal.value >= 10 ? `0${number}` : number;
});
// 是不是倒计时模式
const isCountdown = computed(() => paperStore.config.timedRuleType === 2);

// 正计时
const answeringTime = computed(() => paperStore.answeringTime?.elapsedTime);

// 倒计时
const answeringTimeCountdown = computed(() => paperStore.answeringTimeCountdown?.remainingTime);

// 计时器是否开始工作
const isStart = computed(() => {
    if (isCountdown.value) {
        return paperStore.answeringTime.status !== TimerStatus.idled;
    }

    return paperStore.answeringTimeCountdown.status !== TimerStatus.idled;
});
</script>

<style lang="less" scoped>
.progress-info-wrap {
    padding: 16px;
    background: #ffffff;
    border-radius: 12px;
    font-style: normal;
    font-weight: 400;
    text-align: center;

    .progress-info-title {
        color: #292929;
        font-size: 14px;
        line-height: 20px;
    }

    .progress-time-wrap {
        margin-top: 8px;
        height: 44px;
        background: #e5f6f6;
        border-radius: 8px;
        color: #00a6a7;
        font-family: 'Kanzhun';
        font-size: 24px;
        font-style: normal;
        font-weight: 400;
        line-height: 44px;

        .time-warn {
            color: #f06a39;
        }

        .time-end {
            color: #e03741;
            animation: colorChange 2s linear forwards infinite;
        }
    }

    .progress-bar-info {
        margin-top: 20px;
        .number {
            color: #00a6a7;
            font-family: 'Kanzhun';
            font-size: 14px;
            line-height: 17px;
        }

        .progress-bar-wrap {
            margin-top: 8px;
            position: relative;
            width: 100%;
            height: 7px;
            background: #e6e6eb;
            border-radius: 7px;

            .progress-bar-inner {
                position: absolute;
                left: 0;
                top: 0;
                height: 100%;
                background: #00a6a7;
                border-radius: 7px;

                .progress-bar-boll {
                    position: relative;
                    width: 100%;
                    height: 100%;

                    &::after,
                    &::before {
                        content: '';
                        position: absolute;
                        right: -12px;
                        top: 50%;
                        transform: translateX(-50%) translateY(-50%);
                        width: 12px;
                        height: 12px;
                        background: #00a6a7;
                        border-radius: 50%;
                    }

                    &::before {
                        right: -4px;
                        width: 4px;
                        height: 4px;
                        background: #fff;
                        z-index: 1;
                    }
                }
            }
        }
    }
}

@keyframes colorChange {
    0% {
        opacity: 0.2;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.2;
    }
}
</style>
