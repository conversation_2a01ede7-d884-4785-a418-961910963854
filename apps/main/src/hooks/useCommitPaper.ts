// 交卷
import type { RouteLocationNormalizedLoaded, Router } from 'vue-router';
import { exitFull } from '@crm/exam-utils';
import { ExamStatusEnum } from '@crm/exam-types';

export default async function (type: 'manual' | 'force' | 'timeout' | 'switchScreenForce', route: RouteLocationNormalizedLoaded, router: Router) {
    const params: any = {
        encryptExamId: route.params.examId,
    };

    const getTypeConfig = (typeStr: 'manual' | 'force' | 'timeout' | 'switchScreenForce', number: number = 0) => {
        const typeKey = typeStr || 'manual';
        const config = {
            manual: {
                remarks: '', // 为空 为手动交卷 服务端根据 remarks 值区分交卷方式
                text: `您已完成该场考试，剩余${number}场待完成`,
            },
            force: {
                remarks: '监考判定作弊强制交卷',
                text: '监考官已强制交卷',
            },
            timeout: {
                remarks: '考试结束考生未交卷系统自动交卷',
                text: '考试时间已结束，系统已自动交卷',
            },
            switchScreenForce: {
                remarks: '异常退出或切屏次数过多强制交卷',
                text: '检测到切换屏幕次数过多，系统已强制交卷',
            },
        };

        return config[typeKey];
    };

    try {
        const { remarks } = getTypeConfig(type, 0);
        const paramsData = {
            ...params,
            remarks,
        };
        const res = await Invoke.exam.postCommitPaper(paramsData);
        if (res.code === 0 || res.code === 109) {
            const { text } = getTypeConfig(type, res.data.pendingCount);

            router.replace(`/status/${route.params.seqId}?status=${ExamStatusEnum.已交卷}&text=${text}`);

            // 埋点开始
            BossAnalyticsTrack('zhice-pc-exam-paper-commit', {
                pData: {
                    type: TrackTypeEnum.成功,
                    message: type,
                    nameZh: '交卷成功',
                },
            });
            // 埋点结束
        } else {
            // 埋点开始
            BossAnalyticsTrack('zhice-pc-exam-paper-commit', {
                pData: {
                    type: TrackTypeEnum.失败,
                    message: type,
                    errorText: res,
                    nameZh: '交卷失败',
                },
            });
            // 埋点结束
        }
        exitFull();
    } catch (error: any) {
        // 埋点开始
        BossAnalyticsTrack('zhice-pc-exam-paper-commit', {
            pData: {
                type: TrackTypeEnum.失败,
                message: type,
                errorText: error.message || error.name,
                nameZh: '交卷出错',
            },
        });
        // 埋点结束
        router.replace(`/status/${route.params.seqId}?status=${ExamStatusEnum.已交卷}&text=交卷过程中出现错误，请联系监考官`);
    }
}
