<template>
    <div class="upload-container">
        <div v-if="fileList.length < 5" class="upload-button" @click="clickUpload">
            <slot name="upload-button">
                <svg viewBox="0 0 16 16" width="16px" height="16px">
                    <path
                        d="M8 1C4.141 1 1 4.141 1 8s3.141 7 7 7 7-3.141 7-7-3.141-7-7-7Zm0 13.067c-3.328 0-6.067-2.739-6.067-6.067 0-3.328 2.739-6.067 6.067-6.067 3.328 0 6.067 2.739 6.067 6.067 0 3.328-2.739 6.067-6.067 6.067Zm.512-7.604v4.44a.525.525 0 0 1-.512.512.525.525 0 0 1-.512-.513V6.463L5.439 8.376a.495.495 0 0 1-.717-.035.495.495 0 0 1 .034-.717L7.66 4.893A.49.49 0 0 1 8 4.756a.49.49 0 0 1 .341.137l2.903 2.731a.528.528 0 0 1 .034.717c-.205.205-.512.24-.717.035L8.512 6.463Z"
                        fill="currentColor"
                    />
                </svg>
                <span>点击上传附件</span>
            </slot>
        </div>
        <div class="upload-hint">
            <slot name="upload-hint"> 最多支持上传5个附件，每个附件最大50M </slot>
        </div>
        <div v-if="fileList.length > 0" class="upload-list">
            <div v-for="(item, index) of fileList" :key="index" class="upload-list-item">
                <div class="file-title">
                    <svg id="icon-image" viewBox="0 0 22 22" width="20px" height="20px" class="icon">
                        <defs>
                            <linearGradient id="icon-image_a" x1="50%" y1="0%" x2="50%" y2="100%">
                                <stop stop-color="#80DEDE" offset="0%" />
                                <stop stop-color="#00BEBD" offset="100%" />
                            </linearGradient>
                        </defs>
                        <g transform="translate(4 3)" fill="none" fill-rule="evenodd">
                            <path
                                d="M2.286 0H9.34L14 4.589v9.125A2.286 2.286 0 0 1 11.714 16H2.286A2.286 2.286 0 0 1 0 13.714V2.286A2.286 2.286 0 0 1 2.286 0Z"
                                fill="url(#icon-image_a)"
                            />
                            <path
                                d="M2.333 10.286 3.686 8.96a.571.571 0 0 1 .797-.002l1.375 1.328 2.457-2.913a.571.571 0 0 1 .872-.002l2.48 2.915V12a.571.571 0 0 1-.572.571h-8.19A.571.571 0 0 1 2.333 12v-1.714Z"
                                fill="#FFF"
                            />
                            <ellipse fill="#FFF" cx="3.5" cy="6.857" rx="1.167" ry="1.143" />
                            <path d="M9.333 0 14 4.571h-3.524A1.143 1.143 0 0 1 9.333 3.43V0Z" fill="#FFF" opacity=".5" />
                        </g>
                    </svg>
                    <span class="file-name">{{ item.file.name }}</span>
                </div>
                <div v-if="item.status === UPLOAD_STATUS.SUCCESS" class="extra-btn">
                    <b-tooltip content="下载">
                        <svg id="icon-svg-empty-download" viewBox="0 0 20 20" width="20px" height="20px" class="icon" @click="downloadFile(item)">
                            <g fill="none" fill-rule="evenodd">
                                <path
                                    d="M8 2.5h4a1 1 0 0 1 1 1v3.4a.1.1 0 0 0 .1.1h2.486a1 1 0 0 1 .707 1.707l-5.607 5.607a1 1 0 0 1-1.392.021L3.336 8.728A1 1 0 0 1 4.022 7H6.9a.1.1 0 0 0 .1-.1V3.5a1 1 0 0 1 1-1Z"
                                    stroke="currentColor"
                                    stroke-width="1.5"
                                />
                                <path d="M4.25 16.5h11.5a.75.75 0 1 1 0 1.5H4.25a.75.75 0 1 1 0-1.5Z" fill="currentColor" />
                            </g>
                        </svg>
                    </b-tooltip>
                    <b-tooltip content="删除">
                        <svg id="icon-svg-empty-delete" viewBox="0 0 20 20" width="20px" height="20px" class="icon" @click="deleteFile(item, index)">
                            <g fill="none" fill-rule="evenodd">
                                <path
                                    d="m7 4.5.763-1.563a1 1 0 0 1 .948-.684h2.559a1 1 0 0 1 .948.684L13 4.5M15.75 4.75V15c0 .621-.252 1.184-.659 1.591-.407.407-.97.659-1.591.659h-7a2.243 2.243 0 0 1-1.591-.659A2.243 2.243 0 0 1 4.25 15V4.75h11.5Z"
                                    stroke="currentColor"
                                    stroke-width="1.5"
                                />
                                <path d="M8.25 13h3.5a.75.75 0 1 1 0 1.5h-3.5a.75.75 0 1 1 0-1.5Z" fill="currentColor" />
                            </g>
                        </svg>
                    </b-tooltip>
                </div>
                <div v-else-if="item.status === UPLOAD_STATUS.FAIL" class="extra-btn">
                    <b-tooltip content="重试">
                        <svg id="icon-svg-empty-retry" viewBox="0 0 1024 1024" width="20px" height="20px" class="icon" @click="item.uploadAction">
                            <path
                                d="M754.37 65.01c6.37-2.72 13.74 0.24 16.47 6.61l65.71 153.84c0.47 1.09 0.78 2.25 0.92 3.43 0.83 6.88-4.08 13.13-10.96 13.96l-166.09 20.01c-2.7 0.32-5.42-0.23-7.77-1.59-6-3.46-8.06-11.14-4.59-17.14l34.17-59.29C505.21 95.19 286.92 158.66 186.5 332.59 82.56 512.62 144.24 742.83 324.27 846.77S734.5 889.03 838.44 709c29.07-50.35 45.76-105.61 49.63-162.3 1.18-17.28 16.14-30.34 33.43-29.16 17.28 1.18 30.34 16.14 29.16 33.43-4.51 66.18-24.01 130.71-57.89 189.4-121.26 210.03-389.83 282-599.87 160.73s-282-389.83-160.73-599.87C249.93 97.27 506.58 23.51 713.66 130.45l34.77-60.17c1.36-2.35 3.44-4.2 5.94-5.27z"
                                fill="currentColor"
                            />
                        </svg>
                    </b-tooltip>
                    <b-tooltip content="删除">
                        <svg id="icon-svg-empty-delete" viewBox="0 0 20 20" width="20px" height="20px" class="icon" @click="deleteFile(item, index)">
                            <g fill="none" fill-rule="evenodd">
                                <path
                                    d="m7 4.5.763-1.563a1 1 0 0 1 .948-.684h2.559a1 1 0 0 1 .948.684L13 4.5M15.75 4.75V15c0 .621-.252 1.184-.659 1.591-.407.407-.97.659-1.591.659h-7a2.243 2.243 0 0 1-1.591-.659A2.243 2.243 0 0 1 4.25 15V4.75h11.5Z"
                                    stroke="currentColor"
                                    stroke-width="1.5"
                                />
                                <path d="M8.25 13h3.5a.75.75 0 1 1 0 1.5h-3.5a.75.75 0 1 1 0-1.5Z" fill="currentColor" />
                            </g>
                        </svg>
                    </b-tooltip>
                </div>
                <template v-else>
                    <span v-if="item.progress" class="upload-percentage">{{ item.progress.toFixed() }}%</span>
                    <b-progress v-if="item.progress" :percentage="item.progress" :stroke-width="2" :showText="false" />
                </template>
            </div>
        </div>
        <input id="" ref="inputRef" type="file" name="file" @change="handleChange" />
    </div>
</template>

<script setup lang="ts">
import type { IError, IFileItem } from './use-upload';
import { ref, watch } from 'vue';
import useUpload, { errorTypeEnum, UPLOAD_STATUS } from './use-upload';

defineOptions({
    name: 'Upload',
});
const props = withDefaults(defineProps<IProps>(), {
    modelValue: () => [],
    uploadMethod: () => Promise.resolve(),
    downloadMethod: () => Promise.resolve(),
    onError: () => ({}),
});
const emit = defineEmits<{
    'update:modelValue': [value: IModelValue[]];
}>();
interface IModelValue {
    encryptId: string;
    name: string;
    [key: string]: any;
}
interface IProps {
    modelValue?: IModelValue[];
    uploadMethod?: (params: any, fileItem: IFileItem) => Promise<any>;
    downloadMethod?: (params: any) => Promise<any>;
    onError?: (errors: IError[]) => void;
}

const inputRef = ref();
function clickUpload() {
    inputRef.value.click();
}

async function downloadFile(item: IFileItem) {
    const params = {
        fileId: item.responseBody.encryptId,
        fileName: item.responseBody.name,
    };
    try {
        const res = await props.downloadMethod(params);
        const data = res;
        const url = window.URL.createObjectURL(new Blob([data]));
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url;
        link.setAttribute('download', item.responseBody.name);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } catch (error: any) {}
}

const { fileList, onChange, submit } = useUpload({
    max: 5,
    allowSize: 50, // 单位为mb
    allowExt: ['image/png', 'image/jpg', 'image/jpeg'], // 格式数组
    onError: (errors) => {
        for (let i = 0; i < errors.length; i++) {
            // 把由于类型、大小错误的文件从fileList中删除
            if ([errorTypeEnum.SIZE_ERROR, errorTypeEnum.TYPE_ERROR].includes(errors[i].code)) {
                const index = fileList.value.findIndex((x) => x.uid === errors[i].message.fileId);
                if (index >= 0) {
                    fileList.value.splice(index, 1);
                }
            }
        }
        props.onError(errors);
    },
    onSuccess(res, fileItem) {
        if (res.code === 0) {
            fileItem.status = UPLOAD_STATUS.SUCCESS;
            fileItem.responseBody = res.data;
            emitValue();
        } else {
            fileItem.status = UPLOAD_STATUS.FAIL;
        }
    },
    autoValidate: true,
    autoUpload: true,
    action: props.uploadMethod,
    extraParams: { biz: 'QUESTION' },
});

function handleChange(e: Event) {
    onChange(e);
    submit();
}
function deleteFile(fileItem: IFileItem, index: number) {
    fileList.value.splice(index, 1);
    emitValue();
}
function emitValue() {
    const newValue = fileList.value
        .filter((x) => x.status === UPLOAD_STATUS.SUCCESS)
        .map((x) => ({
            encryptId: x.responseBody.encryptId,
            name: x.responseBody.name,
            status: x.status,
        }));
    emit('update:modelValue', newValue);
}
watch(
    () => props.modelValue,
    () => {
        fileList.value = props.modelValue.map((x, i) => ({
            uid: Date.now() + i,
            file: { name: x.name } as File,
            status: UPLOAD_STATUS.SUCCESS,
            responseBody: {
                encryptId: x.encryptId,
                name: x.name,
            },
            uploadAction: () => Promise.resolve(),
        }));
    },
    { once: true },
);
</script>

<style lang="less" scoped>
.upload-container {
    .upload-button {
        display: inline-flex;
        align-items: center;
        cursor: pointer;
        color: var(--primary-color-6);
        svg {
            margin-right: 4px;
        }
        &:hover {
            color: var(--primary-color-5);
        }
    }
    .upload-hint {
        font-size: 12px;
        color: #939cbc;
    }
    .upload-list {
        margin-top: 10px;
        .upload-list-item {
            display: flex;
            justify-content: space-between;
            height: 36px;
            line-height: 36px;
            background: #f4f4fb;
            padding: 0 12px 0 8px;
            border: 1px solid transparent;
            border-radius: 8px;
            position: relative;

            & + .upload-list-item {
                margin-top: 8px;
            }

            .file-title {
                display: flex;
                align-items: center;
                flex-grow: 0;
                flex-shrink: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;

                .icon {
                    margin-right: 4px;
                    flex-shrink: 0;
                }

                .file-name {
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                }
            }

            .extra-btn {
                display: flex;
                align-items: center;
                flex-shrink: 0;

                .icon {
                    margin-left: 8px;
                    cursor: pointer;
                    outline: none;
                    &:hover {
                        color: var(--primary-color-6);
                    }
                }
            }

            &:hover {
                border-color: var(--primary-color-3);

                .extra-btn {
                    display: flex;
                }
            }

            .upload-percentage {
                flex-shrink: 0;
            }

            .b-progress {
                position: absolute;
                width: 100%;
                bottom: 0;
                left: 0;
                line-height: 0;
                padding: 0 12px 0 8px;
            }
        }
    }
    input {
        display: none;
    }
}
</style>
