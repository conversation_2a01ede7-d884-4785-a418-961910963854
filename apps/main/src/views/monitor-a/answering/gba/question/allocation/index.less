.round-content {
    position: relative;
    padding: 24px 32px 18px;
    background: linear-gradient(179.99998deg, #7EDAFF 4%, #DDE6FF 47%, #3696FC 100%);
    box-shadow: inset 0px 4px 35px 0px rgba(0, 74, 123, 0.23);
    backdrop-filter: blur(18.5px);
    width: 100%;
    height: 100%;
    overflow: hidden;

    &::after {
        content: '';
        position: absolute;
        left: 0;
        height: 261px;
        right: 0;
        bottom: -5px;
        background-image: url('https://img.bosszhipin.com/static/file/2024/h6xy4k3ehq1725606964354.png.webp');
        background-size: 100% auto;
        z-index: -1;
    }


    .title-wrap {
        display: flex;
        align-items: center;
        position: relative;

        .progress-wrap {
            position: absolute;
            background: rgba(255, 255, 255, 0.60);
            border-radius: 179px;
            overflow: hidden;
            width: 360px;
            height: 20px;
            top: 6px;
            left: 180px;

            .bar {
                width: 242px;
                height: 20px;
                background: linear-gradient(179.99998deg, #CDFBFF 0%, #14E7FE 11%, #80F3FC 45%, #06E0FF 92%, #CFFFFF 100%);
                box-shadow: inset 1px 0px 3px 0px #FFFFFF;
                border-radius: 17px;
                transition: all .5s;
            }

            .text {
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translateX(-50%) translateY(-50%);
                color: #000000;
                font-size: 13px;
                font-style: normal;
                font-weight: 400;
                line-height: 13px;
            }
        }

        .total-fund-container {
            min-width: 173px;
            flex-shrink: 0;
            position: absolute;
            right: 0;
            display: flex;
            align-items: center;
            padding: 0 12px 0 3px;
        }
    }

    .allocation-wrap {
        position: relative;
        display: flex;
        gap: 24px;
        justify-content: space-between;
        margin: 50px 12px 0;

        & .player-card-container:first-child,
        .player-card-container:last-child {
            margin-top: -10px;
        }
    }

    .waiting {
        color: #FFFFFF;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        position: absolute;
        left: 41.5%;
        bottom: 85px;

        .dot {
            display: inline-block;
            height: 1em;
            line-height: 1;
            vertical-align: -.25em;
            overflow: hidden;
        }

        .dot::before {
            display: block;
            content: '...\A..\A.';
            white-space: pre-wrap;
            animation: dot-animation 1.5s infinite step-start both;
        }
    }

    .option-button-wrap {
        margin-top: 38px;
        display: flex;
        justify-content: center;
        gap: 149px;
        position: relative;
        z-index: 1;

        .button-inner {
            width: 112px;
            :deep(.button-wrap) {
                .text {
                    text-shadow: none;
                }
            }
            :deep(.button-wrap.disabled) {
                .title-container {
                    color: rgba(168, 210, 135, 1) !important;
                    filter: drop-shadow(0px 0.92px 0.92px #A3CF83);

                    &::before {
                        color: rgba(252, 255, 238, 1) !important;
                    }
                }

                &.orange {
                    .title-container {
                        color: rgba(254, 185, 137, 1) !important;
                        filter: drop-shadow(0px 0.92px 0.92px #FDAC74);

                        &::before {
                            color: rgba(255, 252, 238, 1) !important;
                        }
                    }
                }
            }

            .button-inner-text {
                .icon {
                    width: 20px;
                    height: 20px;
                    background-size: 100% auto;

                    &.green-tick {
                        background-image: url(https://img.bosszhipin.com/static/file/2024/34hjehwz2k1726036366579.png.webp);
                    }

                    &.orange-tick {
                        background-image: url(https://img.bosszhipin.com/static/file/2024/g8xr90q1k81726036367212.png.webp);
                    }

                    &.cross {
                        background-image: url(https://img.bosszhipin.com/static/file/2024/kvfc7nvx351726036366218.png.webp);
                    }
                }
            }

            .key-down-desc {
                margin-top: 8px;
                text-align: center;
                color: #fff;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
            }
        }
    }

    .countdown-info-wrap {
        color: #FFF9DC;
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
        text-align: center;
        transform: translateY(-53px);
        position: relative;
        text-shadow:
            1px 1px 0 rgba(0, 0, 0, 0.1),
            /* 右下角的阴影 */
            -1px -1px 0 rgba(0, 0, 0, 0.1),
            /* 左上角的阴影 */
            1px -1px 0 rgba(0, 0, 0, 0.1),
            /* 右上角的阴影 */
            -1px 1px 0 rgba(0, 0, 0, 0.1);
        /* 左下角的阴影 */
    }

    .countdown-info-tip {
        font-size: 14px;
        font-weight: 400;
        transform: translateY(-53px);
        text-shadow: none;
    }
}