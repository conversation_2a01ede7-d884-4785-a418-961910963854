<template>
    <div v-show="fundAnimationConfig.show" ref="playerFundRef" class="player-fund-change-wrap">
        <Text
            :key="fundAnimationConfig.text"
            :text="fundAnimationConfig.text"
            font-family="PingFang SC"
            font-size="26px"
            :fontWeight="fontWeight"
            :strokeWidth="strokeWidth"
            :strokeColor="fundTextAttrSet['stroke-color']"
            :textColor="fundTextAttrSet['text-color']"
            :shadowFilterConfig="{ x: 0, y: 1, blur: 2, color: fundTextAttrSet['shadow-color']! }"
        />
    </div>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue';
import { gsap, Power3 } from 'gsap';
import { computed, onMounted, ref, watch } from 'vue';
import Text from './text.vue';

const props = defineProps({
    fundAnimationConfig: {
        type: Object as PropType<{ show: boolean; text: string; action: string }>,
        default: () => ({ show: false, text: '', action: '+' }),
    },
    strokeWidth: {
        type: [String, Number],
        default: 0,
    },
    fontWeight: {
        type: [String, Number],
        default: 400,
    },
});
const emits = defineEmits(['fundStartVanish', 'fundAnimationEnd']);
const fundTextAttrSet = computed(() => {
    const attrSet: { 'stroke-color'?: string; 'text-color'?: string; 'shadow-color'?: string } = {};
    if (props.fundAnimationConfig.action === '+') {
        attrSet['stroke-color'] = 'rgba(0, 0, 0, 1)';
        attrSet['text-color'] = 'rgba(0, 0, 0, 1)';
        // attrSet['shadow-color'] = 'rgba(0, 0, 0, 0.70)'
    } else if (props.fundAnimationConfig.action === '-') {
        attrSet['stroke-color'] = 'rgba(0, 0, 0, 1)';
        attrSet['text-color'] = 'rgba(0, 0, 0, 1)';
        // attrSet['shadow-color'] = 'rgba(0, 0, 0, 0.70)'
    }
    return attrSet;
});

const playerFundRef = ref();
function registryFundAnimation(): gsap.core.Timeline {
    const t1 = gsap.timeline({
        paused: true,
        onComplete: () => {
            emits('fundAnimationEnd');
        },
    });
    t1.set(playerFundRef.value, { opacity: 0, y: 10 }).call(() => {}); // 如果需要回调，在每一步都可以插入
    t1.to(playerFundRef.value, { duration: 0.3, opacity: 1, y: 0, ease: Power3.easeOut }, '>');
    t1.to(playerFundRef.value, { duration: 0.3, opacity: 0, y: -10, ease: Power3.easeIn }, '>0.6').call(
        () => {
            emits('fundStartVanish');
        },
        [],
        '<',
    );
    return t1;
}
onMounted(() => {
    watch(
        () => props.fundAnimationConfig.show,
        async (show) => {
            if (show) {
                const t1 = registryFundAnimation();
                t1.play(0);
            }
        },
        { immediate: true },
    );
});
</script>
