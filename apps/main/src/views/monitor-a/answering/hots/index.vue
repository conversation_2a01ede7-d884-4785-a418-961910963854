<template>
    <div class="paper-content">
        <div v-if="loading" class="loading-content">
            <b-loading type="dot"> 加载中 </b-loading>
        </div>
        <!-- 试卷指导语控制 -->
        <OperationTips v-else-if="isShowOperationTips" @onOver="onOperationTipsOver" />
        <div v-else class="question-content">
            <template v-if="hotsStore.questionList.length">
                <div class="question-wrap">
                    <Question :isPreview="monitorStore.isPreview" :config="templateConfig" :encExamId="seqId" :encSeqTmpId="examId" />
                </div>
                <div class="answer-wrap">
                    <Answer :isPreview="monitorStore.isPreview" :config="templateConfig" @onSubmit="onSubmitPaper" @onNext="onNext" @onAnswerChange="addAnswer" />
                </div>
            </template>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useHotsStore } from './store';
import { questionTypeMap } from './constant';
import OperationTips from './component/operation-tips.vue';
import Question from './question/index.vue';
import Answer from './answer/index.vue';
import { useMonitorStore } from '@/store/use-monitor-store';
import { useEvaluationEvents } from '../hooks/useEvaluationEvents';
import { useRouteParamId } from '../../hooks/useRouteParamId';

const $route = useRoute();

const { seqId, examId } = useRouteParamId();

const hotsStore = useHotsStore();
const monitorStore = useMonitorStore();

const { onSubmitPaper, onQuestionChange } = useEvaluationEvents({ examId, seqId });

const { getPreviewInfo, getQuestion, nextQuestion } = hotsStore;

const isShowOperationTips = ref(true); // 指导语展示状态
const loading = ref(true);
const parentData = computed(() => {
    const { currentQuestion, sceneQuestionData } = hotsStore;
    const { parentEncryptId } = currentQuestion;
    return sceneQuestionData[parentEncryptId];
});

const templateConfig = computed(() => {
    const showType = parentData.value.data.product10QuestionParamVO.showType;

    return questionTypeMap[showType];
});

// 关闭试卷指导语
function onOperationTipsOver() {
    if (!monitorStore.isPreview) {
        postLog('eval_operating_instruction', {
            encExamId: examId as string,
        });
    }
    isShowOperationTips.value = false;
}

// 点击下一题并提交答案
async function onNext() {
    nextQuestion();
}

// 试卷暂存
async function addAnswer(params: any) {
    if (!monitorStore.isPreview) {
        await Invoke.hots.postFormAddTmpAnswer({
            encryptExamId: examId,
            ...params,
        });
    }
}

async function getQuestionInfo() {
    loading.value = true;
    if (monitorStore.isPreview) {
        await getPreviewInfo({
            key: $route.query.key as string,
        });
    } else {
        const res = await getQuestion({
            encryptExamId: examId,
        });

        const { clickOperatingInstruction } = res?.data || { clickOperatingInstruction: 0 };
        // 判断之前是否点击过 操作指引或作答说明
        if (clickOperatingInstruction === 1) {
            isShowOperationTips.value = false;
        }
    }
    loading.value = false;
}

watch(
    () => hotsStore.currentQuestion.encryptId,
    (val: string) => {
        onQuestionChange({ currentQuestionId: val });
    },
    { immediate: true },
);

onMounted(() => {
    getQuestionInfo();
});
</script>

<style scoped lang="less">
.paper-content {
    margin: 0 auto;
    max-width: 1366px;
    position: relative;
    top: 0;
    left: 0;
    flex: 1;
    display: flex;
    height: 100%;

    .loading-content {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        background-color: #fff;
        border-radius: 8px;
    }

    .question-content {
        flex: 1;
        margin: 0 auto;
        display: flex;
        height: 100%;

        .question-wrap {
            flex: 1;
            background: linear-gradient(160.13423deg, #f2f5f7 4%, #ffffff 48%);
            box-shadow:
                4px 4px 10px 0 rgba(34, 41, 43, 0.1),
                -5px -4px 14px 0 #ffffff;
            border-radius: 8px;
            overflow: hidden;
        }

        .answer-wrap {
            padding: 20px;
            position: relative;
            margin-left: 8px;
            width: 446px;
            height: 100%;
            background: linear-gradient(154.80472deg, #f2f5f7 1%, #ffffff 42%, #ffffff 96%);
            box-shadow:
                4px 4px 10px 0 rgba(34, 41, 43, 0.1),
                -5px -4px 14px 0 #ffffff;
            border-radius: 8px;
        }
    }
}
</style>
