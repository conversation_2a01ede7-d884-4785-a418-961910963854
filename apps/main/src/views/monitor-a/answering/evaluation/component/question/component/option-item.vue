<template>
    <div
        class="option-wrap"
        :class="{
            checked: isChecked,
            drap: isDrap,
        }"
    >
        <div class="option-content">
            <SvgIcon v-if="isDrap" class="icon-drap" icon="drap" size="16px" />
            <p>{{ content }}</p>
        </div>
        <p class="icon" :class="{ 'has-icon': !!icon }">
            {{ icon }}
        </p>
        <div v-if="imgList.length" :style="content ? { marginTop: '10px' } : {}">
            <ImgWrap :key="content" maxSizeType="option" :maxSize="{ width: 303, height: 360 }" :imgList="imgList" />
        </div>
    </div>
</template>

<script setup lang="ts">
import ImgWrap from './img-wrap.vue';

defineProps({
    content: {
        type: String,
        default: '',
    },
    imgList: {
        type: Array,
        default: () => [],
    },
    isChecked: {
        type: Boolean,
        default: false,
    },
    isDrap: {
        type: Boolean,
        default: false,
    },
    icon: {
        type: String,
        default: '',
    },
});
</script>

<style lang="less" scoped>
.option-wrap {
    position: relative;
    overflow: hidden;
    width: 100%;
    padding: 16px 18px;
    border-radius: 8px;
    border: 1px solid #e6e6eb;
    transition: all 0.3s;
    font-size: 14px;
    color: #292929;
    line-height: 23px;
    word-break: break-all;
    user-select: none;

    &:hover {
        cursor: pointer;
    }

    &.checked {
        border-color: var(--primary-color);
        color: var(--primary-color);

        .icon {
            display: block;
        }
    }

    &.drap {
        padding-left: 8px;
        &:hover {
            cursor: grab;
        }
    }

    .option-content {
        display: flex;
        align-items: center;
    }

    .icon {
        display: none;
        position: absolute;
        right: -7px;
        top: -7px;
        z-index: 2;
        width: 29px;
        height: 29px;
        border-radius: 15px;
        color: #fff;
        padding-left: 9px;
        line-height: 36px;
        font-size: 14px;
        background: var(--primary-color) url('https://img.bosszhipin.com/static/file/2024/ibkyr3q0481729493439324.png.webp') no-repeat;
        background-size: auto 12px;
        background-position: 7px 10px;

        &.has-icon {
            background-image: none;
            line-height: 32px;
        }
    }
}
</style>
