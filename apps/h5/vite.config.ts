/* eslint-disable no-console */
import type { ConfigEnv } from 'vite';
import path from 'node:path';
import inject from '@rollup/plugin-inject';
import legacy from '@vitejs/plugin-legacy';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import SetSourceMapFiletToPathPlugin from 'sourcemap-set-path-plugin';
import { VantResolver } from 'unplugin-vue-components/resolvers';
import Components from 'unplugin-vue-components/vite';
import { defineConfig } from 'vite';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import { BASE_URL } from './src/constant/app';
import autoprefixer from 'autoprefixer';
import autoImport from 'unplugin-auto-import/vite'; // 自动导入组件

// 把指定目录下的souceMap文件提取到目标目录
const sourceDir = path.resolve(__dirname, './dist/static/js/');
const targetDir = path.resolve(__dirname, './dist/static/map/');

const deployVersion = process.env.VITE_DEPLOY_VERSION;

// https://vitejs.dev/config/
export default ({ command, mode }: ConfigEnv) => {
    console.log('DEPLOY_ENV:', mode);
    console.log('DEPLOY_VERSION:', deployVersion);

    return defineConfig({
        // 项目根目录
        root: process.cwd(),
        // 项目部署的基础路径
        base: BASE_URL,
        // 静态目录
        publicDir: 'public',
        // 解决一些问题
        resolve: {
            // 配置别名
            alias: {
                '@': path.resolve(__dirname, './src'),
                '~@': path.resolve(__dirname, './src'),
            },
            // 导入时想要省略的扩展名列表
            // vant/es 必须加入 .mjs
            extensions: ['.mjs', '.js', 'jsx', '.ts', '.tsx', '.vue', '.json'],
        },
        define: {
            DEPLOY_VERSION: deployVersion,
        },
        css: {
            // 预处理器配置项
            preprocessorOptions: {
                less: {
                    math: 'always',
                    additionalData: `@import "@/styles/base/index.less";@import "@/styles/base/biz.less";`,
                    globalVars: {
                        'primary-color': '#009999',
                    },
                },
            },
            postcss: {
                plugins: [
                    // 自动添加浏览器前缀

                    autoprefixer(),
                ],
            },
        },
        server: {
            // 服务器主机名
            host: true,
            open: 'http://localhost:8899?pid=f46c19a3e1e1b0af1nB62w~~',
            // 使用 Typescript hmr 要主动设置为 true 才会热更新
            hmr: true,
            // 端口号
            port: 8899,
            proxy: {
                '/mapi': {
                    target: 'https://kaoshi-qa.weizhipin.com',
                    changeOrigin: true,
                    secure: true,
                    // rewrite: (path: string) => path.replace(/^\/wapi/, ""),
                },
                '/wapi': {
                    target: 'https://kaoshi-qa.weizhipin.com/',
                    // target: 'https://kaoshi-rd.weizhipin.com/',
                    // target: 'https://pre-kaoshi.zhipin.com',
                    changeOrigin: true,
                    secure: true,
                    // rewrite: (path: string) => path.replace(/^\/wapi/, ""),
                },
            },
        },
        // 构建
        build: {
            assetsDir: 'static',
            outDir: 'dist',
            sourcemap: true,
            rollupOptions: {
                // 拆分打包
                output: {
                    format: 'esm', // 设置为ESM格式
                    chunkFileNames: 'static/js/[name]-[hash].js',
                    entryFileNames: 'static/js/[name]-[hash].js',
                    assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
                },
                onwarn(warning, warn) {
                    if (warning.code === 'EVAL' && warning.id?.includes('node_modules')) {
                        return;
                    }
                    warn(warning);
                },
            },
            minify: 'terser',
            terserOptions: {
                compress: {
                    drop_console: true, // 移除console
                    drop_debugger: true, // 移除debugger
                },
            },
        },
        plugins: [
            vue(),
            Components({
                resolvers: [VantResolver()],
            }),
            vueJsx(),
            inject({
                Invoke: path.resolve(process.cwd(), './src/services/invoke.ts'),
                postLog: path.resolve(process.cwd(), 'src/utils/post-log.ts'),
            }),
            legacy({
                targets: ['ie >= 11'],
                additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
            }),
            createSvgIconsPlugin({
                // 指定需要缓存的图标文件夹
                iconDirs: [path.resolve(process.cwd(), './src/assets/svg-sprite')],
                // 指定symbolId格式
                symbolId: 'icon-[dir]-[name]',
            }),
            autoImport({
                imports: ['vue', 'vue-router', 'pinia'],
                dirs: ['src/components', 'src/utils', 'src/hooks'],
                eslintrc: { enabled: true, filepath: './.eslintrc-auto-import.json', globalsPropValue: 'readonly' },
            }),
            {
                ...SetSourceMapFiletToPathPlugin({ sourceDir, targetDir }),
                enforce: 'post',
                apply: 'build',
            },
        ],
    });
};
