<template>
    <div class="setting-inner">
        <div class="setting-inner-title">屏幕共享设置</div>
        <div class="setting-inner-desc">本场考试将启用电脑屏幕监控，请通过共享桌面来共享您的考试界面，未按要求共享屏幕可能影响您的考试成绩</div>
        <div class="setting-inner-mini">
            <div class="screen-pannel">
                <div class="guid-box">
                    <div class="title">
                        <SvgIcon name="icon-question-circle-fill" width="16" height="16" />
                        <h3>屏幕共享指南</h3>
                    </div>
                    <p class="desc">点击右侧【共享屏幕】按钮，在浏览器弹出的对话框中选择<i>「整个屏幕」</i>，并点击右下角的【分享】按钮，即可成功共享</p>
                    <b-image width="216" :src="demoImgUrl" />
                </div>
                <div v-if="shareStatus === 0" class="share-box-default">
                    <img src="./images/img-share.png" alt="" width="72" />
                    <p class="intro">共享后的画面将在此处展示</p>
                    <b-button type="outline" status="primary" shape="round" @click.prevent="handleShare"> 共享屏幕 </b-button>
                </div>
                <div v-else-if="shareStatus === 1" class="share-box-error">
                    <img src="./images/img-share-error.png" alt="" width="72" />
                    <p class="result">共享失败</p>
                    <p class="intro">
                        {{ shareErrorText }}
                    </p>
                    <b-button type="outline" status="primary" shape="round" @click.prevent="handleShare"> 重新共享屏幕 </b-button>
                </div>
                <div v-else-if="shareStatus === 2" id="localStream" ref="localStreamRef" class="share-box-success"></div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { usePrepareCheckSave } from '@/hooks/usePrepareCheckSave';
import { useMonitorStore } from '@/store/use-monitor-store';
import { computed, nextTick, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import demoImgUrl from './images/img-example.png';

defineProps({
    stepStatus: {
        type: String,
        default: 'wait',
    },
});

const emit = defineEmits(['changeStepClickable', 'update:stepStatus']);

const $monitorStore = useMonitorStore();
const $route = useRoute();

const shareStatus = computed(() => $monitorStore.STATUS.screen.status); // 0 未开启、1 失败、2 成功
const shareErrorText = computed(() => $monitorStore.STATUS.screen.errorText);
const localStream = computed(() => $monitorStore.STATUS.screen.localStream);

const localStreamRef = ref();

BossAnalyticsTrack('zhice-pc-exam-enter-screen-share', {
    pData: {
        nameZh: '进入屏幕共享',
    },
});

watch(
    () => shareStatus.value,
    (status: any) => {
        if (localStream.value && status === 2) {
            nextTick(() => {
                setTimeout(() => {
                    if (localStreamRef.value) {
                        localStream.value?.play('localStream', { objectFit: 'contain' });
                    }
                }, 400);
            });
        }
    },
    {
        immediate: true,
    },
);

watch(
    () => shareStatus.value,
    (val: number) => {
        usePrepareCheckSave({
            encryptExamId: ($route.query.examId || $route.query.seqId) as string,
            checkItem: 5, // 1 浏览器 2 网速 3麦克风 4 电脑主摄像头 5 电脑屏幕 6手机第二视角
            checkStatus: val === 2 ? 1 : 2, // //状态 1成功 2失败
            checkRemark: val === 2 ? '' : shareErrorText.value,
        });
        // 修改step × or ✓
        emit('update:stepStatus', val === 2 ? 'finish' : 'danger');
    },
);

if (shareStatus.value === 0) {
    emit('update:stepStatus', 'danger');
}

async function handleShare() {
    $monitorStore.openScreenMonitor(mode.value === 'PREPARE_MODE');
    if (shareStatus.value === 0) {
        BossAnalyticsTrack('zhice-pc-exam-share', {
            pData: {
                nameZh: '考前-首次共享屏幕',
            },
        });
    } else if (shareStatus.value === 1) {
        BossAnalyticsTrack('zhice-pc-exam-share-again', {
            pData: {
                nameZh: '考前-重试共享屏幕',
            },
        });
    }
}

const mode = computed(() => $monitorStore.ExamMode); // PREPARE_MODE : EXAM_MODE
const nextClickable = computed(() => {
    if (mode.value === 'EXAM_MODE') {
        return shareStatus.value === 2;
    }
    return true;
});

watch(
    () => nextClickable.value,
    () => {
        emit('changeStepClickable', nextClickable.value);
    },
    { immediate: true },
);

function getNextCheckResult(cb: (str?: string) => void) {
    if (!nextClickable.value) {
        return false;
    }

    if (mode.value === 'EXAM_MODE') {
        return true;
    }
    if (mode.value === 'PREPARE_MODE') {
        if (shareStatus.value !== 2) {
            cb('您尚未成功共享屏幕，正式考试时共享失败可能影响您的考试结果，确定要先进入下一步吗？');
            return false;
        }
        return true;
    }
}

defineExpose({ getNextCheckResult });
</script>

<style lang="less">
@import './index.less';
</style>
