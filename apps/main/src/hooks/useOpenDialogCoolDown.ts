import { ref } from 'vue';

export function useOpenDialogCoolDown() {
    const visible = ref(false);

    let lastTimestamp = 0;

    function openDialogWithCoolDown() {
        const now = Date.now();
        if (now - lastTimestamp > 5 * 60 * 1000) {
            visible.value = true;
            lastTimestamp = now;
        }
    }
    return {
        visible,
        openDialogWithCoolDown,
    };
}
